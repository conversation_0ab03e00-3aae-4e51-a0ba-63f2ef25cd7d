package com.cmcc.cmdevops.ci.com.cmcc.zzz.util.util;

import org.jasypt.intf.cli.JasyptPBEStringEncryptionCLI;

import java.util.ArrayList;
import java.util.List;

public class PropertiesPasswordEncoder {
    public static void encrypt(String password, String salt) {
        List<String> args = new ArrayList<>(3);
        args.add("input=" + password);
        args.add("password=" + salt);
        args.add("algorithm=PBEWithMD5AndTripleDES");
        JasyptPBEStringEncryptionCLI.main(args.toArray(new String[3]));
    }
    public static void main(String[] args) {
        encrypt("PbF1GLq9Tycxm0Mj6Eta", "HD&DCJJDGDK");
    }
}
