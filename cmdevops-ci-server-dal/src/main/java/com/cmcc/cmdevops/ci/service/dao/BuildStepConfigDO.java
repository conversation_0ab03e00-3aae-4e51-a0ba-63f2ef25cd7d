package com.cmcc.cmdevops.ci.service.dao;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> @since 2025-05-22
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("build_step_config")
public class BuildStepConfigDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("task_id")
    private String taskId;

    @TableField("name")
    private String name;

    @TableField("type")
    private String type;

    @TableField("serial")
    private Integer serial;

    @TableField("config")
    private String config;

    @TableField("has_open")
    private Boolean hasOpen;

    @TableField("space_id")
    private String spaceId;

    @TableField("tenant_id")
    private String tenantId;

    @TableField("deleted")
    @TableLogic
    private Boolean deleted;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField("create_uid")
    private String createUid;

    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    @TableField("update_uid")
    private String updateUid;

    @TableField("delete_time")
    private LocalDateTime deleteTime;

    @TableField("delete_uid")
    private String deleteUid;
}
