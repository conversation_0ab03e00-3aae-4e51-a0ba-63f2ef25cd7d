package com.cmcc.cmdevops.ci.service.atom.impl;

import com.cmcc.cmdevops.ci.service.dao.BuildJenkinsNodeDO;
import com.cmcc.cmdevops.ci.service.dao.mapper.BuildJenkinsNodeMapper;
import com.cmcc.cmdevops.ci.service.atom.BuildJenkinsNodeAtomService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> @since 2025-05-22
 */
@Service
public class BuildJenkinsNodeAtomServiceImpl extends ServiceImpl<BuildJenkinsNodeMapper, BuildJenkinsNodeDO> implements BuildJenkinsNodeAtomService {

}
