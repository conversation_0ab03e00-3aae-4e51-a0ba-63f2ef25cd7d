package com.cmcc.cmdevops.ci.service.dao;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> @since 2025-05-28
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("build_artifact")
public class BuildArtifactDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("task_id")
    private String taskId;

    @TableField("build_snapshot_id")
    private String buildSnapshotId;

    @TableField("build_number")
    private String buildNumber;

    @TableField("build_version")
    private String buildVersion;

    @TableField("build_artifact_name")
    private String buildArtifactName;

    @TableField("build_artifact_real_name")
    private String buildArtifactRealName;

    @TableField("build_artifact_path")
    private String buildArtifactPath;

    @TableField("build_artifact_version")
    private String buildArtifactVersion;

    @TableField("build_artifact_url")
    private String buildArtifactUrl;

    @TableField("build_artifact_type")
    private String buildArtifactType;

    @TableField("repository_name")
    private String repositoryName;

    @TableField("space_id")
    private String spaceId;

    @TableField("tenant_id")
    private String tenantId;

    @TableField("deleted")
    @TableLogic
    private Boolean deleted;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField("create_uid")
    private String createUid;

    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    @TableField("update_uid")
    private String updateUid;

    @TableField("delete_time")
    private LocalDateTime deleteTime;

    @TableField("delete_uid")
    private String deleteUid;
}
