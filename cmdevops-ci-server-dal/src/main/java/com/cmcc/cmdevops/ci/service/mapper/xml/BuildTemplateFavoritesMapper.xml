<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmcc.cmdevops.ci.service.dao.mapper.BuildTemplateFavoritesMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.cmcc.cmdevops.ci.service.dao.BuildTemplateFavoritesDO">
    <id column="id" property="id" />
    <result column="user_id" property="userId" />
    <result column="template_id" property="templateId" />
    <result column="space_id" property="spaceId" />
    <result column="tenant_id" property="tenantId" />
    <result column="deleted" property="deleted" />
    <result column="create_time" property="createTime" />
    <result column="update_time" property="updateTime" />
  </resultMap>

  <!-- 通用查询结果列 -->
  <sql id="Base_Column_List">
    id, user_id, template_id, space_id, tenant_id, deleted, create_time, update_time
  </sql>

</mapper>
