package com.cmcc.cmdevops.ci.service.dao;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 构建模板数据对象
 * </p>
 *
 * <AUTHOR> @since 2025-05-21
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("build_template")
@ToString
public class BuildTemplateDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 模板标题
     */
    @TableField("title")
    private String title;

    /**
     * 模板描述
     */
    @TableField("template_describe")
    private String templateDescribe;

    /**
     * 模板图标
     */
    @TableField("icon")
    private String icon;

    /**
     * 构建配置(JSON格式)
     */
    @TableField("build_config")
    private String buildConfig;

    /**
     * 模板类型(0-系统模板, 1-自定义模板)
     */
    @TableField("template_type")
    private Integer templateType;

    /**
     * 工作空间ID
     */
    @TableField("space_id")
    private String spaceId;

    /**
     * 模板分组
     */
    @TableField("template_group")
    private String templateGroup;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 是否删除
     */
    @TableField("deleted")
    @TableLogic
    private Boolean deleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人ID
     */
    @TableField("create_uid")
    private String createUid;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人ID
     */
    @TableField("update_uid")
    private String updateUid;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private LocalDateTime deleteTime;

    /**
     * 删除人ID
     */
    @TableField("delete_uid")
    private String deleteUid;
}
