package com.cmcc.cmdevops.ci.service.atom.impl;

import com.cmcc.cmdevops.ci.service.dao.BuildArtifactDO;
import com.cmcc.cmdevops.ci.service.dao.mapper.BuildArtifactMapper;
import com.cmcc.cmdevops.ci.service.atom.BuildArtifactAtomService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> @since 2025-05-28
 */
@Service
public class BuildArtifactAtomServiceImpl extends ServiceImpl<BuildArtifactMapper, BuildArtifactDO> implements BuildArtifactAtomService {

}
