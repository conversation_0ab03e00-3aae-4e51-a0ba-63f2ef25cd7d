package com.cmcc.cmdevops.ci.service.atom.impl;

import com.cmcc.cmdevops.ci.service.dao.BuildCodeConfigDO;
import com.cmcc.cmdevops.ci.service.dao.mapper.BuildCodeConfigMapper;
import com.cmcc.cmdevops.ci.service.atom.BuildCodeConfigAtomService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> @since 2025-05-22
 */
@Service
public class BuildCodeConfigAtomServiceImpl extends ServiceImpl<BuildCodeConfigMapper, BuildCodeConfigDO> implements BuildCodeConfigAtomService {

}
