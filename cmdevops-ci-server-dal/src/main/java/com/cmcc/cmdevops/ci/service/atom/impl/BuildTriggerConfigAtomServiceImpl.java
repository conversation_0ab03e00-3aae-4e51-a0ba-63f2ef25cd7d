package com.cmcc.cmdevops.ci.service.atom.impl;

import com.cmcc.cmdevops.ci.service.dao.BuildTriggerConfigDO;
import com.cmcc.cmdevops.ci.service.dao.mapper.BuildTriggerConfigMapper;
import com.cmcc.cmdevops.ci.service.atom.BuildTriggerConfigAtomService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> @since 2025-05-22
 */
@Service
public class BuildTriggerConfigAtomServiceImpl extends ServiceImpl<BuildTriggerConfigMapper, BuildTriggerConfigDO> implements BuildTriggerConfigAtomService {

}
