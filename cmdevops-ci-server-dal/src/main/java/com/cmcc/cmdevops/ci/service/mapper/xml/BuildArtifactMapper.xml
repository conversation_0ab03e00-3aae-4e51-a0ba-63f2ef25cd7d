<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmcc.cmdevops.ci.service.dao.mapper.BuildArtifactMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cmcc.cmdevops.ci.service.dao.BuildArtifactDO">
        <id column="id" property="id" />
        <result column="task_id" property="taskId" />
        <result column="build_snapshot_id" property="buildSnapshotId" />
        <result column="build_number" property="buildNumber" />
        <result column="build_artifact_name" property="buildArtifactName" />
        <result column="build_artifact_real_name" property="buildArtifactRealName" />
        <result column="build_artifact_path" property="buildArtifactPath" />
        <result column="build_artifact_version" property="buildArtifactVersion" />
        <result column="build_artifact_url" property="buildArtifactUrl" />
        <result column="build_artifact_type" property="buildArtifactType" />
        <result column="repository_name" property="repositoryName" />
        <result column="space_id" property="spaceId" />
        <result column="tenant_id" property="tenantId" />
        <result column="deleted" property="deleted" />
        <result column="create_time" property="createTime" />
        <result column="create_uid" property="createUid" />
        <result column="update_time" property="updateTime" />
        <result column="update_uid" property="updateUid" />
        <result column="delete_time" property="deleteTime" />
        <result column="delete_uid" property="deleteUid" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, task_id, build_snapshot_id, build_number, build_artifact_name, build_artifact_real_name, build_artifact_path, build_artifact_version, build_artifact_url, build_artifact_type, repository_name, space_id, tenant_id, deleted, create_time, create_uid, update_time, update_uid, delete_time, delete_uid
    </sql>

</mapper>
