<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmcc.cmdevops.ci.service.dao.mapper.BuildEnvironmentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cmcc.cmdevops.ci.service.dao.BuildEnvironmentDO">
        <id column="id" property="id" />
        <result column="environment_name" property="environmentName" />
        <result column="environment_type" property="environmentType" />
        <result column="environment_desc" property="environmentDesc" />
        <result column="region" property="region" />
        <result column="environment_url" property="environmentUrl" />
        <result column="environment_config" property="environmentConfig" />
        <result column="is_use_cache" property="isUseCache" />
        <result column="cache_dir" property="cacheDir" />
        <result column="access_key" property="accessKey" />
        <result column="secret_key" property="secretKey" />
        <result column="space_id" property="spaceId" />
        <result column="tenant_id" property="tenantId" />
        <result column="deleted" property="deleted" />
        <result column="create_time" property="createTime" />
        <result column="create_uid" property="createUid" />
        <result column="update_time" property="updateTime" />
        <result column="update_uid" property="updateUid" />
        <result column="delete_time" property="deleteTime" />
        <result column="delete_uid" property="deleteUid" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, environment_name, environment_type, environment_desc, region, environment_url, environment_config, is_use_cache, cache_dir, access_key, secret_key, space_id, tenant_id, deleted, create_time, create_uid, update_time, update_uid, delete_time, delete_uid
    </sql>

</mapper>
