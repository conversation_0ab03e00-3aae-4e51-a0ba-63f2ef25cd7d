package com.cmcc.cmdevops.ci.service.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> @since 2025-05-22
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("build_dict")
public class BuildDictDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("dict_name")
    private String dictName;

    @TableField("dict_key")
    private String dictKey;

    @TableField("dict_value")
    private String dictValue;
}
