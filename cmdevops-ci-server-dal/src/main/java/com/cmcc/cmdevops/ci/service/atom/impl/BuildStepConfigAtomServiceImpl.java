package com.cmcc.cmdevops.ci.service.atom.impl;

import com.cmcc.cmdevops.ci.service.dao.BuildStepConfigDO;
import com.cmcc.cmdevops.ci.service.dao.mapper.BuildStepConfigMapper;
import com.cmcc.cmdevops.ci.service.atom.BuildStepConfigAtomService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> @since 2025-05-22
 */
@Service
public class BuildStepConfigAtomServiceImpl extends ServiceImpl<BuildStepConfigMapper, BuildStepConfigDO> implements BuildStepConfigAtomService {

}
