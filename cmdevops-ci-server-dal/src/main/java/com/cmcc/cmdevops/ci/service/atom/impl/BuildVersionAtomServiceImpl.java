package com.cmcc.cmdevops.ci.service.atom.impl;

import com.cmcc.cmdevops.ci.service.dao.BuildVersionDO;
import com.cmcc.cmdevops.ci.service.dao.mapper.BuildVersionMapper;
import com.cmcc.cmdevops.ci.service.atom.BuildVersionAtomService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> @since 2025-05-22
 */
@Service
public class BuildVersionAtomServiceImpl extends ServiceImpl<BuildVersionMapper, BuildVersionDO> implements BuildVersionAtomService {

}
