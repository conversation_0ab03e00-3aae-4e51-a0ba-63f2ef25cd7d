package com.cmcc.cmdevops.ci.service.atom.impl;

import com.cmcc.cmdevops.ci.service.dao.BuildCodeSnapshotDO;
import com.cmcc.cmdevops.ci.service.dao.mapper.BuildCodeSnapshotMapper;
import com.cmcc.cmdevops.ci.service.atom.BuildCodeSnapshotAtomService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> @since 2025-05-22
 */
@Service
public class BuildCodeSnapshotAtomServiceImpl extends ServiceImpl<BuildCodeSnapshotMapper, BuildCodeSnapshotDO> implements BuildCodeSnapshotAtomService {

}
