package com.cmcc.cmdevops.ci.service.dao;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> @since 2025-07-24
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("build_permission")
public class BuildPermissionDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("task_id")
    private String taskId;

    /**
     * 用户id
     */
    @TableField("user_uid")
    private String userUid;

    /**
     * 用户名
     */
    @TableField("user_name")
    private String userName;

    /**
     * 权限码
     */
    @TableField("permission_code")
    private String permissionCode;

    @TableField("space_id")
    private String spaceId;

    @TableField("tenant_id")
    private String tenantId;

    @TableField("deleted")
    @TableLogic
    private Boolean deleted;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField("create_uid")
    private String createUid;

    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    @TableField("update_uid")
    private String updateUid;

    @TableField("delete_time")
    private LocalDateTime deleteTime;

    @TableField("delete_uid")
    private String deleteUid;
}
