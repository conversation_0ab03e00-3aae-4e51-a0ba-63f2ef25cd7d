package com.cmcc.cmdevops.ci.service.atom.impl;

import com.cmcc.cmdevops.ci.service.dao.BuildProjectRelationDO;
import com.cmcc.cmdevops.ci.service.dao.mapper.BuildProjectRelationMapper;
import com.cmcc.cmdevops.ci.service.atom.BuildProjectRelationAtomService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> @since 2025-07-25
 */
@Service
public class BuildProjectRelationAtomServiceImpl extends ServiceImpl<BuildProjectRelationMapper, BuildProjectRelationDO> implements BuildProjectRelationAtomService {

}
