package com.cmcc.cmdevops.ci.service.atom.impl;

import com.cmcc.cmdevops.ci.service.dao.BuildParamSnapshotDO;
import com.cmcc.cmdevops.ci.service.dao.mapper.BuildParamSnapshotMapper;
import com.cmcc.cmdevops.ci.service.atom.BuildParamSnapshotAtomService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> @since 2025-05-22
 */
@Service
public class BuildParamSnapshotAtomServiceImpl extends ServiceImpl<BuildParamSnapshotMapper, BuildParamSnapshotDO> implements BuildParamSnapshotAtomService {

}
