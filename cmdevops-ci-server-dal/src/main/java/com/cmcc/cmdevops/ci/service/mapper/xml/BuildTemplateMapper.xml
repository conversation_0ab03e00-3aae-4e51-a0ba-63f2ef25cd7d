<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmcc.cmdevops.ci.service.dao.mapper.BuildTemplateMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.cmcc.cmdevops.ci.service.dao.BuildTemplateDO">
    <id column="id" property="id" />
    <result column="title" property="title" />
    <result column="template_describe" property="templateDescribe" />
    <result column="icon" property="icon" />
    <result column="build_config" property="buildConfig" />
    <result column="template_type" property="templateType" />
    <result column="space_id" property="spaceId" />
    <result column="template_group" property="templateGroup" />
    <result column="tenant_id" property="tenantId" />
    <result column="deleted" property="deleted" />
    <result column="create_time" property="createTime" />
    <result column="create_uid" property="createUid" />
    <result column="update_time" property="updateTime" />
    <result column="update_uid" property="updateUid" />
    <result column="delete_time" property="deleteTime" />
    <result column="delete_uid" property="deleteUid" />
  </resultMap>

  <!-- 通用查询结果列 -->
  <sql id="Base_Column_List">
    id, title, template_describe, icon, build_config, template_type, space_id, template_group, tenant_id, deleted, create_time, create_uid, update_time, update_uid, delete_time, delete_uid
  </sql>

</mapper>
