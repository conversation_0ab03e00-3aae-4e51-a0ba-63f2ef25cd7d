package com.cmcc.cmdevops.ci.service.dao;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> @since 2025-07-31
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("build_project_snapshot")
public class BuildProjectSnapshotDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("task_id")
    private String taskId;

    @TableField("build_snapshot_id")
    private String buildSnapshotId;

    @TableField("project_code")
    private String projectCode;

    @TableField("tenant_id")
    private String tenantId;

    @TableField("deleted")
    @TableLogic
    private Boolean deleted;

    @TableField("create_uid")
    private String createUid;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField("update_uid")
    private String updateUid;

    @TableField("update_time")
    private LocalDateTime updateTime;

    @TableField("delete_uid")
    private String deleteUid;

    @TableField("delete_time")
    private LocalDateTime deleteTime;
}
