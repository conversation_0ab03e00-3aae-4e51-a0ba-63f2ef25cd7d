package com.cmcc.cmdevops.ci.service.atom.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cmcc.cmdevops.ci.service.atom.BuildDictAtomService;
import com.cmcc.cmdevops.ci.service.dao.BuildDictDO;
import com.cmcc.cmdevops.ci.service.dao.mapper.BuildDictMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> @since 2025-05-22
 */
@Service
public class BuildDictAtomServiceImpl extends ServiceImpl<BuildDictMapper, BuildDictDO> implements BuildDictAtomService {

}
