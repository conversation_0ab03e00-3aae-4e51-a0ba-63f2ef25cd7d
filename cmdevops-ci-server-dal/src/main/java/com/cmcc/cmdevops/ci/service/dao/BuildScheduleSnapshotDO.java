package com.cmcc.cmdevops.ci.service.dao;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 构建启动快照表
 * </p>
 *
 * <AUTHOR> @since 2025-05-24
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("build_schedule_snapshot")
public class BuildScheduleSnapshotDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private String id;

    @TableField("ci_schedule_url")
    private String ciScheduleUrl;

    @TableField("ci_tool_url")
    private String ciToolUrl;

    @TableField("job_xml")
    private String jobXml;

    @TableField("user_name")
    private String userName;

    @TableField("token")
    private String token;

    @TableField("params")
    private String params;

    @TableField("docker_credentials")
    private String dockerCredentials;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}
