package com.cmcc.cmdevops.ci.service.atom.impl;

import com.cmcc.cmdevops.ci.service.dao.BuildPermissionDO;
import com.cmcc.cmdevops.ci.service.dao.mapper.BuildPermissionMapper;
import com.cmcc.cmdevops.ci.service.atom.BuildPermissionAtomService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> @since 2025-07-24
 */
@Service
public class BuildPermissionAtomServiceImpl extends ServiceImpl<BuildPermissionMapper, BuildPermissionDO> implements BuildPermissionAtomService {

}
