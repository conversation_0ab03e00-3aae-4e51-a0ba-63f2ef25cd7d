package com.cmcc.cmdevops.ci.service.dao;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> @since 2025-05-22
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("build_trigger_config")
public class BuildTriggerConfigDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("task_id")
    private String taskId;

    @TableField("code_trigger_switch")
    private Boolean codeTriggerSwitch;

    @TableField("code_trigger_event")
    private String codeTriggerEvent;

    @TableField("code_trigger_filter")
    private String codeTriggerFilter;

    @TableField("time_trigger_switch")
    private Boolean timeTriggerSwitch;

    @TableField("time_trigger_crontab")
    private String timeTriggerCrontab;

    @TableField("time_trigger_code_change_time_trigger_switch")
    private Boolean timeTriggerCodeChangeTimeTriggerSwitch;

    @TableField("space_id")
    private String spaceId;

    @TableField("tenant_id")
    private String tenantId;

    @TableField("deleted")
    @TableLogic
    private Boolean deleted;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField("create_uid")
    private String createUid;

    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    @TableField("update_uid")
    private String updateUid;

    @TableField("delete_time")
    private LocalDateTime deleteTime;

    @TableField("delete_uid")
    private String deleteUid;

    @TableField("time_trigger_type")
    private String timeTriggerType;

    @TableField("job_exe_time")
    private LocalDateTime jobExeTime;
}
