package com.cmcc.cmdevops.ci.service.dao;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> @since 2025-05-29
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("build_group")
public class BuildGroupDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID) // 使用雪花算法生成ID
    private Integer id;

    @TableField("group_name")
    private String groupName;

    @TableField("group_describe")
    private String groupDescribe;

    @TableField("group_sign")
    private String groupSign;

    @TableField("space_id")
    private String spaceId;

    @TableField("tenant_id")
    private String tenantId;

    @TableField("deleted")
    @TableLogic
    private Boolean deleted;

    @TableField("create_uid")
    private String createUid;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField("update_uid")
    private String updateUid;

    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    @TableField("delete_uid")
    private String deleteUid;

    @TableField("delete_time")
    private LocalDateTime deleteTime;
}
