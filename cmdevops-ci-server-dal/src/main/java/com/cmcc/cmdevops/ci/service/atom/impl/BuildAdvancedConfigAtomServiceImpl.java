package com.cmcc.cmdevops.ci.service.atom.impl;

import com.cmcc.cmdevops.ci.service.dao.BuildAdvancedConfigDO;
import com.cmcc.cmdevops.ci.service.dao.mapper.BuildAdvancedConfigMapper;
import com.cmcc.cmdevops.ci.service.atom.BuildAdvancedConfigAtomService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> @since 2025-05-22
 */
@Service
public class BuildAdvancedConfigAtomServiceImpl extends ServiceImpl<BuildAdvancedConfigMapper, BuildAdvancedConfigDO> implements BuildAdvancedConfigAtomService {

}
