<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmcc.cmdevops.ci.service.dao.mapper.BuildTriggerConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cmcc.cmdevops.ci.service.dao.BuildTriggerConfigDO">
        <id column="id" property="id" />
        <result column="task_id" property="taskId" />
        <result column="code_trigger_switch" property="codeTriggerSwitch" />
        <result column="code_trigger_event" property="codeTriggerEvent" />
        <result column="code_trigger_filter" property="codeTriggerFilter" />
        <result column="time_trigger_switch" property="timeTriggerSwitch" />
        <result column="time_trigger_crontab" property="timeTriggerCrontab" />
        <result column="time_trigger_code_change_time_trigger_switch" property="timeTriggerCodeChangeTimeTriggerSwitch" />
        <result column="space_id" property="spaceId" />
        <result column="tenant_id" property="tenantId" />
        <result column="deleted" property="deleted" />
        <result column="create_time" property="createTime" />
        <result column="create_uid" property="createUid" />
        <result column="update_time" property="updateTime" />
        <result column="update_uid" property="updateUid" />
        <result column="delete_time" property="deleteTime" />
        <result column="delete_uid" property="deleteUid" />
        <result column="time_trigger_type" property="timeTriggerType" />
        <result column="job_exe_time" property="jobExeTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, task_id, code_trigger_switch, code_trigger_event, code_trigger_filter, time_trigger_switch, time_trigger_crontab, time_trigger_code_change_time_trigger_switch, space_id, tenant_id, deleted, create_time, create_uid, update_time, update_uid, delete_time, delete_uid, time_trigger_type, job_exe_time
    </sql>

</mapper>
