package com.cmcc.cmdevops.ci.service.atom.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cmcc.cmdevops.ci.service.atom.BuildTemplateBizAtomService;
import com.cmcc.cmdevops.ci.service.dao.BuildTemplateDO;
import com.cmcc.cmdevops.ci.service.dao.mapper.BuildTemplateMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> @since 2025-06-03
 */
@Service
public class BuildTemplateBizAtomServiceImpl extends ServiceImpl<BuildTemplateMapper, BuildTemplateDO> implements BuildTemplateBizAtomService {

}
