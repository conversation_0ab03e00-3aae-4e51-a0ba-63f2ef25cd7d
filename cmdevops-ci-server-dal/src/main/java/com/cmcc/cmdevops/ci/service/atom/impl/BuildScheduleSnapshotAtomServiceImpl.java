package com.cmcc.cmdevops.ci.service.atom.impl;

import com.cmcc.cmdevops.ci.service.dao.BuildScheduleSnapshotDO;
import com.cmcc.cmdevops.ci.service.dao.mapper.BuildScheduleSnapshotMapper;
import com.cmcc.cmdevops.ci.service.atom.BuildScheduleSnapshotAtomService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 构建启动快照表 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2025-05-24
 */
@Service
public class BuildScheduleSnapshotAtomServiceImpl extends ServiceImpl<BuildScheduleSnapshotMapper, BuildScheduleSnapshotDO> implements BuildScheduleSnapshotAtomService {

}
