package com.cmcc.cmdevops.ci.service.dao;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> @since 2025-05-22
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("build_tool")
public class BuildToolDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("id")
    private String id;

    @TableField("tool_name")
    private String toolName;

    @TableField("tool_image")
    private String toolImage;

    @TableField("args")
    private String args;

    @TableField("type")
    private String type;
}
