package com.cmcc.cmdevops.ci.service.atom.impl;

import com.cmcc.cmdevops.ci.service.dao.BuildConfigFileDO;
import com.cmcc.cmdevops.ci.service.dao.mapper.BuildConfigFileMapper;
import com.cmcc.cmdevops.ci.service.atom.BuildConfigFileAtomService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> @since 2025-07-16
 */
@Service
public class BuildConfigFileAtomServiceImpl extends ServiceImpl<BuildConfigFileMapper, BuildConfigFileDO> implements BuildConfigFileAtomService {

}
