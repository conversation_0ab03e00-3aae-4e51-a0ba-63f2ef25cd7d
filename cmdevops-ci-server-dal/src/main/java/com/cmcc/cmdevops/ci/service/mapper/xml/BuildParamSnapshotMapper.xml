<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmcc.cmdevops.ci.service.dao.mapper.BuildParamSnapshotMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cmcc.cmdevops.ci.service.dao.BuildParamSnapshotDO">
        <id column="id" property="id" />
        <result column="task_id" property="taskId" />
        <result column="param_name" property="paramName" />
        <result column="param_type" property="paramType" />
        <result column="param_value" property="paramValue" />
        <result column="param_value_type" property="paramValueType" />
        <result column="param_options" property="paramOptions" />
        <result column="param_discrib" property="paramDiscrib" />
        <result column="is_open" property="isOpen" />
        <result column="is_running" property="isRunning" />
        <result column="is_encrypt" property="isEncrypt" />
        <result column="space_id" property="spaceId" />
        <result column="tenant_id" property="tenantId" />
        <result column="deleted" property="deleted" />
        <result column="create_time" property="createTime" />
        <result column="create_uid" property="createUid" />
        <result column="update_time" property="updateTime" />
        <result column="update_uid" property="updateUid" />
        <result column="delete_time" property="deleteTime" />
        <result column="delete_uid" property="deleteUid" />
        <result column="build_snapshot_id" property="buildSnapshotId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, task_id, param_name, param_type, param_value, param_value_type, param_options, param_discrib, is_open, is_running, is_encrypt, space_id, tenant_id, deleted, create_time, create_uid, update_time, update_uid, delete_time, delete_uid, build_snapshot_id
    </sql>

</mapper>
