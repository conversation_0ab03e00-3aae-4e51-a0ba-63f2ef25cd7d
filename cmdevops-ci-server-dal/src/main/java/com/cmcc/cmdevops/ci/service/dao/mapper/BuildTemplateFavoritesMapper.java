package com.cmcc.cmdevops.ci.service.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cmcc.cmdevops.ci.service.dao.BuildTemplateFavoritesDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * <p>
 * 构建模板收藏 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2025-06-07
 */
@Mapper
public interface BuildTemplateFavoritesMapper extends BaseMapper<BuildTemplateFavoritesDO> {

    @Select("SELECT * FROM build_template_favorites WHERE user_id = #{userId} AND template_id = #{templateId} AND space_id = #{spaceId} LIMIT 1")
    BuildTemplateFavoritesDO findByUserIdAndTemplateIdIgnoreDeleted(@Param("userId") String userId, @Param("templateId") Integer templateId , @Param("spaceId") String spaceId);

    @Update("UPDATE build_template_favorites SET deleted=false, space_id=#{spaceId},update_time=now() WHERE id=#{id}")
    boolean updateDeletedRecord(@Param("id") Integer id, @Param("spaceId") String spaceId);

}
