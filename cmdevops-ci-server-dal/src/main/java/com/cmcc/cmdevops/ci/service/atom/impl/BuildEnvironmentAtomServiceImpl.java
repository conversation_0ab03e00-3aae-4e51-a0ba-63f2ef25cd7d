package com.cmcc.cmdevops.ci.service.atom.impl;

import com.cmcc.cmdevops.ci.service.dao.BuildEnvironmentDO;
import com.cmcc.cmdevops.ci.service.dao.mapper.BuildEnvironmentMapper;
import com.cmcc.cmdevops.ci.service.atom.BuildEnvironmentAtomService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> @since 2025-05-22
 */
@Service
public class BuildEnvironmentAtomServiceImpl extends ServiceImpl<BuildEnvironmentMapper, BuildEnvironmentDO> implements BuildEnvironmentAtomService {

}
