package com.cmcc.cmdevops.ci.service.atom.impl;

import com.cmcc.cmdevops.ci.service.dao.BuildParameterDO;
import com.cmcc.cmdevops.ci.service.dao.mapper.BuildParameterMapper;
import com.cmcc.cmdevops.ci.service.atom.BuildParameterAtomService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> @since 2025-05-22
 */
@Service
public class BuildParameterAtomServiceImpl extends ServiceImpl<BuildParameterMapper, BuildParameterDO> implements BuildParameterAtomService {

}
