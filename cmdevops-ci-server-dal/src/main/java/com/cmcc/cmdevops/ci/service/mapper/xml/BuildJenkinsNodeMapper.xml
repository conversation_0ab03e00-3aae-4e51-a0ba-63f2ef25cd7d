<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmcc.cmdevops.ci.service.dao.mapper.BuildJenkinsNodeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cmcc.cmdevops.ci.service.dao.BuildJenkinsNodeDO">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="type" property="type" />
        <result column="node_status" property="nodeStatus" />
        <result column="node_start_time" property="nodeStartTime" />
        <result column="node_url" property="nodeUrl" />
        <result column="build_environment_id" property="buildEnvironmentId" />
        <result column="space_id" property="spaceId" />
        <result column="tenant_id" property="tenantId" />
        <result column="deleted" property="deleted" />
        <result column="create_time" property="createTime" />
        <result column="create_uid" property="createUid" />
        <result column="update_time" property="updateTime" />
        <result column="update_uid" property="updateUid" />
        <result column="delete_time" property="deleteTime" />
        <result column="delete_uid" property="deleteUid" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, type, node_status, node_start_time, node_url, build_environment_id, space_id, tenant_id, deleted, create_time, create_uid, update_time, update_uid, delete_time, delete_uid
    </sql>

</mapper>
