<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmcc.cmdevops.ci.service.dao.mapper.BuildCodeSnapshotMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cmcc.cmdevops.ci.service.dao.BuildCodeSnapshotDO">
        <id column="id" property="id" />
        <result column="task_id" property="taskId" />
        <result column="build_snapshot_id" property="buildSnapshotId" />
        <result column="vcs_name" property="vcsName" />
        <result column="vcs_repository" property="vcsRepository" />
        <result column="vcs_branch" property="vcsBranch" />
        <result column="vcs_clone_type" property="vcsCloneType" />
        <result column="vcs_submodule" property="vcsSubmodule" />
        <result column="commit_message" property="commitMessage" />
        <result column="change_message" property="changeMessage" />
        <result column="space_id" property="spaceId" />
        <result column="tenant_id" property="tenantId" />
        <result column="deleted" property="deleted" />
        <result column="create_time" property="createTime" />
        <result column="create_uid" property="createUid" />
        <result column="update_time" property="updateTime" />
        <result column="update_uid" property="updateUid" />
        <result column="delete_time" property="deleteTime" />
        <result column="delete_uid" property="deleteUid" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, task_id, build_snapshot_id, vcs_name, vcs_repository, vcs_branch, vcs_clone_type, vcs_submodule, commit_message, change_message, space_id, tenant_id, deleted, create_time, create_uid, update_time, update_uid, delete_time, delete_uid
    </sql>

</mapper>
