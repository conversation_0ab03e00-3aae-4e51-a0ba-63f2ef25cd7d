<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmcc.cmdevops.ci.service.dao.mapper.BuildNodeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cmcc.cmdevops.ci.service.dao.BuildNodeDO">
        <id column="id" property="id" />
        <result column="build_environment_id" property="buildEnvironmentId" />
        <result column="build_node_path" property="buildNodePath" />
        <result column="build_node_type" property="buildNodeType" />
        <result column="build_node_name" property="buildNodeName" />
        <result column="build_node_status" property="buildNodeStatus" />
        <result column="space_id" property="spaceId" />
        <result column="tenant_id" property="tenantId" />
        <result column="deleted" property="deleted" />
        <result column="create_time" property="createTime" />
        <result column="create_uid" property="createUid" />
        <result column="update_time" property="updateTime" />
        <result column="update_uid" property="updateUid" />
        <result column="delete_time" property="deleteTime" />
        <result column="delete_uid" property="deleteUid" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, build_environment_id, build_node_path, build_node_type, build_node_name, build_node_status, space_id, tenant_id, deleted, create_time, create_uid, update_time, update_uid, delete_time, delete_uid
    </sql>

</mapper>
