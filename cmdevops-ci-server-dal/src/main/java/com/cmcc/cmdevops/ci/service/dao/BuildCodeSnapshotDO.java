package com.cmcc.cmdevops.ci.service.dao;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> @since 2025-05-22
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("build_code_snapshot")
public class BuildCodeSnapshotDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("task_id")
    private String taskId;

    @TableField("build_snapshot_id")
    private String buildSnapshotId;

    @TableField("vcs_id")
    private String vcsId;

    @TableField("vcs_name")
    private String vcsName;

    @TableField("vcs_repository")
    private String vcsRepository;

    @TableField("vcs_branch")
    private String vcsBranch;

    @TableField("vcs_clone_type")
    private String vcsCloneType;

    @TableField("vcs_submodule")
    private Boolean vcsSubmodule;

    @TableField("commit_id")
    private String commitId;

    @TableField("commit_message")
    private String commitMessage;

    @TableField("change_message")
    private String changeMessage;

    @TableField("space_id")
    private String spaceId;

    @TableField("tenant_id")
    private String tenantId;

    @TableField("deleted")
    @TableLogic
    private Boolean deleted;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField("create_uid")
    private String createUid;

    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    @TableField("update_uid")
    private String updateUid;

    @TableField("delete_time")
    private LocalDateTime deleteTime;

    @TableField("delete_uid")
    private String deleteUid;
}
