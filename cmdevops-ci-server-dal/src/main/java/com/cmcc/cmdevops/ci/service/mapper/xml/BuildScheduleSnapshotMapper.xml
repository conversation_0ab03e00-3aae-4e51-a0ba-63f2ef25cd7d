<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmcc.cmdevops.ci.service.dao.mapper.BuildScheduleSnapshotMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cmcc.cmdevops.ci.service.dao.BuildScheduleSnapshotDO">
        <id column="id" property="id" />
        <result column="ci_schedule_url" property="ciScheduleUrl" />
        <result column="ci_tool_url" property="ciToolUrl" />
        <result column="job_xml" property="jobXml" />
        <result column="user_name" property="userName" />
        <result column="token" property="token" />
        <result column="params" property="params" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, ci_schedule_url, ci_tool_url, job_xml, user_name, token, params, create_time
    </sql>

</mapper>
