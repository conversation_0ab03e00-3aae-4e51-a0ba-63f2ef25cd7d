<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmcc.cmdevops.ci.service.dao.mapper.BuildToolMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cmcc.cmdevops.ci.service.dao.BuildToolDO">
        <id column="id" property="id" />
        <result column="tool_name" property="toolName" />
        <result column="tool_image" property="toolImage" />
        <result column="args" property="args" />
        <result column="type" property="type" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tool_name, tool_image, args, type
    </sql>

</mapper>
