# Uni SpringBoot Template

这是一个基于Spring Boot的多模块Maven项目模板。


- adapter: 适配器层，处理外部请求和响应
- service: 服务层，包含业务逻辑
- dal: 数据访问层(data access layer)，处理数据库访问
- start: 启动模块，包含应用程序入口和配置


- JDK 17
- Spring Boot 3.4.5
- Maven
- Lombok


1. 确保已安装JDK 17和Maven
2. 克隆项目到本地
3. 在项目根目录执行：
   ```bash
   mvn clean install
   ```
4. 运行应用：
   ```bash
   cd start
   mvn spring-boot:run
   ```


主要配置文件位于 `start/src/main/resources/application.yml` 
