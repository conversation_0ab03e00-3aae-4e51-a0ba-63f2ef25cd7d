package com.cmcc.cmdevops.ci.adapter.dto.request;

import com.cmcc.cmdevops.BaseRequest;
import com.cmcc.cmdevops.ci.adapter.dto.BuildTemplateDTO;

public class BuildTemplateRequest extends BaseRequest {

    private BuildTemplateDTO buildTemplateDTO;

    public BuildTemplateDTO getBuildTemplateDTO() {
        return buildTemplateDTO;
    }

    public void setBuildTemplateDTO(BuildTemplateDTO dto) {
        this.buildTemplateDTO = dto;
    }
}
