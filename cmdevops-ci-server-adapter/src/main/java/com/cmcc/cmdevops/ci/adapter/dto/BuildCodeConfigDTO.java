package com.cmcc.cmdevops.ci.adapter.dto;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> @since 2025-05-20
 */
@Getter
@Setter
@Builder
@Accessors(chain = true)
public class BuildCodeConfigDTO implements Serializable {
    public BuildCodeConfigDTO() {
    }

    public BuildCodeConfigDTO(Integer id, String taskId, String vcsId, String vcsName, String vcsRepository, String vcsBranch, String vcsCloneType, Boolean vcsSubmodule, String spaceId, String tenantId, Boolean deleted, LocalDateTime createTime, String createUid, LocalDateTime updateTime, String updateUid, LocalDateTime deleteTime, String deleteUid) {
        this.id = id;
        this.taskId = taskId;
        this.vcsId = vcsId;
        this.vcsName = vcsName;
        this.vcsRepository = vcsRepository;
        this.vcsBranch = vcsBranch;
        this.vcsCloneType = vcsCloneType;
        this.vcsSubmodule = vcsSubmodule;
        this.spaceId = spaceId;
        this.tenantId = tenantId;
        this.deleted = deleted;
        this.createTime = createTime;
        this.createUid = createUid;
        this.updateTime = updateTime;
        this.updateUid = updateUid;
        this.deleteTime = deleteTime;
        this.deleteUid = deleteUid;
    }

    private static final long serialVersionUID = 1L;

    private Integer id;

    private String taskId;

    private String vcsId;

    private String vcsName;

    private String vcsRepository;

    private String vcsBranch;

    private String vcsCloneType;

    private Boolean vcsSubmodule;

    private String spaceId;

    private String tenantId;

    private Boolean deleted;

    private LocalDateTime createTime;

    private String createUid;

    private LocalDateTime updateTime;

    private String updateUid;

    private LocalDateTime deleteTime;

    private String deleteUid;
}
