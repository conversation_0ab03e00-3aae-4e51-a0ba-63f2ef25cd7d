package com.cmcc.cmdevops.ci.adapter.dto;

import com.alibaba.fastjson2.JSONObject;
import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.bo.BuildStepSnapshotBO;
import com.cmcc.cmdevops.util.BeanCloner;

import java.util.Collections;
import java.util.List;

public class BuildStepSnapshotConverter {

    public static BuildTaskDTO convertToDTO(BuildStepSnapshotBO demoProductInfoBO) {
        return JSONObject.parseObject(JSONObject.toJSONString(demoProductInfoBO), BuildTaskDTO.class);
    }

    public static BuildStepSnapshotBO convertToBO(BuildTaskDTO demoProductInfoDTO) {
        return JSONObject.parseObject(JSONObject.toJSONString(demoProductInfoDTO), BuildStepSnapshotBO.class);
    }

    public static PageResponse<List<BuildTaskDTO>> convertToDTO(PageResponse<List<BuildStepSnapshotBO>> pageResponse, PageRequest pageRequest) {
        if (pageResponse == null) {
            return PageResponse.success(Collections.emptyList(), 0L, pageRequest.getPageNo(), pageRequest.getPageSize());
        }
        List<BuildTaskDTO> list = BeanCloner.clone(pageResponse.getData(), BuildTaskDTO.class);
        return PageResponse.success(list, pageResponse.getCount(), pageRequest.getPageNo(), pageRequest.getPageSize());
    }
}
