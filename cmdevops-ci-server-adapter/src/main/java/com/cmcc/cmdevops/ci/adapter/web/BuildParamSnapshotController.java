package com.cmcc.cmdevops.ci.adapter.web;

import com.cmcc.cmdevops.BaseResponse;
import com.cmcc.cmdevops.ci.adapter.dto.BuildParamSnapshotDTO;
import com.cmcc.cmdevops.ci.service.bo.BuildParamSnapshotBO;
import com.cmcc.cmdevops.ci.service.business.BuildParamSnapshotBizService;
import com.cmcc.cmdevops.util.BeanCloner;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/v1/paramSnapshot")
@RequiredArgsConstructor
public class BuildParamSnapshotController {

    private final BuildParamSnapshotBizService buildParamSnapshotBizService;

    @GetMapping("/list")
    public BaseResponse<List<BuildParamSnapshotDTO>> list(BuildParamSnapshotDTO paramSnapshotDTO){
        BuildParamSnapshotBO paramSnapshotBO = BeanCloner.clone(paramSnapshotDTO, BuildParamSnapshotBO.class);
        List<BuildParamSnapshotBO> list = buildParamSnapshotBizService.list(paramSnapshotBO);
        return BaseResponse.success(BeanCloner.clone(list, BuildParamSnapshotDTO.class));
    }
}
