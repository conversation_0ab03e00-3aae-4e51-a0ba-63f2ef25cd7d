package com.cmcc.cmdevops.ci.adapter.web;

import com.cmcc.cmdevops.BaseResponse;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.adapter.dto.BuildConfigFileDTO;
import com.cmcc.cmdevops.ci.adapter.dto.BuildPermissionDTO;
import com.cmcc.cmdevops.ci.adapter.dto.BuildTaskConverter;
import com.cmcc.cmdevops.ci.adapter.dto.BuildTaskDTO;
import com.cmcc.cmdevops.ci.adapter.dto.request.BuildConfigFilePageRequest;
import com.cmcc.cmdevops.ci.adapter.dto.request.BuildPermissionPageRequest;
import com.cmcc.cmdevops.ci.service.bo.BuildConfigFileBO;
import com.cmcc.cmdevops.ci.service.bo.BuildPermissionBO;
import com.cmcc.cmdevops.ci.service.bo.BuildTaskBO;
import com.cmcc.cmdevops.ci.service.business.BuildConfigFileBizService;
import com.cmcc.cmdevops.ci.service.business.BuildPermissionBizService;
import com.cmcc.cmdevops.ci.service.business.util.BeanConvertor;
import com.cmcc.cmdevops.util.BeanCloner;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * 注意这是已经使用swagger3.0版本
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/v1/permission")
@RequiredArgsConstructor
public class BuildPermissionController {
    private static final Logger LOGGER = LoggerFactory.getLogger(BuildPermissionController.class);

    @Resource
    private BuildPermissionBizService buildPermissionBizService;

    @GetMapping("/page")
    public PageResponse<List<BuildPermissionDTO>> page(BuildPermissionPageRequest pageRequest){
        BuildPermissionBO buildPermissionBO = BeanCloner.clone(pageRequest.getBuildPermissionDTO(), BuildPermissionBO.class);
        PageResponse<List<BuildPermissionBO>> pageResponse = buildPermissionBizService.page(pageRequest, buildPermissionBO);
        if (pageResponse == null) {
            return PageResponse.success(Collections.emptyList(), 0L, pageRequest.getPageNo(), pageRequest.getPageSize());
        }
        List<BuildPermissionDTO> list = BeanCloner.clone(pageResponse.getData(), BuildPermissionDTO.class);
        return PageResponse.success(list, pageResponse.getCount(), pageRequest.getPageNo(), pageRequest.getPageSize());
    }

    @PostMapping("/save")
    public BaseResponse<Void> save(@RequestBody List<BuildPermissionDTO> dtos){
        List<BuildPermissionBO> bos = BeanCloner.clone(dtos, BuildPermissionBO.class);
        buildPermissionBizService.save(bos);
        return BaseResponse.success();
    }

    @PutMapping("/update")
    public BaseResponse<Void> update(@RequestBody BuildPermissionDTO dto){
        BuildPermissionBO buildPermissionBO = BeanCloner.clone(dto, BuildPermissionBO.class);
        buildPermissionBizService.update(buildPermissionBO);
        return BaseResponse.success();
    }

    @DeleteMapping("/delete/{userUid}")
    public BaseResponse<Void> delete(@PathVariable String userUid){
        buildPermissionBizService.delete(userUid);
        return BaseResponse.success();
    }

    @GetMapping("/validPermission/{taskId}/{permissionCode}")
    public BaseResponse<Boolean> validPermission(@PathVariable String taskId, @PathVariable String permissionCode) {
        return BaseResponse.success(buildPermissionBizService.validPermission(taskId,permissionCode));
    }

    @PostMapping("/validPermission/batch/{permissionCode}")
    public BaseResponse<String> validPermissionBatch(@RequestBody List<BuildTaskDTO> tasks, @PathVariable String permissionCode) {
        return BaseResponse.success(buildPermissionBizService.validPermissionBatch(BeanCloner.clone(tasks, BuildTaskBO.class),permissionCode));
    }
}
