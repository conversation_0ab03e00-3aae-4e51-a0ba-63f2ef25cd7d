package com.cmcc.cmdevops.ci.adapter.dto.request;

import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.ci.adapter.dto.BuildSnapshotDTO;
import lombok.Getter;

import java.io.Serial;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> @since 2025-05-28
 */
@Getter
public class BuildSnapshotPageRequest extends PageRequest {
    @Serial
    private static final long serialVersionUID = -474806103198663152L;

    private BuildSnapshotDTO buildSnapshotDTO;

    public void setBuildSnapshotDTO(BuildSnapshotDTO buildSnapshotDTO) {
        this.buildSnapshotDTO = buildSnapshotDTO;
    }
}
