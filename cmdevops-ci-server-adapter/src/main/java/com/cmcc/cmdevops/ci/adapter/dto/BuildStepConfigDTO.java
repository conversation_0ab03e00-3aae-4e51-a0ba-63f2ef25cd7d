package com.cmcc.cmdevops.ci.adapter.dto;

import com.alibaba.fastjson2.JSONObject;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> @since 2025-05-20
 */
@Getter
@Setter
@Accessors(chain = true)
public class BuildStepConfigDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    public BuildStepConfigDTO() {
    }

    public BuildStepConfigDTO(Integer id, String taskId, String name, String type, Integer serial, JSONObject config, Boolean hasOpen, String spaceId, String tenantId, Boolean deleted, LocalDateTime createTime, String createUid, LocalDateTime updateTime, String updateUid, LocalDateTime deleteTime, String deleteUid) {
        this.id = id;
        this.taskId = taskId;
        this.name = name;
        this.type = type;
        this.serial = serial;
        this.config = config;
        this.hasOpen = hasOpen;
        this.spaceId = spaceId;
        this.tenantId = tenantId;
        this.deleted = deleted;
        this.createTime = createTime;
        this.createUid = createUid;
        this.updateTime = updateTime;
        this.updateUid = updateUid;
        this.deleteTime = deleteTime;
        this.deleteUid = deleteUid;
    }

    private Integer id;

    private String taskId;

    private String name;

    private String type;

    private Integer serial;

    private JSONObject config;

    private Boolean hasOpen;

    private String spaceId;

    private String tenantId;

    private Boolean deleted;

    private LocalDateTime createTime;

    private String createUid;

    private LocalDateTime updateTime;

    private String updateUid;

    private LocalDateTime deleteTime;

    private String deleteUid;
}
