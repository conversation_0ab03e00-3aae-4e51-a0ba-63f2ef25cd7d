package com.cmcc.cmdevops.ci.adapter.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> @since 2025-05-20
 */
@Getter
@Setter
@Accessors(chain = true)
public class BuildToolDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    private String toolName;

    private String toolImage;

    private String args;

    private String type;

    private String cacheDir;

}
