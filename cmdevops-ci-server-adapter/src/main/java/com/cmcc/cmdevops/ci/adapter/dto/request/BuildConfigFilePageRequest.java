package com.cmcc.cmdevops.ci.adapter.dto.request;

import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.ci.adapter.dto.BuildConfigFileDTO;
import com.cmcc.cmdevops.ci.adapter.dto.BuildEnvironmentDTO;

public class BuildConfigFilePageRequest extends PageRequest {
    private BuildConfigFileDTO buildConfigFileDTO;

    public BuildConfigFileDTO getBuildConfigFileDTO() {
        return buildConfigFileDTO;
    }

    public void setBuildConfigFileDTO(BuildConfigFileDTO buildConfigFileDTO) {
        this.buildConfigFileDTO = buildConfigFileDTO;
    }
}
