# Jackson全局配置说明

## 概述

`JacksonConfig.java` 是一个全局的Jackson配置类，用于统一配置项目中所有LocalDateTime、LocalDate、LocalTime类型字段的序列化和反序列化格式。

## 配置的格式

- **LocalDateTime**: `yyyy-MM-dd HH:mm:ss` (例如: 2024-01-15 14:30:25)
- **LocalDate**: `yyyy-MM-dd` (例如: 2024-01-15)
- **LocalTime**: `HH:mm:ss` (例如: 14:30:25)
- **时区**: GMT+8 (中国标准时间)

## 功能特性

1. **全局生效**: 配置会自动应用到所有使用Jackson进行JSON序列化/反序列化的场景
2. **统一格式**: 确保整个项目中的时间格式保持一致
3. **双向支持**: 同时支持序列化(Java对象→JSON)和反序列化(JSON→Java对象)
4. **时区处理**: 自动处理GMT+8时区转换

## 使用示例

### 1. 在DTO中使用

```java
@Data
public class UserDTO {
    private String name;
    private LocalDateTime createTime;  // 会自动格式化为 yyyy-MM-dd HH:mm:ss
    private LocalDate birthDate;       // 会自动格式化为 yyyy-MM-dd
}
```

### 2. Controller返回JSON

```java
@GetMapping("/user")
public BaseResponse<UserDTO> getUser() {
    UserDTO user = new UserDTO();
    user.setCreateTime(LocalDateTime.now());  // 输出: "2024-01-15 14:30:25"
    user.setBirthDate(LocalDate.now());       // 输出: "2024-01-15"
    return BaseResponse.success(user);
}
```

### 3. Controller接收JSON

```java
@PostMapping("/user")
public BaseResponse<Void> createUser(@RequestBody UserDTO user) {
    // 可以正确解析如下格式的JSON:
    // {
    //   "name": "张三",
    //   "createTime": "2024-01-15 14:30:25",
    //   "birthDate": "2024-01-15"
    // }
    return BaseResponse.success();
}
```

## 测试验证

项目中提供了 `DateTimeTestController` 用于测试配置是否生效：

- **GET** `/api/test/datetime/current` - 获取当前时间，验证序列化格式
- **POST** `/api/test/datetime/parse` - 提交时间数据，验证反序列化功能

### 测试示例

```bash
# 测试序列化
curl -X GET http://localhost:8080/cmdevops-ci-server/api/test/datetime/current

# 测试反序列化
curl -X POST http://localhost:8080/cmdevops-ci-server/api/test/datetime/parse \
  -H "Content-Type: application/json" \
  -d '{
    "message": "测试",
    "currentDateTime": "2024-01-15 14:30:25",
    "currentDate": "2024-01-15",
    "currentTime": "14:30:25"
  }'
```

## 注意事项

1. **优先级**: 此配置使用了 `@Primary` 注解，会覆盖Spring Boot默认的ObjectMapper配置
2. **兼容性**: 与现有的 `application.yml` 中的Jackson配置兼容
3. **全局影响**: 配置会影响所有使用Jackson的地方，包括HTTP接口、消息队列等
4. **时区一致性**: 确保前端和后端都使用相同的时区理解

## 扩展配置

如果需要支持其他时间格式或添加新的序列化规则，可以在 `javaTimeModule()` 方法中添加：

```java
// 添加自定义格式
javaTimeModule.addSerializer(ZonedDateTime.class, 
    new ZonedDateTimeSerializer(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss Z")));
```
