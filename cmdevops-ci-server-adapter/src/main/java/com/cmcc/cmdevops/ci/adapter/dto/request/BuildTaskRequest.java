package com.cmcc.cmdevops.ci.adapter.dto.request;


import com.cmcc.cmdevops.BaseRequest;
import com.cmcc.cmdevops.ci.adapter.dto.BuildTaskDTO;

public class BuildTaskRequest extends BaseRequest {
    private BuildTaskDTO buildTaskDTO;

    public BuildTaskDTO getBuildTaskDTO() {
        return buildTaskDTO;
    }

    public void setBuildTaskDTO(BuildTaskDTO buildTaskDTO) {
        this.buildTaskDTO = buildTaskDTO;
    }
}
