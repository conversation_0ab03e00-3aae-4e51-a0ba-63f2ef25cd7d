package com.cmcc.cmdevops.ci.adapter.web;

import com.cmcc.cmdevops.BaseResponse;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.adapter.dto.BuildCacheDTO;
import com.cmcc.cmdevops.ci.adapter.dto.BuildTaskConverter;
import com.cmcc.cmdevops.ci.adapter.dto.request.BuildCachePageRequest;
import com.cmcc.cmdevops.ci.adapter.dto.request.BuildTaskRequest;
import com.cmcc.cmdevops.ci.service.bo.BuildCacheBO;
import com.cmcc.cmdevops.ci.service.bo.BuildTaskBO;
import com.cmcc.cmdevops.ci.service.business.BuildCacheBizService;
import com.cmcc.cmdevops.util.BeanCloner;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 注意这是已经使用swagger3.0版本
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/v1/cache")
public class BuildCacheController {

    private static final Logger LOGGER = LoggerFactory.getLogger(BuildCacheController.class);

    @Resource
    private BuildCacheBizService buildCacheBizService;

    /**
     * 缓存列表
     */
    @GetMapping("/listByTaskId/{taskId}")
    public BaseResponse<List<BuildCacheDTO>> listByTaskId(@PathVariable String taskId) {
        List<BuildCacheBO> listByTaskId = buildCacheBizService.getListByTaskId(taskId);
        return BaseResponse.success(BeanCloner.clone(listByTaskId, BuildCacheDTO.class));
    }

    /**
     * 分页查询
     */
    @GetMapping("/page")
    public PageResponse<List<BuildCacheDTO>> page(BuildCachePageRequest pageRequest) {
        PageResponse<List<BuildCacheBO>> pageResponse = buildCacheBizService.pageBuildCaches(pageRequest);
        return PageResponse.success(BeanCloner.clone(pageResponse.getData(), BuildCacheDTO.class), pageResponse.getCount(), pageRequest.getPageNo(), pageRequest.getPageSize());
    }

    /**
     * 清理缓存
     */
    @PostMapping("/cleanCache/{taskId}")
    public BaseResponse<String> cleanCache(@PathVariable String taskId) {
        buildCacheBizService.cleanBuildCache(taskId);
        return BaseResponse.success();
    }

    /**
     * 创建缓存
     */
    @PostMapping("/save")
    public BaseResponse<Void> save(@RequestBody BuildCacheDTO cacheDTO) {
        BuildCacheBO cacheBO = BeanCloner.clone(cacheDTO, BuildCacheBO.class);
        buildCacheBizService.createBuildCache(cacheBO);
        return BaseResponse.success();
    }

    /**
     * 更新缓存
     */
    @PutMapping("/update")
    public BaseResponse<Void> update(@RequestBody BuildCacheDTO cacheDTO) {
        BuildCacheBO cacheBO = BeanCloner.clone(cacheDTO, BuildCacheBO.class);
        buildCacheBizService.updateBuildCache(cacheBO);
        return BaseResponse.success();
    }

    /**
     * 删除缓存
     */
    @DeleteMapping("/delete/{id}")
    public BaseResponse<Void> delete(@PathVariable("id") Integer id) {
        buildCacheBizService.deleteBuildCache(id);
        return BaseResponse.success();
    }

    /**
     * 缓存详情
     */
    @GetMapping("/detail/{id}")
    public BaseResponse<BuildCacheDTO> detail(@PathVariable Integer id) {
        BuildCacheBO cacheBO = buildCacheBizService.getBuildCacheById(id);
        BuildCacheDTO cacheDTO = BeanCloner.clone(cacheBO, BuildCacheDTO.class);
        return BaseResponse.success(cacheDTO);
    }

}
