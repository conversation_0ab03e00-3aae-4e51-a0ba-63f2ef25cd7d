package com.cmcc.cmdevops.ci.adapter.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> @since 2025-05-20
 */
@Getter
@Setter
@Accessors(chain = true)
public class BuildAdvancedConfigDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    public BuildAdvancedConfigDTO() {
    }

    public BuildAdvancedConfigDTO(Integer id, String taskId, Integer assignedNode, Short overtime, String spaceId, String tenantId, Boolean deleted, LocalDateTime createTime, String createUid, LocalDateTime updateTime, String updateUid, LocalDateTime deleteTime, String deleteUid) {
        this.id = id;
        this.taskId = taskId;
        this.assignedNode = assignedNode;
        this.overtime = overtime;
        this.spaceId = spaceId;
        this.tenantId = tenantId;
        this.deleted = deleted;
        this.createTime = createTime;
        this.createUid = createUid;
        this.updateTime = updateTime;
        this.updateUid = updateUid;
        this.deleteTime = deleteTime;
        this.deleteUid = deleteUid;
    }

    private Integer id;

    private String taskId;

    private Integer assignedNode;

    private Short overtime;

    private String spaceId;

    private String tenantId;

    private Boolean deleted;

    private LocalDateTime createTime;

    private String createUid;

    private LocalDateTime updateTime;

    private String updateUid;

    private LocalDateTime deleteTime;

    private String deleteUid;
}
