package com.cmcc.cmdevops.ci.adapter.dto;


import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 构建模板数据传输对象
 * </p>
 * @since 2025-05-21
 */
@Getter
@Setter
@Accessors(chain = true)
public class BuildTemplateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 模板标题
     */
    private String title;

    /**
     * 模板描述
     */
    private String templateDescribe;

    /**
     * 模板图标
     */
    private String icon;

    /**
     * 构建配置(JSON格式)
     */
    private String buildConfig;

    /**
     * 模板类型(1:系统模板 2:自定义模板 3:CI构建 4:自动化测试等)
     */
    private Integer templateType;

    /**
     * 工作空间ID
     */
    private String spaceId;

    /**
     * 模板分组
     */
    private String templateGroup;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 是否删除
     */
    private Boolean deleted;

    /**
     * 创建人ID
     */
    private String createUid;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    private String updateUid;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除人ID
     */
    private String deleteUid;

    /**
     * 删除时间
     */
    private LocalDateTime deleteTime;
}
