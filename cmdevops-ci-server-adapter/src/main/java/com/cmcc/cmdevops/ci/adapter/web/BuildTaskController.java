package com.cmcc.cmdevops.ci.adapter.web;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.cmcc.cmdevops.BaseResponse;
import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.adapter.dto.*;
import com.cmcc.cmdevops.ci.adapter.dto.request.BuildSnapshotPageRequest;
import com.cmcc.cmdevops.ci.adapter.dto.request.BuildTaskPageRequest;
import com.cmcc.cmdevops.ci.adapter.dto.request.BuildTaskRequest;
import com.cmcc.cmdevops.ci.adapter.dto.request.BuildVersionRequest;
import com.cmcc.cmdevops.ci.service.bo.*;
import com.cmcc.cmdevops.ci.service.business.*;
import com.cmcc.cmdevops.ci.service.business.util.UserUtils;
import com.cmcc.cmdevops.util.BeanCloner;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Collections;
import java.util.List;

/**
 * 注意这是已经使用swagger3.0版本
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/build")
public class BuildTaskController {
    private static final Logger LOGGER = LoggerFactory.getLogger(BuildTaskController.class);
    @Resource
    private BuildTaskBizService buildTaskBizService;

    @Resource
    private BuildToolBizService buildToolBizService;

    @Resource
    private BuildArtifactBizService buildArtifactBizService;

    @Resource
    private BuildCodeSnapshotBizService buildCodeSnapshotBizService;

    @Resource
    private BuildSnapshotBizService buildSnapshotBizService;

    @Resource
    private SWorkerOpenBizService sWorkerOpenBizService;

    @Resource
    private BuildCodeConfigBizService buildCodeConfigBizService;

    @Value("${ci.external-api.dev}")
    private Boolean isDev;

    // 分页查询构建任务
    @GetMapping("/pageList")
    public PageResponse<List<BuildTaskDTO>> pageBuildTasks(BuildTaskPageRequest buildTaskPageRequest) {
        BuildTaskBO buildTaskBO = BuildTaskConverter.convertToBO(buildTaskPageRequest.getBuildTaskDTO());
        PageResponse<List<BuildTaskBO>> pageResponse = buildTaskBizService.pageBuildTasks(buildTaskPageRequest, buildTaskBO);
        return BuildTaskConverter.convertToDTO(pageResponse, buildTaskPageRequest);
    }

    @GetMapping("/detail/{taskId}")
    public BaseResponse<BuildTaskDTO> getTask(@PathVariable String taskId) {
        BuildTaskBO buildTaskBO = buildTaskBizService.getTask(taskId);
        BuildTaskDTO buildTaskDTO = BuildTaskConverter.convertToDTO(buildTaskBO);
        return BaseResponse.success(buildTaskDTO);
    }

    // 编辑任务
    @PostMapping("/create")
    public BaseResponse<Void> createTask(@RequestBody BuildTaskRequest buildTaskRequest) {
        BuildTaskBO buildTaskBO = BuildTaskConverter.convertToBO(buildTaskRequest.getBuildTaskDTO());
        buildTaskBizService.createTask(buildTaskBO);
        return BaseResponse.success();
    }

    @PostMapping("/init/{spaceId}/{templateId}")
    public BaseResponse<String> initTask(@PathVariable String spaceId, @PathVariable Integer templateId) {
        String taskId = buildTaskBizService.initTask(spaceId, templateId);
        return BaseResponse.success(taskId);
    }

    @PutMapping("/update")
    public BaseResponse<String> updateTask(@RequestBody BuildTaskRequest buildTaskRequest) {
        BuildTaskBO buildTaskBO = BuildTaskConverter.convertToBO(buildTaskRequest.getBuildTaskDTO());
        buildTaskBizService.updateTask(buildTaskBO);
        return BaseResponse.success();
    }

    // 复制任务
    @PostMapping("/copy/{taskId}")
    public BaseResponse<String> copyTask(@PathVariable String taskId) {
        buildTaskBizService.copyTask(taskId);
        return BaseResponse.success();
    }

    // 执行任务
    @PostMapping("/start")
    public BaseResponse<String> startTask(@RequestBody BuildTaskRequest buildTaskRequest) {
        BuildTaskBO buildTaskBO = BuildTaskConverter.convertToBO(buildTaskRequest.getBuildTaskDTO());
        String buildSnapshotId = buildTaskBizService.startTask(buildTaskBO, "ONESELF");
        return BaseResponse.success(buildSnapshotId);
    }

    @PostMapping("/start/{taskId}")
    public BaseResponse<String> startTask(@PathVariable String taskId) {
        String buildSnapshotId = buildTaskBizService.startTask(taskId, "ONESELF");
        return BaseResponse.success(buildSnapshotId);
    }

    // 删除任务
    @DeleteMapping("/delete/{taskId}")
    public BaseResponse<String> deleteTask(@PathVariable String taskId) {
        buildTaskBizService.deleteTask(taskId);
        return BaseResponse.success();
    }

    // 批量删除
    @DeleteMapping("/batch/delete")
    public BaseResponse<String> batchDeleteTask(@RequestBody List<String> taskIds) {
        buildTaskBizService.batchDeleteTask(taskIds);
        return BaseResponse.success();
    }

    // 停止任务
    @PostMapping("/stop/{buildSnapshotId}")
    public BaseResponse<String> stopTask(@PathVariable String buildSnapshotId) {
        buildTaskBizService.stopTask(buildSnapshotId);
        return BaseResponse.success();
    }

    @PostMapping("/stopLast/{taskId}")
    public BaseResponse<String> stopLastTask(@PathVariable String taskId) {
        // 查询最新的快照
        PageRequest pageRequest = new PageRequest(1, 1);
        PageResponse<List<BuildSnapshotBO>> pageResponse = buildSnapshotBizService.list(pageRequest, new BuildSnapshotBO().setTaskId(taskId));
        BuildSnapshotBO buildSnapshot = pageResponse.getData().get(0);
        buildTaskBizService.stopTask(buildSnapshot.getId());
        return BaseResponse.success();
    }

    // 获取构建历史
    @GetMapping("/buildHistory")
    public PageResponse<List<BuildSnapshotDTO>> getBuildHistory(BuildSnapshotPageRequest pageRequest) {
        BuildSnapshotBO buildSnapshotBO = BeanCloner.clone(pageRequest.getBuildSnapshotDTO(), BuildSnapshotBO.class);
        PageResponse<List<BuildSnapshotBO>> pageResponse = buildSnapshotBizService.list(pageRequest, buildSnapshotBO);
        if (pageResponse == null) {
            return PageResponse.success(Collections.emptyList(), 0L, pageRequest.getPageNo(), pageRequest.getPageSize());
        }
        List<BuildSnapshotDTO> list = BeanCloner.clone(pageResponse.getData(), BuildSnapshotDTO.class);
        list.forEach(each -> {
            BuildCodeSnapshotBO buildCodeSnapshotBO = buildCodeSnapshotBizService.getBuildCodeSnapshotBySnapshotId(each.getId());
            BuildCodeSnapshotDTO buildCodeSnapshotDTO = BeanCloner.clone(buildCodeSnapshotBO, BuildCodeSnapshotDTO.class);
            buildCodeSnapshotDTO.setCommitMsg(JSONObject.parseObject(buildCodeSnapshotDTO.getCommitMessage()));
            each.setBuildCodeSnapshot(buildCodeSnapshotDTO);
        });
        return PageResponse.success(list, pageResponse.getCount(), pageRequest.getPageNo(), pageRequest.getPageSize());
    }

    // 获取构建快照详情
    @GetMapping("/getBuildSnapshotDetail/{buildSnapshotId}")
    public BaseResponse<BuildSnapshotDTO> getBuildSnapshotDetail(@PathVariable String buildSnapshotId) {
        BuildSnapshotBO buildSnapshotBO = buildTaskBizService.getBuildSnapshot(buildSnapshotId);
        BuildCodeSnapshotBO buildCodeSnapshotBO = buildCodeSnapshotBizService.getBuildCodeSnapshotBySnapshotId(buildSnapshotId);
        BuildCodeSnapshotDTO buildCodeSnapshotDTO = BeanCloner.clone(buildCodeSnapshotBO, BuildCodeSnapshotDTO.class);
        buildCodeSnapshotDTO.setCommitMsg(JSONObject.parseObject(buildCodeSnapshotDTO.getCommitMessage()));
        List<BuildStepSnapshotBO> buildStepSnapshotBOList = buildTaskBizService.getBuildStepSnapshots(buildSnapshotId);
        List<BuildStepSnapshotDTO> buildStepSnapshotDTOList = BeanCloner.clone(buildStepSnapshotBOList, BuildStepSnapshotDTO.class);
        BuildSnapshotDTO buildSnapshotDTO = BeanCloner.clone(buildSnapshotBO, BuildSnapshotDTO.class);
        buildSnapshotDTO.setBuildStepSnapshots(buildStepSnapshotDTOList);
        buildSnapshotDTO.setBuildCodeSnapshot(buildCodeSnapshotDTO);
        return BaseResponse.success(buildSnapshotDTO);
    }

    @GetMapping("/getBuildSnapshot/{buildSnapshotId}")
    public BaseResponse<BuildSnapshotDTO> getBuildSnapshot(@PathVariable String buildSnapshotId) {
        BuildSnapshotBO buildSnapshotBO = buildTaskBizService.getBuildSnapshot(buildSnapshotId);
        BuildSnapshotDTO buildSnapshotDTO = BeanCloner.clone(buildSnapshotBO, BuildSnapshotDTO.class);
        return BaseResponse.success(buildSnapshotDTO);
    }

    // 获取构建快照详情的步骤.
    @GetMapping("/getBuildStepSnapshots/{buildSnapshotId}")
    public BaseResponse<List<BuildStepSnapshotDTO>> getBuildStepSnapshots(@PathVariable String buildSnapshotId) {
        List<BuildStepSnapshotBO> buildStepSnapshotBOList = buildTaskBizService.getBuildStepSnapshots(buildSnapshotId);
        List<BuildStepSnapshotDTO> buildStepSnapshotDTOList = BeanCloner.clone(buildStepSnapshotBOList, BuildStepSnapshotDTO.class);
        return BaseResponse.success(buildStepSnapshotDTOList);
    }

    // 查询日志
    @GetMapping("/queryLog/{buildSnapshotId}")
    public BaseResponse<String> queryLog(@PathVariable String buildSnapshotId) {
        String log = buildTaskBizService.queryLog(buildSnapshotId, false);
        return BaseResponse.success(log);
    }

    // 导出日志
    @GetMapping("/exportLog/{buildSnapshotId}")
    public ResponseEntity<InputStreamResource> exportLog(@PathVariable String buildSnapshotId) {
        String log = buildTaskBizService.queryLog(buildSnapshotId, true);
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(log.getBytes());
        // 设置响应头，告诉浏览器这是一个文件下载
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Disposition", "attachment; filename=build.log");
        // 返回字符串内容作为文件下载
        return ResponseEntity.ok()
                .headers(headers)
                .body(new InputStreamResource(byteArrayInputStream));
    }

    @PostMapping("/downArtifact")
    public ResponseEntity<InputStreamResource> downArtifact(@RequestBody BuildArtifactDTO buildArtifactDTO) {
        String buildArtifactUrl = buildArtifactDTO.getBuildArtifactUrl();         // HTTP URL 地址
        String buildArtifactRealName = buildArtifactDTO.getBuildArtifactRealName(); // 下载显示的文件名
        if (!isDev) {
            String downloadUrl = buildArtifactUrl;
            String[] parts = downloadUrl.split("/");
            for (int i = 0; i < parts.length; i++) {
                if (parts[i].equals("artifactory") && i + 1 < parts.length) {
                    parts[i + 1] += "_generic";
                    break;
                }
            }
            buildArtifactUrl = String.join("/", parts);
        }
        log.info(buildArtifactUrl);
        // 打开远程 HTTP 连接
        InputStream inputStream = null;
        try {
            URL url = new URL(buildArtifactUrl);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            if (!isDev) {
                String userId = UserUtils.getUserId();
                String normalRepositoryAuth = sWorkerOpenBizService.normalRepositoryAuth(userId);
                String auth = normalRepositoryAuth.split("#######")[0] + ":" + normalRepositoryAuth.split("#######")[1];
                String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes(StandardCharsets.UTF_8));
                conn.setRequestProperty("Authorization", "Basic " + encodedAuth);
            }


            conn.setRequestMethod("GET");
            conn.setConnectTimeout(10000); // 超时可配置
            conn.setReadTimeout(15000);

            int responseCode = conn.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                return ResponseEntity.status(responseCode).build();
            }

            inputStream = conn.getInputStream();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        // 编码文件名（防止中文乱码）
        String encodedFilename = URLEncoder.encode(buildArtifactRealName, StandardCharsets.UTF_8).replaceAll("\\+", "%20");

        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + encodedFilename);
        headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);

        return ResponseEntity.ok()
                .headers(headers)
                .body(new InputStreamResource(inputStream));
    }

    // 构建产物列表
    @GetMapping("/products/{buildSnapshotId}")
    public BaseResponse<List<BuildArtifactDTO>> getBuildProducts(@PathVariable String buildSnapshotId) {
        BuildArtifactBO buildArtifactBO = new BuildArtifactBO();
        buildArtifactBO.setBuildSnapshotId(buildSnapshotId);
        List<BuildArtifactBO> list = buildArtifactBizService.list(buildArtifactBO);
        List<BuildArtifactDTO> buildArtifactList = BeanCloner.clone(list, BuildArtifactDTO.class);
        for (BuildArtifactDTO buildArtifactDTO : buildArtifactList) {
            String vcsId = buildCodeConfigBizService.getBuildCodeConfigByTaskId(buildArtifactDTO.getTaskId()).getVcsId();
            JSONObject codeRepoMetaData = sWorkerOpenBizService.getCodeRepoMetaData(vcsId, UserUtils.getTenantId());
            JSONArray apps = codeRepoMetaData.getJSONArray("apps");
            if (apps.size() == 1) {
                buildArtifactDTO.setAppName(apps.getJSONObject(0).getString("appName"));
            }
        }
        return BaseResponse.success(buildArtifactList);
    }

    // 获取构建工具列表
    @GetMapping("/buildTools")
    public BaseResponse<List<BuildToolDTO>> pageBuildTools() {
        List<BuildToolBO> list = buildToolBizService.list(new BuildToolBO());
        List<BuildToolDTO> buildToolList = BeanCloner.clone(list, BuildToolDTO.class);
        return BaseResponse.success(buildToolList);
    }

    /**
     * 禁用
     */
    @PostMapping("/disable/{taskId}")
    public BaseResponse<Void> disableTask(@PathVariable String taskId) {
        buildTaskBizService.disableTask(taskId);
        return BaseResponse.success();
    }

    /**
     * 启用
     */
    @PostMapping("/enable/{taskId}")
    public BaseResponse<Void> enableTask(@PathVariable String taskId) {
        buildTaskBizService.enableTask(taskId);
        return BaseResponse.success();
    }

    /**
     * 构建分析
     */
    @GetMapping("/analysis")
    public BaseResponse<BuildAnalysisBO> analysis(@RequestParam String startTime, @RequestParam String endTime,
                                                  @RequestParam String taskId) {
        // BuildAnalysisDTO analysisDTO = BeanCloner.clone(buildSnapshotBizService.analysis(startTime, endTime, taskId), BuildAnalysisDTO.class);
        return BaseResponse.success(buildSnapshotBizService.analysis(startTime, endTime, taskId));
    }

    /**
     * 获取指定分组中的所有任务
     */
    @GetMapping("/group/tasks/{groupId}")
    public BaseResponse<List<BuildTaskDTO>> getTasksByGroupId(@PathVariable String groupId) {
        List<BuildTaskBO> taskList = buildTaskBizService.getTasksByGroupId(groupId);
        List<BuildTaskDTO> taskDTOList = BeanCloner.clone(taskList, BuildTaskDTO.class);
        return BaseResponse.success(taskDTOList);
    }

    @GetMapping("/history/BuildVersionList")
    PageResponse<List<BuildVersionDTO>> getBuildVersionList(BuildVersionRequest request) {
        BuildVersionBO buildVersionBO = BeanCloner.clone(request.getBuildVersionDTO(), BuildVersionBO.class);
        PageRequest pageRequest = new PageRequest(request.getPageNo(), request.getPageSize());
        PageResponse<List<BuildVersionBO>> pageResponse = buildTaskBizService.getBuildVersionList(pageRequest, buildVersionBO);
        List<BuildVersionBO> buildVersionBOList = pageResponse.getData();
        List<BuildVersionDTO> buildVersionDTOList = BeanCloner.clone(buildVersionBOList, BuildVersionDTO.class);
        return PageResponse.success(buildVersionDTOList, pageResponse.getCount(), pageRequest.getPageNo(), pageRequest.getPageSize());
    }
}
