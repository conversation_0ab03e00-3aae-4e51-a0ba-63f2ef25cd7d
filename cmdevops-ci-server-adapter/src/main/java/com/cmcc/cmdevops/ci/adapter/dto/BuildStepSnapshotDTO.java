package com.cmcc.cmdevops.ci.adapter.dto;

import com.alibaba.fastjson2.JSONObject;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> @since 2025-05-20
 */
@Getter
@Setter
@Accessors(chain = true)
public class BuildStepSnapshotDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private String taskId;

    private String buildSnapshotId;

    private String name;

    private String type;

    private Integer serial;

    private String config;

    private Boolean hasOpen;

    private String stepState;

    private String stepResult;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private Integer duration;

    private String stepLog;

    private String stepLogPath;

    private String stepStatus;

    private String spaceId;

    private String tenantId;

    private Boolean deleted;

    private LocalDateTime createTime;

    private String createUid;

    private LocalDateTime updateTime;

    private String updateUid;

    private LocalDateTime deleteTime;

    private String deleteUid;

    private JSONObject stage;

    private String stageNum;

    private String log;
}
