package com.cmcc.cmdevops.ci.adapter.dto.request;

import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.ci.adapter.dto.BuildConfigFileDTO;
import com.cmcc.cmdevops.ci.adapter.dto.BuildPermissionDTO;

public class BuildPermissionPageRequest extends PageRequest {
    private BuildPermissionDTO buildPermissionDTO;

    public BuildPermissionDTO getBuildPermissionDTO() {
        return buildPermissionDTO;
    }

    public void setBuildPermissionDTO(BuildPermissionDTO buildPermissionDTO) {
        this.buildPermissionDTO = buildPermissionDTO;
    }
}
