package com.cmcc.cmdevops.ci.adapter.web;

import com.cmcc.cmdevops.BaseResponse;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.adapter.dto.*;
import com.cmcc.cmdevops.ci.service.bo.BuildGroupBO;
import com.cmcc.cmdevops.ci.service.business.BuildGroupBizService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title: BuildGroupController</p>
 * <p>Description: 分组功能 - 控制器</p>
 * <p>Copyright: Copyright (c) 2021</p>
 * <p>Company: SI-TECH </p>
 * Author by_csd_hlj
 * Version 1.0
 * CreateTime 2025/5/29 16:45
 */
@RestController
@RequestMapping("/api/v1/build/group")
public class BuildGroupController {
    private static final Logger LOGGER = LoggerFactory.getLogger(BuildGroupController.class);
    BuildGroupBizService buildGroupBizService;

    public BuildGroupController(BuildGroupBizService buildGroupBizService) {
        this.buildGroupBizService = buildGroupBizService;
    }

    /**
     * Description 分组功能 - 分页列表
     * Author by_csd_hlj
     * Date 2025/5/29 16:13
     * Param BuildGroupPageRequest
     * return
     **/
    @GetMapping("/page")
    public PageResponse<List<BuildGroupBO>> pageBuildGroups(BuildGroupBO buildGroupBO) {

        PageResponse<List<BuildGroupBO>> pageResponse = buildGroupBizService.pageBuildGroups(buildGroupBO );

        return pageResponse;
    }
    /**
     * Description 条件查询
     * Author by_csd_hlj
     * Date 2025/6/6 11:22
     * Param
     * return
     **/
    @GetMapping("/list")
    public BaseResponse<List<BuildGroupBO>> listBuildGroups(BuildGroupBO buildGroupBO) {

        List<BuildGroupBO> response = buildGroupBizService.listBuildGroups(buildGroupBO);

        return BaseResponse.success(response);
    }

    /**
     * Description 根据id 查询
     * Author by_csd_hlj
     * Date 2025/6/3 9:59
     * Param groupId
     * return
     **/
    @GetMapping("/getGroup/{groupId}")
    public BaseResponse<BuildGroupDTO> getGroups(@PathVariable Integer groupId) {

        BuildGroupBO groupBO = buildGroupBizService.getGroup(groupId);

        BuildGroupDTO groupDTO = BuildGroupConverter.convertToDTO(groupBO);

        return BaseResponse.success(groupDTO);
    }

    /**
     * Description 新增 / 修改 分组
     * Author by_csd_hlj
     * Date 2025/6/3 10:24
     * Param BuildGroupPageRequest
     * return
     **/
    @PostMapping("/saveGroup")
    public BaseResponse<String> saveGroup(@RequestBody BuildGroupBO buildGroupBO){

        try{
            String id = buildGroupBizService.saveGroup(buildGroupBO);
            return BaseResponse.success(id);
        }catch (Exception e){
            e.printStackTrace();
            return BaseResponse.invalidate(e.getMessage());
        }

    }

    /**
     * Description 删除分组
     * Author by_csd_hlj
     * Date 2025/6/3 10:24
     * Param groupId
     * return
     **/
    @PostMapping("/deleteGroup/{groupId}")
    public BaseResponse<String> deleteGroup(@PathVariable Integer groupId){
        buildGroupBizService.deleteGroup(groupId);
        return BaseResponse.success();
    }

    /**
     * Description 批量操作
     * Author by_csd_hlj
     * Date 2025/6/6 10:51
     * Param
     * return
     **/
    @PostMapping("/batchGroupRelation")
    public BaseResponse<String> batchGroupRelation(@RequestBody BuildGroupBO buildGroupBO){
        buildGroupBizService.batchGroupRelation(buildGroupBO);
        return BaseResponse.success();
    }
}
