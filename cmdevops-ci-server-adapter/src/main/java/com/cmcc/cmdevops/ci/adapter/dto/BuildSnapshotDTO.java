package com.cmcc.cmdevops.ci.adapter.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> @since 2025-05-20
 */
@Getter
@Setter
@Accessors(chain = true)
public class BuildSnapshotDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    private String taskId;

    private String buildStatus;

    private Long buildNumber;

    private Integer duration;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    private String spaceId;

    private String tenantId;

    private String source;

    private Boolean deleted;

    private LocalDateTime createTime;

    private String createUid;

    private LocalDateTime updateTime;

    private String updateUid;

    private LocalDateTime deleteTime;

    private String deleteUid;

    private BuildCodeSnapshotDTO buildCodeSnapshot;

    private List<BuildStepSnapshotDTO> buildStepSnapshots;
}
