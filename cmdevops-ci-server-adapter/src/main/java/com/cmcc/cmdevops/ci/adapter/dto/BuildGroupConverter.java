package com.cmcc.cmdevops.ci.adapter.dto;

import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.bo.BuildGroupBO;
import com.cmcc.cmdevops.util.BeanCloner;

import java.util.Collections;
import java.util.List;

public class BuildGroupConverter {

    public static BuildGroupDTO convertToDTO(BuildGroupBO buildGroupBO) {
        if(buildGroupBO ==  null){
            return new BuildGroupDTO();
        }else {
            return BeanCloner.clone(buildGroupBO, BuildGroupDTO.class);
        }
    }

    public static BuildGroupBO convertToBO(BuildGroupDTO buildGroupDTO) {
        if(buildGroupDTO == null) {
            return new BuildGroupBO() ;
        }else {
            return BeanCloner.clone(buildGroupDTO, BuildGroupBO.class);
        }
    }

    public static PageResponse<List<BuildGroupDTO>> convertToDTO(PageResponse<List<BuildGroupBO>> pageResponse, PageRequest pageRequest) {
        if (pageResponse == null) {
            return PageResponse.success(Collections.emptyList(), 0L, pageRequest.getPageNo(), pageRequest.getPageSize());
        }
        List<BuildGroupDTO> list = BeanCloner.clone(pageResponse.getData(), BuildGroupDTO.class);
        return PageResponse.success(list, pageResponse.getCount(), pageRequest.getPageNo(), pageRequest.getPageSize());
    }
}
