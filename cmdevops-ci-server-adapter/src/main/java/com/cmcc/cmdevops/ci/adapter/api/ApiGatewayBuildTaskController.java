package com.cmcc.cmdevops.ci.adapter.api;

import com.alibaba.fastjson2.JSONObject;
import com.cmcc.cmdevops.BaseResponse;
import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.adapter.dto.*;
import com.cmcc.cmdevops.ci.adapter.dto.request.BuildTaskPageRequest;
import com.cmcc.cmdevops.ci.adapter.dto.request.BuildTaskRequest;
import com.cmcc.cmdevops.ci.adapter.web.BuildTaskController;
import com.cmcc.cmdevops.ci.service.bo.*;
import com.cmcc.cmdevops.ci.service.business.*;
import com.cmcc.cmdevops.ci.service.business.impl.BuildSnapshotBizServiceImpl;
import com.cmcc.cmdevops.util.BeanCloner;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.xml.bind.SchemaOutputResolver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/openapi/build/")
public class ApiGatewayBuildTaskController {
    private static final Logger logger = LoggerFactory.getLogger(ApiGatewayBuildTaskController.class);

    @Resource
    private BuildTaskBizService buildTaskBizService;

    @Resource
    private BuildArtifactBizService buildArtifactBizService;

    @Resource
    private BuildCodeSnapshotBizService buildCodeSnapshotBizService;

    @Resource
    private BuildStepConfigBizService buildStepConfigBizService;

    @Resource
    private BuildSnapshotBizService buildSnapshotBizService;

    // 分页查询构建任务
    @Tag(name = "构建任务列表", description = "构建任务列表")
    @PostMapping("/pageList")
    public PageResponse<List<BuildTaskDTO>> pageBuildTasks(@RequestBody BuildTaskPageRequest buildTaskPageRequest) {
        BuildTaskBO buildTaskBO = BuildTaskConverter.convertToBO(buildTaskPageRequest.getBuildTaskDTO());
        PageResponse<List<BuildTaskBO>> pageResponse = buildTaskBizService.pageBuildTasks(buildTaskPageRequest, buildTaskBO);
        return BuildTaskConverter.convertToDTO(pageResponse, buildTaskPageRequest);
    }


    @Tag(name = "启动构建任务", description = "启动构建任务")
    @PostMapping("/start/{taskId}")
    public BaseResponse<String> startTask(@PathVariable String taskId) {
        String buildSnapshotId = buildTaskBizService.startTask(taskId, "PIPELINE");
        return BaseResponse.success(buildSnapshotId);
    }

    @Tag(name = "查询构建状态", description = "查询构建状态")
    @GetMapping("/queryStatus/{buildSnapshotId}")
    public BaseResponse<String> getBuildSnapshot(@PathVariable String buildSnapshotId) {
        BuildSnapshotBO buildSnapshotBO = buildTaskBizService.getBuildSnapshot(buildSnapshotId);
        return BaseResponse.success(StatusEnum.fromCode(Integer.parseInt(buildSnapshotBO.getBuildStatus())));
    }

    // 查询日志
    @Tag(name = "查询构建日志", description = "查询构建日志")
    @GetMapping("/queryLog/{buildSnapshotId}")
    public BaseResponse<String> queryLog(@PathVariable String buildSnapshotId) {
        String log = buildTaskBizService.queryLog(buildSnapshotId, true);
        return BaseResponse.success(log);
    }

    // 构建产物列表
    @Tag(name = "查询构建产物", description = "查询构建产物")
    @GetMapping("/products/{buildSnapshotId}")
    public BaseResponse<List<BuildArtifactDTO>> getBuildProducts(@PathVariable String buildSnapshotId) {
        BuildArtifactBO buildArtifactBO = new BuildArtifactBO();
        buildArtifactBO.setBuildSnapshotId(buildSnapshotId);
        List<BuildArtifactBO> list = buildArtifactBizService.list(buildArtifactBO);
        List<BuildArtifactDTO> buildArtifactList = BeanCloner.clone(list, BuildArtifactDTO.class);
        return BaseResponse.success(buildArtifactList);
    }

    @Tag(name = "查询软件包名称", description = "查询软件包名称")
    @GetMapping("/queryArtifactName")
    public BaseResponse<List<String>> queryArtifactName(@RequestParam(name = "artifactType", required = false) String artifactType,
                                                        @RequestParam(name = "taskId", required = false) String taskId) {
        List<String> artifacNametList = new ArrayList<>();
        List<BuildStepConfigBO> buildStepConfigBOList = buildStepConfigBizService.getBuildStepConfigListByTaskId(taskId);
        buildStepConfigBOList.forEach(buildStepConfigBO -> {
            if ("0".equals(artifactType)) {
                if (buildStepConfigBO.getType().equals("uploadDocker")) {
                    String artifactName = buildStepConfigBO.getConfig().getString("artifactName");
                    artifacNametList.add(artifactName);
                }
            }
            if ("1".equals(artifactType)) {
                if (buildStepConfigBO.getType().equals("uploadArtifact")) {
                    String artifactName = buildStepConfigBO.getConfig().getString("artifactName");
                    artifacNametList.add(artifactName);
                }
            }
        });
        return BaseResponse.success(artifacNametList);
    }

    @Tag(name = "查询产物详细信息", description = "查询产物详细信息")
    @GetMapping("/queryArtifact")
    public BaseResponse<List<BuildArtifactDTO>> queryArtifact(@RequestParam(name = "artifactType", required = false) String artifactType,
                                                              @RequestParam(name = "artifactName", required = false) String artifactName,
                                                              @RequestParam(name = "version", required = false) String version,
                                                              @RequestParam(name = "taskId", required = false) String taskId,
                                                              @RequestParam(name = "buildId", required = false) String buildId) {
        BuildArtifactDTO buildArtifact = new BuildArtifactDTO();
        buildArtifact.setTaskId(taskId);
        buildArtifact.setBuildSnapshotId(buildId);
        buildArtifact.setBuildArtifactType(artifactType);
        buildArtifact.setBuildArtifactName(artifactName);
        buildArtifact.setBuildVersion(version);
        BuildArtifactBO buildArtifactBO = BeanCloner.clone(buildArtifact, BuildArtifactBO.class);
        List<BuildArtifactDTO> artifacNametList = new ArrayList<>();
        List<BuildArtifactBO> list = buildArtifactBizService.list(buildArtifactBO);
        artifacNametList.addAll(BeanCloner.clone(list, BuildArtifactDTO.class));
        return BaseResponse.success(artifacNametList);
    }

    // 构建版本列表
    @Tag(name = "构建版本列表", description = "构建版本列表")
    @GetMapping("/versions/{taskId}")
    public BaseResponse<List<BuildCodeSnapshotDTO>> getBuildVersions(@PathVariable String taskId) {
        List<BuildCodeSnapshotBO> codeSnapshotBOS = buildCodeSnapshotBizService.getBuildCodeSnapshotByTaskId(taskId);
        return BaseResponse.success(BeanCloner.clone(codeSnapshotBOS, BuildCodeSnapshotDTO.class));
    }

    @GetMapping("/getBuildMetadata/{buildId}")
    public BaseResponse<List<Metadata>> getMetadata(@PathVariable String buildId) {
        List<Metadata> metadataList = buildSnapshotBizService.getMetadataList(buildId);
        return BaseResponse.success(metadataList);
    }

    @PostMapping("/addBuildMetadata")
    public BaseResponse<String> getMetadata(@RequestBody List<Metadata> metadataList) {
        buildSnapshotBizService.sendMessage(metadataList);
        return BaseResponse.success();
    }


    @Tag(name = "创建并启动构建任务", description = "启动构建任务")
    @PostMapping("/createAndStart/{taskId}")
    public BaseResponse<String> createAndStart(@PathVariable String taskId, @RequestBody BuildTaskDTO buildTaskDTO) {
        BuildTaskBO buildTaskBO = JSONObject.parseObject(JSONObject.toJSONString(buildTaskDTO), BuildTaskBO.class);
        String buildSnapshotId = buildTaskBizService.createAndStart(taskId, buildTaskBO);
        return BaseResponse.success(buildSnapshotId);
    }
}
