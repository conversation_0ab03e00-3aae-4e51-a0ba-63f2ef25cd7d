package com.cmcc.cmdevops.ci.adapter.web;

import com.cmcc.cmdevops.BaseResponse;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.adapter.dto.BuildParameterDTO;
import com.cmcc.cmdevops.ci.adapter.dto.request.BuildParameterPageRequest;
import com.cmcc.cmdevops.ci.service.bo.BuildParameterBO;
import com.cmcc.cmdevops.ci.service.business.BuildParameterBizService;
import com.cmcc.cmdevops.util.BeanCloner;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

@RestController
@RequestMapping("/api/v1/parameter")
@RequiredArgsConstructor
public class BuildParameterController {

    private final BuildParameterBizService buildParameterBizService;

    @GetMapping("/page")
    public PageResponse<List<BuildParameterDTO>> page(BuildParameterPageRequest pageRequest){
        BuildParameterBO buildParameterBO = BeanCloner.clone(pageRequest.getBuildParameterDTO(), BuildParameterBO.class);
        PageResponse<List<BuildParameterBO>> pageResponse = buildParameterBizService.page(pageRequest, buildParameterBO);
        if (pageResponse == null) {
            return PageResponse.success(Collections.emptyList(), 0L, pageRequest.getPageNo(), pageRequest.getPageSize());
        }
        List<BuildParameterDTO> list = BeanCloner.clone(pageResponse.getData(), BuildParameterDTO.class);
        return PageResponse.success(list, pageResponse.getCount(), pageRequest.getPageNo(), pageRequest.getPageSize());
    }

    @PostMapping("/save")
    public BaseResponse<Void> save(@RequestBody BuildParameterDTO dto){
        BuildParameterBO buildParameterBO = BeanCloner.clone(dto, BuildParameterBO.class);
        buildParameterBizService.save(buildParameterBO);
        return BaseResponse.success();
    }

    @PostMapping("/batch/save")
    public BaseResponse<Void> batchSave(@RequestBody List<BuildParameterDTO> dtos){
        List<BuildParameterBO> bos = BeanCloner.clone(dtos, BuildParameterBO.class);
        buildParameterBizService.batchSave(bos);
        return BaseResponse.success();
    }

    @PutMapping("/update")
    public BaseResponse<Void> update(@RequestBody BuildParameterDTO dto){
        BuildParameterBO buildParameterBO = BeanCloner.clone(dto, BuildParameterBO.class);
        buildParameterBizService.update(buildParameterBO);
        return BaseResponse.success();
    }

    @DeleteMapping("/delete/{id}")
    public BaseResponse<Void> delete(@PathVariable Integer id){
        buildParameterBizService.delete(id);
        return BaseResponse.success();
    }

    @GetMapping("/detail/{id}")
    public BaseResponse<BuildParameterDTO> detail(@PathVariable Integer id){
        BuildParameterDTO buildParameterDTO = BeanCloner.clone(buildParameterBizService.detail(id), BuildParameterDTO.class);
        return BaseResponse.success(buildParameterDTO);
    }

    @GetMapping("/listByTaskId/{taskId}")
    public BaseResponse<List<BuildParameterDTO>> listByTaskId(@PathVariable String taskId){
        List<BuildParameterBO> listByTaskId = buildParameterBizService.listByTaskId(taskId);
        return BaseResponse.success(BeanCloner.clone(listByTaskId, BuildParameterDTO.class));
    }

}
