package com.cmcc.cmdevops.ci.adapter.dto;

import com.alibaba.fastjson2.JSONObject;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> @since 2025-05-20
 */
@Getter
@Setter
@Accessors(chain = true)
public class BuildCodeSnapshotDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private String taskId;

    private String buildSnapshotId;

    private String vcsId;

    private String vcsName;

    private String vcsRepository;

    private String vcsBranch;

    private String vcsCloneType;

    private Boolean vcsSubmodule;

    private String commitId;

    private String commitMessage;

    private String changeMessage;

    private JSONObject commitMsg;
    
    private String spaceId;

    private String tenantId;

    private Boolean deleted;

    private LocalDateTime createTime;

    private String createUid;

    private LocalDateTime updateTime;

    private String updateUid;

    private LocalDateTime deleteTime;

    private String deleteUid;
}
