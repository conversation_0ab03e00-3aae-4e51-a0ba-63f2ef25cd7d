package com.cmcc.cmdevops.ci.adapter.dto;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.bo.BuildTaskBO;
import com.cmcc.cmdevops.ci.service.bo.BuildTaskBO;
import com.cmcc.cmdevops.ci.service.business.util.BeanConvertor;
import com.cmcc.cmdevops.util.BeanCloner;

import java.util.Collections;
import java.util.List;

public class BuildTaskConverter {

    public static BuildTaskDTO convertToDTO(BuildTaskBO demoProductInfoBO) {
        return JSONObject.parseObject(JSONObject.toJSONString(demoProductInfoBO), BuildTaskDTO.class);
    }

    public static BuildTaskBO convertToBO(BuildTaskDTO demoProductInfoDTO) {
        return JSONObject.parseObject(JSONObject.toJSONString(demoProductInfoDTO), BuildTaskBO.class);
    }

    public static PageResponse<List<BuildTaskDTO>> convertToDTO(PageResponse<List<BuildTaskBO>> pageResponse, PageRequest pageRequest) {
        if (pageResponse == null) {
            return PageResponse.success(Collections.emptyList(), 0L, pageRequest.getPageNo(), pageRequest.getPageSize());
        }
        List<BuildTaskDTO> list = JSONArray.parseArray(JSONArray.toJSONString(pageResponse.getData()), BuildTaskDTO.class);
        return PageResponse.success(list, pageResponse.getCount(), pageRequest.getPageNo(), pageRequest.getPageSize());
    }
}
