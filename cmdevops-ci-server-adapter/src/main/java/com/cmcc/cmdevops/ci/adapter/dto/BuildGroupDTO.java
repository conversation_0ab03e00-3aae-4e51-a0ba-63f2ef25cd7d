package com.cmcc.cmdevops.ci.adapter.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>Title: BuildGroupDTO</p>
 * <p>Description: </p>
 * <p>Copyright: Copyright (c) 2021</p>
 * <p>Company: SI-TECH </p>
 * Author by_csd_hlj
 * Version 1.0
 * CreateTime 2025/5/29 16:21
 */
@Data
@Accessors(chain = true)
public class BuildGroupDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer id;
    private String groupName;
    private String groupDescribe;
    private String groupSign;
    private String spaceId;
    private String tenantId;
    private Boolean deleted;
    private String createUid;
    private Date createTime;
    private String updateUid;
    private Date updateTime;
    private String deleteUid;
    private Date deleteTime;

    private List<String> businessDataId ;

}
