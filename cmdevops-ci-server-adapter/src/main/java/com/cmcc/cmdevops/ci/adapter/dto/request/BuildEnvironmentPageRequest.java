package com.cmcc.cmdevops.ci.adapter.dto.request;

import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.ci.adapter.dto.BuildEnvironmentDTO;

public class BuildEnvironmentPageRequest extends PageRequest {
    private BuildEnvironmentDTO buildEnvironmentDTO;

    public BuildEnvironmentDTO getBuildEnvironmentDTO() {
        return buildEnvironmentDTO;
    }

    public void setBuildEnvironmentDTO(BuildEnvironmentDTO buildEnvironmentDTO) {
        this.buildEnvironmentDTO = buildEnvironmentDTO;
    }
}
