package com.cmcc.cmdevops.ci.adapter.web;

import com.cmcc.cmdevops.BaseResponse;
import com.cmcc.cmdevops.ci.adapter.dto.BuildTemplateConverter;
import com.cmcc.cmdevops.ci.adapter.dto.BuildTemplateDTO;
import com.cmcc.cmdevops.ci.adapter.dto.request.BuildTemplateRequest;
import com.cmcc.cmdevops.ci.service.bo.BuildTemplateBO;
import com.cmcc.cmdevops.ci.service.business.BuildTemplateBizService;
import com.cmcc.cmdevops.ci.service.business.BuildTemplateFavoritesBizService;
import com.cmcc.cmdevops.ci.service.business.util.UserUtils;
import com.cmcc.cmdevops.exception.BusinessException;
import com.cmcc.cmdevops.util.BeanCloner;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 注意这是已经使用swagger3.0版本
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/v1/template")
public class BuildTemplateController {
    private static final Logger LOGGER = LoggerFactory.getLogger(BuildTemplateController.class);

    @Resource
    private BuildTemplateBizService buildTemplateService;

    @Resource
    private BuildTemplateFavoritesBizService buildTemplateFavoritesService;


    @PostMapping("/create")
    public BaseResponse<Void> create(@RequestBody BuildTemplateRequest request) {
        LOGGER.info("创建请求: {}", request);
        try {
          buildTemplateService.saveBuildTemplate(BuildTemplateConverter.convertToBO(request.getBuildTemplateDTO()));
        }catch (BusinessException e){
            return BaseResponse.fail("FAIL",e.getMessage());
        }
        return BaseResponse.success();
    }

    @PostMapping("/update")
    public BaseResponse<Void> update(@RequestBody BuildTemplateRequest request) {
        LOGGER.info("更新请求: {}", request);
        try {
            buildTemplateService.updateBuildTemplate(BuildTemplateConverter.convertToBO(request.getBuildTemplateDTO()));
        }catch (BusinessException e){
            return BaseResponse.fail("FAIL",e.getMessage());
        }return BaseResponse.success();
    }

    @PostMapping("/delete")
    public BaseResponse<Void> delete(@RequestBody BuildTemplateRequest request) {
        LOGGER.info("删除请求: {}", request);
        buildTemplateService.deleteBuildTemplate(BuildTemplateConverter.convertToBO(request.getBuildTemplateDTO()));
        return BaseResponse.success();
    }

    @GetMapping("/{id}")
    public BaseResponse<BuildTemplateDTO> getById(@PathVariable Integer id) {
        LOGGER.info("根据ID查询请求, ID: {}", id);
        BuildTemplateBO bo = buildTemplateService.getBuildTemplateById(id);
        return BaseResponse.success(BuildTemplateConverter.convertToDTO(bo));
    }

    @GetMapping("/type")
    public BaseResponse<List<BuildTemplateDTO>> queryTemplatesByType(
            @RequestParam Integer templateType,
            @RequestParam String spaceId) {
        LOGGER.info("按类型查询构建模板列表请求, templateType: {}", templateType);
        List<BuildTemplateBO> templateList = buildTemplateService.queryBuildTemplatesByType(templateType,spaceId);
        // 将BO列表转换为DTO列表
        List<BuildTemplateDTO> dtoList = templateList.stream()
                .map(BuildTemplateConverter::convertToDTO)
                .collect(java.util.stream.Collectors.toList());
        return BaseResponse.success(dtoList);
    }

    @GetMapping
    public BaseResponse<List<BuildTemplateDTO>> getAllTemplates(BuildTemplateRequest request) {
        LOGGER.info("获取所有构建模板请求");
        BuildTemplateBO templateBO = BeanCloner.clone(request.getBuildTemplateDTO(), BuildTemplateBO.class);
        List<BuildTemplateBO> templateList = buildTemplateService.getAllBuildTemplates(templateBO);
        // 将BO列表转换为DTO列表
        List<BuildTemplateDTO> dtoList = templateList.stream()
                .map(BuildTemplateConverter::convertToDTO)
                .collect(java.util.stream.Collectors.toList());
        return BaseResponse.success(dtoList);
    }

    /**
     * 收藏模板
     *
     * @param templateId 模板ID
     * @param spaceId    工作空间ID
     * @param tenantId   租户ID
     * @return 操作结果
     */
    @PostMapping("/favorite")
    public BaseResponse<Void> favoriteTemplate(
            @RequestParam Integer templateId,
            @RequestParam String spaceId) {
        String userId = UserUtils.getUserId();
        LOGGER.info("收藏模板请求, templateId: {}, userId: {}, spaceId: {}",
                templateId, userId, spaceId);
        buildTemplateFavoritesService.favoriteTemplate(templateId, userId, spaceId);
        return BaseResponse.success();
    }

    /**
     * 取消收藏模板
     *
     * @param templateId 模板ID
     * @return 操作结果
     */
    @DeleteMapping("/favorite")
    public BaseResponse<Void> unfavoriteTemplate(
            @RequestParam String templateId,
            @RequestParam String spaceId) {
        String userId = UserUtils.getUserId();
        LOGGER.info("取消收藏模板请求, templateId: {}, userId: {}, spaceId: {}",
                templateId, userId, spaceId);
        buildTemplateFavoritesService.unfavoriteTemplate(templateId, userId, spaceId);
        return BaseResponse.success();
    }

    /**
     * 获取用户收藏的所有模板
     *
     * @param spaceId  工作空间ID
     * @return 收藏的模板列表
     */
    @GetMapping("/favorites")
    public BaseResponse<List<BuildTemplateDTO>> getFavoriteTemplates(
            @RequestParam String spaceId) {
        String userId = UserUtils.getUserId();
        LOGGER.info("查询用户收藏模板请求, userId: {}, spaceId: {}",
                userId, spaceId);
        List<BuildTemplateBO> favoriteTemplates =
                buildTemplateFavoritesService.getFavoriteTemplates(userId, spaceId);
        List<BuildTemplateDTO> dtoList = favoriteTemplates.stream()
                .map(BuildTemplateConverter::convertToDTO)
                .collect(java.util.stream.Collectors.toList());
        return BaseResponse.success(dtoList);
    }
}
