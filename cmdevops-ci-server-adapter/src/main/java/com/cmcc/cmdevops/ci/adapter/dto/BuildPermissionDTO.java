package com.cmcc.cmdevops.ci.adapter.dto;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> @since 2025-05-20
 */
@Getter
@Setter
@Builder
@Accessors(chain = true)
public class BuildPermissionDTO implements Serializable {
    public BuildPermissionDTO() {
    }

    public BuildPermissionDTO(Integer id, String taskId, String userUid, String userName, String permissionCode, String spaceId, String tenantId, Boolean deleted, LocalDateTime createTime, String createUid, LocalDateTime updateTime, String updateUid, LocalDateTime deleteTime, String deleteUid, List<String> permissions) {
        this.id = id;
        this.taskId = taskId;
        this.userUid = userUid;
        this.userName = userName;
        this.permissionCode = permissionCode;
        this.spaceId = spaceId;
        this.tenantId = tenantId;
        this.deleted = deleted;
        this.createTime = createTime;
        this.createUid = createUid;
        this.updateTime = updateTime;
        this.updateUid = updateUid;
        this.deleteTime = deleteTime;
        this.deleteUid = deleteUid;
        this.permissions = permissions;
    }

    private static final long serialVersionUID = 1L;

    private Integer id;

    private String taskId;

    private String userUid;

    private String userName;

    private String permissionCode;

    private String spaceId;

    private String tenantId;

    private Boolean deleted;

    private LocalDateTime createTime;

    private String createUid;

    private LocalDateTime updateTime;

    private String updateUid;

    private LocalDateTime deleteTime;

    private String deleteUid;

    private List<String> permissions;
}
