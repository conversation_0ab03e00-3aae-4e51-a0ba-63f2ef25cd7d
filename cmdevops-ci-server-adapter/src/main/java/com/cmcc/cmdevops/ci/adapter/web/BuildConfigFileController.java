package com.cmcc.cmdevops.ci.adapter.web;

import com.cmcc.cmdevops.BaseResponse;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.adapter.dto.BuildConfigFileDTO;
import com.cmcc.cmdevops.ci.adapter.dto.BuildEnvironmentDTO;
import com.cmcc.cmdevops.ci.adapter.dto.request.BuildConfigFilePageRequest;
import com.cmcc.cmdevops.ci.adapter.dto.request.BuildEnvironmentPageRequest;
import com.cmcc.cmdevops.ci.service.bo.BuildConfigFileBO;
import com.cmcc.cmdevops.ci.service.bo.BuildEnvironmentBO;
import com.cmcc.cmdevops.ci.service.business.BuildConfigFileBizService;
import com.cmcc.cmdevops.util.BeanCloner;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * 注意这是已经使用swagger3.0版本
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/v1/config_file")
@RequiredArgsConstructor
public class BuildConfigFileController {
    private static final Logger LOGGER = LoggerFactory.getLogger(BuildConfigFileController.class);

    @Resource
    private BuildConfigFileBizService buildConfigFileBizService;

    @GetMapping("/page")
    public PageResponse<List<BuildConfigFileDTO>> page(BuildConfigFilePageRequest pageRequest){
        BuildConfigFileBO buildConfigFileBO = BeanCloner.clone(pageRequest.getBuildConfigFileDTO(), BuildConfigFileBO.class);
        PageResponse<List<BuildConfigFileBO>> pageResponse = buildConfigFileBizService.page(pageRequest, buildConfigFileBO);
        if (pageResponse == null) {
            return PageResponse.success(Collections.emptyList(), 0L, pageRequest.getPageNo(), pageRequest.getPageSize());
        }
        List<BuildConfigFileDTO> list = BeanCloner.clone(pageResponse.getData(), BuildConfigFileDTO.class);
        return PageResponse.success(list, pageResponse.getCount(), pageRequest.getPageNo(), pageRequest.getPageSize());
    }

    @PostMapping("/save")
    public BaseResponse<Void> save(@RequestBody BuildConfigFileDTO dto){
        BuildConfigFileBO buildConfigFileBO = BeanCloner.clone(dto, BuildConfigFileBO.class);
        buildConfigFileBizService.save(buildConfigFileBO);
        return BaseResponse.success();
    }

    @PutMapping("/update")
    public BaseResponse<Void> update(@RequestBody BuildConfigFileDTO dto){
        BuildConfigFileBO buildConfigFileBO = BeanCloner.clone(dto, BuildConfigFileBO.class);
        buildConfigFileBizService.update(buildConfigFileBO);
        return BaseResponse.success();
    }

    @DeleteMapping("/delete/{id}")
    public BaseResponse<Void> delete(@PathVariable String id){
        buildConfigFileBizService.delete(id);
        return BaseResponse.success();
    }

    @GetMapping("/detail/{id}")
    public BaseResponse<BuildConfigFileDTO> detail(@PathVariable String id){
        BuildConfigFileBO bo = buildConfigFileBizService.detail(id);
        BuildConfigFileDTO buildConfigFileDTO = BeanCloner.clone(bo, BuildConfigFileDTO.class);
        return BaseResponse.success(buildConfigFileDTO);
    }
}
