package com.cmcc.cmdevops.ci.adapter.dto.request;


import com.cmcc.cmdevops.BaseRequest;
import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.ci.adapter.dto.BuildTaskDTO;
import com.cmcc.cmdevops.ci.adapter.dto.BuildVersionDTO;

public class BuildVersionRequest extends PageRequest {
    private BuildVersionDTO buildVersionDTO;

    public BuildVersionDTO getBuildVersionDTO() {
        return buildVersionDTO;
    }

    public void setBuildVersionDTO(BuildVersionDTO buildVersionDTO) {
        this.buildVersionDTO = buildVersionDTO;
    }
}
