package com.cmcc.cmdevops.ci.adapter.dto;

import com.cmcc.cmdevops.ci.service.bo.BuildTemplateBO;
import com.cmcc.cmdevops.ci.service.business.util.EmptyValidator;
import com.cmcc.cmdevops.util.BeanCloner;

public class BuildTemplateConverter {
    public static BuildTemplateDTO convertToDTO(BuildTemplateBO bo) {
        return EmptyValidator.isNotEmpty(bo) ? BeanCloner.clone(bo, BuildTemplateDTO.class) : null;
    }

    public static BuildTemplateBO convertToBO(BuildTemplateDTO dto) {
        return EmptyValidator.isNotEmpty(dto) ? BeanCloner.clone(dto, BuildTemplateBO.class) : null;
    }
}
