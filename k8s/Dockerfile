# 构建阶段镜像，基于 euler-openjdk:17 安装 maven 双平面
# 此处不再使用两阶段构建，因为不能使用到构建缓存，先通过持续集成或者流水线创建一个maven打包任务，再进行镜像构建
#FROM artifactory.dep.devops.cmit.cloud:20101/tools/panji-base-images/euler-openjdk-maven:3.8.8 AS builder
#WORKDIR /build
#COPY .. .
#RUN mvn clean install -Dmaven.test.skip=true --settings ./.cmit/settings.xml -P default,dynamic,static,common

# 运行阶段镜像，基于 euler-openjdk:17 安装 maven 双平面
FROM artifactory.dep.devops.cmit.cloud:20101/tools/panji-base-images/euler-openjdk:17 AS final
WORKDIR /app
RUN mkdir /app/logs
#COPY --from=builder /build/cmdevops-ci-server-start/target/cmdevops-ci-server.jar .
COPY  /cmdevops-ci-server-start/target/cmdevops-ci-server.jar .
# 暴露端口
EXPOSE 8080
# 更新时区
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

# 按需修改，可以在k8s yaml文件里使用env覆盖
ENV MEM_OPTS="-Xms2g -Xmx2g -XX:MaxMetaspaceSize=256m"
ENV JAVA_OPTS="-XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UseStringDeduplication -XX:+DisableExplicitGC -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/tmp/heapdump.hprof -XX:+ExitOnOutOfMemoryError -Dfile.encoding=UTF-8 -Duser.timezone=Asia/Shanghai"

# 运行jar包
CMD java $MEM_OPTS $JAVA_OPTS -jar /app/cmdevops-ci-server.jar
