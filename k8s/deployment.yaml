apiVersion: apps/v1
kind: Deployment
metadata:
  name: cmdevops-ci-server
  namespace: cmdevops-ci
  labels:
    app: cmdevops-ci-server
    operation-source: api    #该资源负载以何种方式创建(web或者api)，通过web页面创建或者api创建
    paas-app-code: ${appCode}     #该资源所属的应用编码
    paas-app-service-version: v1        #应用服务版本
    paas-app-source: baseImage    #资源负载以何种手段创建：取固定值为“baseImage”，意为基础镜像部署
    paas-cluster-code: ${clusterId}    #资源负载部署的集群编码
    paas-env-code: ${envCode}    #该资源负载部署的环境编码
    paas-owner: ${username} #表征该资源负载的创建人
    paas-plane-code: ${planeCode}    #该资源负载部署的平面编码
    paas-resource-category: tenant-app    #取值固定为“tenant-app”，意为租户应用
    paas-system-code: cmdevops-ci        #该资源所属的系统编码（namespace）
    paas-tenant-code: ${tenantCode}    #该资源所属的租户编码，取当前账号所在租户的租户编码
    paas-unit-code: ${unitCode}    #该资源负载部署的单元编码
    paas-workload-name: cmdevops-ci-server    #资源负载的名称（即应用服务名称）
spec:
  replicas: 2
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: cmdevops-ci-server
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: cmdevops-ci-server
    spec:
      containers:
        - name: cmdevops-ci-server
          image: ${image}
          imagePullPolicy: Always
          env:
            - name: TZ
              value: Asia/Shanghai
            - name: NAMESPACE
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.namespace
            - name: POD_UID
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.uid
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.name
            - name: NACOS_CONFIG_SERVER_ADDR
              valueFrom:
                secretKeyRef:
                  name: cmdevops-ci-nacos-secret
                  key: nacos_config_server
            - name: NACOS_CONFIG_USERNAME
              valueFrom:
                secretKeyRef:
                  name: cmdevops-ci-nacos-secret
                  key: nacos_config_username
            - name: NACOS_CONFIG_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: cmdevops-ci-nacos-secret
                  key: nacos_config_password
          ports:
            - containerPort: 8080
              protocol: TCP
          resources:
            requests:
              cpu: "2"
              memory: 4Gi
            limits:
              cpu: "4"
              memory: 8Gi
          readinessProbe:
            httpGet:
              path: /actuator/health/readiness
              port: 8080
            initialDelaySeconds: 60
            periodSeconds: 30
            timeoutSeconds: 3
            failureThreshold: 3
          livenessProbe:
            httpGet:
              path: /actuator/health/liveness
              port: 8080
            initialDelaySeconds: 60
            periodSeconds: 30
            timeoutSeconds: 3
            failureThreshold: 3
          volumeMounts:
            - mountPath: /app/logs/app
              name: app-logs
              subPathExpr: ns/$(NAMESPACE)/pod/$(POD_NAME)/poduid/$(POD_UID)/cn/cmdevops-ci-server/app
            - mountPath: /app/logs/operation
              name: app-logs
              subPathExpr: ns/$(NAMESPACE)/pod/$(POD_NAME)/poduid/$(POD_UID)/cn/cmdevops-ci-server/operation
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      volumes:
        - hostPath:
            path: /tmp
            type: ""
          name: app-logs
---
apiVersion: v1
kind: Service
metadata:
  name: cmdevops-ci-server
  namespace: cmdevops-ci
  labels:
    svc: cmdevops-ci-server
    operation-source: api    #该资源负载以何种方式创建(web或者api)，通过web页面创建或者api创建
    paas-app-code: ${appCode}     #该资源所属的应用编码
    paas-app-source: baseImage    #资源负载以何种手段创建：取固定值为“baseImage”，意为基础镜像部署
    paas-cluster-code: ${clusterId}    #资源负载部署的集群编码
    paas-env-code: ${envCode}    #该资源负载部署的环境编码
    paas-owner: ${username} #表征该资源负载的创建人
    paas-plane-code: ${planeCode}    #该资源负载部署的平面编码
    paas-resource-category: tenant-app    #取值固定为“tenant-app”，意为租户应用
    paas-system-code: cmdevops-ci        #该资源所属的系统编码（namespace）
    paas-tenant-code: ${tenantCode}    #该资源所属的租户编码，取当前账号所在租户的租户编码
    paas-unit-code: ${unitCode}    #该资源负载部署的单元编码
    paas-workload-name: cmdevops-ci-server    #资源负载的名称（即应用服务名称）
spec:
  internalTrafficPolicy: Cluster
  ipFamilies:
    - IPv4
  ipFamilyPolicy: SingleStack
  ports:
    - port: 8080
      targetPort: 8080
      protocol: TCP
  selector:
    app: cmdevops-ci-server
  sessionAffinity: None
  type: ClusterIP
---
apiVersion: v1
kind: Secret
metadata:
  name: cmdevops-ci-nacos-secret
  namespace: cmdevops-ci
  labels:
    operation-source: api    #该资源负载以何种方式创建(web或者api)，通过web页面创建或者api创建
    paas-cluster-code: ${clusterId}    #资源负载部署的集群编码
    paas-owner: ${username} #表征该资源负载的创建人
    paas-resource-category: tenant-app    #取值固定为“tenant-app”，意为租户应用
data:
  nacos_config_username: bmFjb3M=
  nacos_config_password: bmFjb3NAMTAxNQ==
  nacos_config_server: ****************************
