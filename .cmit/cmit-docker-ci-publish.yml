#变量介绍☆☆☆☆☆
#${{env.PROJECT_PATH}}:代码根目录
#${{env.TAG}}:$(date +%s):从 1970 年 1 月 1 日 00:00:00 UTC 到目前为止的秒数（时间戳）

#名称：Docker镜像持续交付
name: Docker Image CI

#获取代码
on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

#开始工作
jobs:
  #获取镜像
  build:
    #镜像名称：cmit-docker-ci
    name: cmit-docker-ci
    #镜像版本：ubuntu-latest
    runs-on: ${{env.RUNTIME_IMAGE}}
    #执行步骤
    steps:
      #切换分支
      - uses: actions/checkout@v2
      #设置镜像仓库☆☆☆☆☆☆☆☆☆☆修改为当前工程使用的'镜像库/工程名'☆☆☆☆☆☆☆☆☆☆
      - name: set image repository
        run: echo "IMAGE_REPO=harbor.dep.devops.cmit.cloud:20030/devops_doc/devops-doc:2024.01.01.sp02_${GIT_COMMIT_ID:0:7}" | tee -a $GITHUB_ENV ${{env.PROJECT_PATH}}/env
      #打印环境变量，用于调试
      - name: echo env
        run: echo GITHUB_ENV=$GITHUB_ENV, RUNTIME_IMAGE=${{env.RUNTIME_IMAGE}}, PROJECT_PATH=${{env.PROJECT_PATH}}, IMAGE_REPO=${{env.IMAGE_REPO}}, USER_NAME=${{env.USER_NAME}}
      #登录镜像库
      - name: docker login
        run: docker login harbor.dep.devops.cmit.cloud:20030 -u ${{env.USER_NAME}} -p ${{env.PASSWORD}}
      #制作镜像☆☆☆☆☆☆☆☆☆☆进入代码仓库根目录制作镜像Dockerfile需要根据项目工程进行修改，使用脚本库、本地仓库构建时请使用@@DockerfilePath@@为默认注入地址☆☆☆☆☆☆☆☆☆☆
      - name: Build the Docker image
        run: cd ${{env.PROJECT_PATH}} && docker build . --file @@DockerfilePath@@ --tag ${{env.IMAGE_REPO}}
      #推送镜像
      - name: docker push
        run: docker push ${{env.IMAGE_REPO}}
