# 数据库变更管理

## 目录结构
```
db-migration/
├── 2025.01.01/                                             # 版本1：基础表结构
│   └── 0001_2025.01.01_xxx-demo_product_info_init.sql
│   └── 0002_2025.01.01_xxx-demo_order_info_init.sql
├── 2025.02.01/                                             # 版本2：权限相关表
│   └── 0003_2025.02.01_add_permission_tables.sql
├── 2025.02.02/                                             # 版本3：操作日志表
│   └── 0004_2025.02.02_add_operation_log.sql
└── README.md                                               # 说明文档
```

## SQL文件命名规范
SQL命名规范优化为：序号_版本号_模块名_sql描述.sql ，示例:  
```
0001_2024.01.01_security_init.sql
0002_2024.01.01_security_production-legacy-migrate.sql
```
说明： 
- 模块名为每个模块固定分配
- 描述使用小写字母和横岗分隔

## SQL文件变更规则
1. 序号必须全局递增
2. 任何正式发版出去的SQL不允许做任何修改，对已发版SQL的更新必须要走新增SQL的方式更新（比如新增一个SQL alter某字段长度）
3. 每个版本的SQL尽量写在一个文件内
4. SQL文件必须包含完整的变更说明注释，必须包含字段注释，必须包含表注释
5. 所有表名和字段名使用小写字母
6. 必须包含主键和必要的索引
7. 必须指定字符集为utf8mb4

## 表设计规范
- 合理的设计索引，遵循“按需添加”引入原则。缺失的索引会导致查询性能的大幅下降，过多的索引又会导致写入行的大幅下降。
- 单表行数超过千万级别且有业务查询时，要开始考虑优化查询性能。优先考虑使用分布式数据库分表机制或集群式数据库PG分区表实现分表，而不是业务自己用代码去实现逻辑分表。
- 禁止使用触发器和外键。触发器让部分业务逻辑散落在数据库上，相比业务代码更难维护；外键的引入不利于实现逻辑删除，不利于按单表作数据备份和迁移，可以用业务代码实现数据的关联合法性校验。
- 命名规范：
    - 命名：表名、字段名、序列名、索引名、视图名必须使用小写字母或数字，禁止出现数字开头，禁止两个下划线中间只出现数字
    - 表命名： 格式为 模块名_表功能说明，名词**使用单数**而不使用复数。如：账号模块的用户基本信息表： account_user_info
    - 序列命名：格式为 seq_表名_字段名去掉下划线，如在 account_user_info 表的id字段上增加序列，命名为 seq_account_user_info_id
    - 索引命名：格式为 idx_索引字段1去掉下划线_索引字段2去掉下划线，如 在user_id字段上加索引，索引命名为 idx_userid, 在user_id和user_name上加联合索引，索引命名为 idx_userid_username
    - 视图命名：格式为 view_模块名_功能说明，如 view_account_user_detail
- 主键：所有表必须有主键且保持统一，如下所列：
    - id：主键，BIGINT类型，自增
- 租户隔离：所有跟租户有关的表都要有租户隔离字段，且字段名保持统一，如下所列：
    - tenant_id:varchar(20)
- 逻辑删除：原则上有业务数据删除的表都使用逻辑删除，而不直接物理删除。逻辑删除字段命名要保持统一，如下所列：
    - deleted:bool true-已删除，false-未删除
- 审计字段：原则上所有表都要有创建、修改、删除相关审计字段，除非该表没有修改、删除的可能性，可以没有修改、删除相关审计字段。所有表审计字段命名要保持统一，如下所列：
    - 创建审计字段：create_time:timestamp(6)、create_uid:varchar(255)
    - 修改审计字段：update_time:timestamp(6)、update_uid:varchar(255)
    - 删除审计字段：delete_time:timestamp(6)、delete_uid:varchar(255)

## 注意事项
1. 任何字段变更都要新增sql变更文件，禁止在已发布的sql文件上直接修改
2. 新增字段必须指定默认值或允许为空
3. 变更表结构必须考虑数据迁移的影响
4. 重要变更需要添加回滚脚本 
