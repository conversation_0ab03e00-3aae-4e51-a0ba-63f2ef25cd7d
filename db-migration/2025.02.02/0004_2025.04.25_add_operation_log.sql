-- 删除表（如果存在）
DROP TABLE IF EXISTS operation_log;

-- 创建操作日志表
CREATE TABLE operation_log (
    id BIGSERIAL PRIMARY KEY,  -- 自增主键（对应 MySQL 的 BIGINT AUTO_INCREMENT）
    user_id BIGINT,            -- 操作用户ID（允许 NULL）
    username VARCHAR(50),      -- 操作用户名（允许 NULL）
    operation_type VARCHAR(50) NOT NULL,  -- 操作类型（非空）
    operation_desc VARCHAR(500),          -- 操作描述（允许 NULL）
    operation_method VARCHAR(200),        -- 请求方法（允许 NULL）
    operation_params TEXT,                -- 请求参数（允许 NULL）
    operation_result TEXT,                -- 操作结果（允许 NULL）
    operation_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,  -- 操作时间（自动填充，含时区）
    operation_ip VARCHAR(50),             -- 操作IP（允许 NULL）
    operation_location VARCHAR(200),      -- 操作地点（允许 NULL）
    operation_browser VARCHAR(200),       -- 操作浏览器（允许 NULL）
    operation_os VARCHAR(200),            -- 操作系统（允许 NULL）
    status SMALLINT NOT NULL DEFAULT 1,   -- 状态：0-失败，1-成功（使用 SMALLINT 节省空间）
    error_msg TEXT                        -- 错误信息（允许 NULL）
);

-- 添加字段注释
COMMENT ON TABLE operation_log IS '操作日志表';
COMMENT ON COLUMN operation_log.id IS '主键ID';
COMMENT ON COLUMN operation_log.user_id IS '操作用户ID';
COMMENT ON COLUMN operation_log.username IS '操作用户名';
COMMENT ON COLUMN operation_log.operation_type IS '操作类型';
COMMENT ON COLUMN operation_log.operation_desc IS '操作描述';
COMMENT ON COLUMN operation_log.operation_method IS '请求方法';
COMMENT ON COLUMN operation_log.operation_params IS '请求参数';
COMMENT ON COLUMN operation_log.operation_result IS '操作结果';
COMMENT ON COLUMN operation_log.operation_time IS '操作时间';
COMMENT ON COLUMN operation_log.operation_ip IS '操作IP';
COMMENT ON COLUMN operation_log.operation_location IS '操作地点';
COMMENT ON COLUMN operation_log.operation_browser IS '操作浏览器';
COMMENT ON COLUMN operation_log.operation_os IS '操作系统';
COMMENT ON COLUMN operation_log.status IS '状态：0-失败，1-成功';
COMMENT ON COLUMN operation_log.error_msg IS '错误信息';

-- 添加索引
CREATE INDEX idx_user_id ON operation_log (user_id);         -- 用户ID索引
CREATE INDEX idx_operation_time ON operation_log (operation_time);  -- 操作时间索引
