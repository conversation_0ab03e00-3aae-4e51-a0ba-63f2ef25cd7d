
-- DROP TABLE pipline.build_group;

CREATE TABLE pipline.build_group (
     id varchar(64) NOT NULL,
     group_name varchar(255) NULL DEFAULT NULL::character varying,
     group_describe varchar(255) NULL DEFAULT NULL::character varying,
     group_sign varchar(255) NULL DEFAULT NULL::character varying,
     space_id varchar(255) NULL DEFAULT NULL::character varying,
     tenant_id varchar(255) NULL DEFAULT NULL::character varying,
     deleted bool NULL DEFAULT false,
     create_uid varchar(255) NULL DEFAULT NULL::character varying,
     create_time timestamp(6) NULL DEFAULT NULL::timestamp without time zone,
     update_uid varchar(255) NULL DEFAULT NULL::character varying,
     update_time timestamp(6) NULL DEFAULT NULL::timestamp without time zone,
     delete_uid varchar(255) NULL DEFAULT NULL::character varying,
     delete_time timestamp(6) NULL DEFAULT NULL::timestamp without time zone,
     CONSTRAINT build_group_pkey PRIMARY KEY (id)
)
WITH (
    orientation=row,
    compression=no
);




-- DROP TABLE pipline.build_group_relation;

CREATE TABLE pipline.build_group_relation (
     id varchar(64) NOT NULL,
     business_data_id varchar(255) NULL DEFAULT NULL::character varying,
     group_id varchar(255) NULL DEFAULT NULL::character varying,
     tenant_id varchar(255) NULL DEFAULT NULL::character varying,
     deleted bool NULL DEFAULT false,
     create_uid varchar(255) NULL DEFAULT NULL::character varying,
     create_time timestamp(6) NULL DEFAULT NULL::timestamp without time zone,
     update_uid varchar(255) NULL DEFAULT NULL::character varying,
     update_time timestamp(6) NULL DEFAULT NULL::timestamp without time zone,
     delete_uid varchar(255) NULL DEFAULT NULL::character varying,
     delete_time timestamp(6) NULL DEFAULT NULL::timestamp without time zone,
     CONSTRAINT build_group_relation_pkey PRIMARY KEY (id)
)
WITH (
    orientation=row,
    compression=no
);
