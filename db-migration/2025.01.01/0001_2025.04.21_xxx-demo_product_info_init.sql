-- 删除表（如果存在）
DROP TABLE IF EXISTS demo_product_info;

-- 创建商品表
CREATE TABLE demo_product_info (
    product_id BIGINT NOT NULL PRIMARY KEY,  -- 商品ID（无符号长整型）
    product_name VARCHAR(100) NOT NULL,      -- 商品名称
    price DECIMAL(10, 2) NOT NULL,           -- 商品价格（精确到小数点后两位）
    stock INT NOT NULL DEFAULT 0 CHECK (stock >= 0),  -- 库存数量（非负，默认0）
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP  -- 创建时间（自动填充）
);

-- 添加字段注释
COMMENT ON TABLE demo_product_info IS '示例商品信息表';
COMMENT ON COLUMN demo_product_info.product_id IS '商品ID';
COMMENT ON COLUMN demo_product_info.product_name IS '商品名称';
COMMENT ON COLUMN demo_product_info.price IS '商品价格';
COMMENT ON COLUMN demo_product_info.stock IS '库存数量';
COMMENT ON COLUMN demo_product_info.created_time IS '创建时间';
