-- 构建任务表
DROP TABLE IF EXISTS ci.build_task;
CREATE TABLE ci.build_task (
   id varchar(64) NOT NULL,
   task_name varchar(200) NULL DEFAULT NULL::character varying,
   build_status varchar(16) NULL DEFAULT NULL::character varying,
   build_number varchar(10) NULL DEFAULT NULL::character varying,
   last_build_id varchar(64) NULL DEFAULT NULL::character varying,
   last_build_time timestamp NULL,
   has_disable bool NULL DEFAULT false, --- 是否禁用
   space_id varchar(64) NULL DEFAULT NULL::character varying,
   tenant_id varchar(64) NULL DEFAULT NULL::character varying, --- 租户ID
   deleted bool,
   create_time timestamp(6),
   create_uid varchar(255),
   update_time timestamp(6),
   update_uid varchar(255),
   delete_time timestamp(6),
   delete_uid varchar(255),
   CONSTRAINT build_task_pkey PRIMARY KEY (id)
);

-- 代码配置表
DROP TABLE IF EXISTS ci.build_code_config;
DROP SEQUENCE IF EXISTS ci.build_code_config_id_seq;
CREATE SEQUENCE ci.build_code_config_id_seq
    START 1
    INCREMENT 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;
CREATE TABLE ci.build_code_config (
    id INT DEFAULT nextval('ci.build_code_config_id_seq') PRIMARY KEY,
	task_id varchar(64) NULL DEFAULT NULL::character varying, --- 任务ID
    vcs_id varchar(2048) NULL DEFAULT NULL::character varying, --- 代码库地址
    vcs_name varchar(2048) NULL DEFAULT NULL::character varying, --- 代码库地址
	vcs_repository varchar(2048) NULL DEFAULT NULL::character varying, --- 代码库地址
	vcs_branch varchar(255) NULL DEFAULT NULL::character varying, --- 代码库地址
	vcs_clone_type varchar(255) NULL DEFAULT NULL::character varying, --- 代码拉取类型
    vcs_submodule bool NULL,  --- 是否使用子模块
    space_id varchar(64) NULL DEFAULT NULL::character varying,
    tenant_id varchar(64) NULL DEFAULT NULL::character varying, --- 租户ID
    deleted bool NULL DEFAULT false, --- 公共字段
    create_time timestamp(6),
    create_uid varchar(255),
    update_time timestamp(6),
    update_uid varchar(255),
    delete_time timestamp(6),
    delete_uid varchar(255)
);
CREATE INDEX seq_build_code_config_task_id ON ci.build_code_config (task_id);

-- 构建步骤配置表
DROP TABLE IF EXISTS ci.build_step_config;
DROP SEQUENCE IF EXISTS ci.build_step_config_id_seq;
CREATE SEQUENCE ci.build_step_config_id_seq
    START 1
    INCREMENT 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;
CREATE TABLE ci.build_step_config (
    id INT DEFAULT nextval('ci.build_step_config_id_seq') PRIMARY KEY,
	task_id varchar(64) NULL,
	name varchar(64) NULL,
    type varchar(64) NULL, -- 步骤类型
	serial int4 NULL,
	config text NULL, --- 步骤配置
    has_open bool NULL, --- 是否开启
    space_id varchar(64) NULL DEFAULT NULL::character varying,
    tenant_id varchar(64) NULL DEFAULT NULL::character varying, --- 租户ID
    deleted bool NULL DEFAULT false, --- 公共字段
    create_time timestamp(6),
    create_uid varchar(255),
    update_time timestamp(6),
    update_uid varchar(255),
    delete_time timestamp(6),
    delete_uid varchar(255)
);
CREATE INDEX seq_build_step_config_task_id ON ci.build_step_config (task_id);


-- 构建触发器配置表
DROP TABLE IF EXISTS ci.build_trigger_config;
DROP SEQUENCE IF EXISTS ci.build_trigger_config_id_seq;
CREATE SEQUENCE ci.build_trigger_config_id_seq
    START 1
    INCREMENT 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;
CREATE TABLE ci.build_trigger_config (
    id INT DEFAULT nextval('ci.build_trigger_config_id_seq') PRIMARY KEY,
	task_id varchar(64) NULL,
    code_trigger_switch bool NULL,
    code_trigger_event varchar(255) DEFAULT NULL ,
    code_trigger_filter varchar(255) DEFAULT NULL ,
    time_trigger_switch bool NULL,
    time_trigger_crontab varchar(255) DEFAULT NULL ,
    time_trigger_code_change_time_trigger_switch bool NULL,
    space_id varchar(64) NULL DEFAULT NULL::character varying,
    tenant_id varchar(64) NULL DEFAULT NULL::character varying, --- 租户ID
    deleted bool NULL DEFAULT false, --- 公共字段
    create_time timestamp(6),
    create_uid varchar(255),
    update_time timestamp(6),
    update_uid varchar(255),
    delete_time timestamp(6),
    delete_uid varchar(255)
);
CREATE INDEX seq_build_trigger_config_task_id ON ci.build_trigger_config (task_id);

-- 构建参数表
DROP TABLE IF EXISTS ci.build_parameter;
DROP SEQUENCE IF EXISTS ci.build_parameter_id_seq;
CREATE SEQUENCE ci.build_parameter_id_seq
    START 1
    INCREMENT 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;
CREATE TABLE ci.build_parameter (
	id INT DEFAULT nextval('ci.build_parameter_id_seq') PRIMARY KEY,
	task_id varchar(64) NULL DEFAULT NULL::character varying,
	param_name varchar(255) NULL DEFAULT NULL::character varying,
	param_type int4 NULL,
	param_value text NULL,
	param_value_type int4 NULL,
	param_options text NULL,
	param_discrib text NULL,
	is_open bool NULL DEFAULT true,
	is_running bool NULL DEFAULT false,
	is_encrypt bool NULL DEFAULT false,
	space_id varchar(64) NULL DEFAULT NULL::character varying,
	tenant_id varchar(64) NULL DEFAULT NULL::character varying,
	deleted bool NULL DEFAULT false,
	create_time timestamp NULL,
	create_uid varchar(255) NULL,
	update_time timestamp NULL,
	update_uid varchar(255) NULL,
	delete_time timestamp NULL,
	delete_uid varchar(255) NULL
);
CREATE INDEX seq_build_parameter_task_id ON ci.build_parameter (task_id);

-- 构建缓存表
DROP TABLE IF EXISTS ci.build_cache;
DROP SEQUENCE IF EXISTS ci.build_cache_id_seq;
CREATE SEQUENCE ci.build_cache_id_seq
    START 1
    INCREMENT 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;
CREATE TABLE ci.build_cache (
    id INT DEFAULT nextval('ci.build_cache_id_seq') PRIMARY KEY,
	task_id varchar(64) NULL DEFAULT NULL::character varying, --- 任务ID
    directory varchar(200) NULL DEFAULT NULL::character varying, --- 目录
    describ varchar(200) NULL DEFAULT NULL::character varying, --- 描述
    has_open bool NULL, --- 是否开启
    space_id varchar(64) NULL DEFAULT NULL::character varying,
    tenant_id varchar(64) NULL DEFAULT NULL::character varying, --- 租户ID
    deleted bool NULL DEFAULT false, --- 公共字段
    create_time timestamp(6),
    create_uid varchar(255),
    update_time timestamp(6),
    update_uid varchar(255),
    delete_time timestamp(6),
    delete_uid varchar(255)
);
CREATE INDEX seq_build_cache_task_id ON ci.build_cache (task_id);

-- 构建高级配置表
DROP TABLE IF EXISTS ci.build_advanced_config;
DROP SEQUENCE IF EXISTS ci.build_advanced_config_id_seq;
CREATE SEQUENCE ci.build_advanced_config_id_seq
    START 1
    INCREMENT 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;
CREATE TABLE ci.build_advanced_config (
	id INT DEFAULT nextval('ci.build_advanced_config_id_seq') PRIMARY KEY,
	task_id varchar(64) NULL DEFAULT NULL::character varying,
	assigned_node INT NULL,
	overtime int2 NULL,
	space_id varchar(64) NULL DEFAULT NULL::character varying,
	tenant_id varchar(64) NULL DEFAULT NULL::character varying,
	deleted bool NULL DEFAULT false,
	create_time timestamp(6) NULL,
	create_uid varchar(255) NULL,
	update_time timestamp(6) NULL,
	update_uid varchar(255) NULL,
	delete_time timestamp(6) NULL,
	delete_uid varchar(255) NULL
);
CREATE INDEX seq_build_advanced_config_task_id ON ci.build_advanced_config (task_id);

-- 构建版本表
DROP TABLE IF EXISTS ci.build_version;
DROP SEQUENCE IF EXISTS ci.build_version_id_seq;
CREATE SEQUENCE ci.build_version_id_seq
    START 1
    INCREMENT 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;
CREATE TABLE ci.build_version (
    id INT DEFAULT nextval('ci.build_version_id_seq') PRIMARY KEY,
	task_id varchar(64) NULL DEFAULT NULL::character varying, --- 任务ID
	build_version_context text NULL, --- 构建版本记录
    space_id varchar(64) NULL DEFAULT NULL::character varying,
    tenant_id varchar(64) NULL DEFAULT NULL::character varying, --- 租户ID
    deleted bool NULL DEFAULT false, --- 公共字段
    create_time timestamp(6),
    create_uid varchar(255),
    update_time timestamp(6),
    update_uid varchar(255),
    delete_time timestamp(6),
    delete_uid varchar(255)
);
CREATE INDEX seq_build_version_task_id ON ci.build_version (task_id);


-- 构建脚本表
DROP TABLE IF EXISTS ci.build_script;
DROP SEQUENCE IF EXISTS ci.build_script_id_seq;
CREATE SEQUENCE ci.build_script_id_seq
    START 1
    INCREMENT 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;
CREATE TABLE ci.build_script (
    id INT DEFAULT nextval('ci.build_script_id_seq') PRIMARY KEY,
    build_script_name varchar(200) NULL DEFAULT NULL::character varying, --- 脚本名称
    build_script_context text NULL DEFAULT NULL::character varying, --- 脚本内容
    space_id varchar(64) NULL DEFAULT NULL::character varying,
    tenant_id varchar(64) NULL DEFAULT NULL::character varying, --- 租户ID
    deleted bool NULL DEFAULT false, --- 公共字段
    create_time timestamp(6),
    create_uid varchar(255),
    update_time timestamp(6),
    update_uid varchar(255),
    delete_time timestamp(6),
    delete_uid varchar(255)
);


-- 构建快照表
DROP TABLE IF EXISTS ci.build_snapshot;
CREATE TABLE ci.build_snapshot (
	id varchar(64) NOT NULL,
	task_id varchar(64) NULL DEFAULT NULL::character varying,
	build_status varchar(16) NULL DEFAULT NULL::character varying,
	build_number int4 NULL DEFAULT NULL, --- 构建次数
    duration int4 NULL DEFAULT NULL, --- 耗时
    start_time timestamp(6) NULL DEFAULT NULL, --- 开始时间
    end_time timestamp(6) NULL DEFAULT NULL, --- 结束时间
	space_id varchar(64) NULL DEFAULT NULL::character varying,
	tenant_id varchar(64) NULL DEFAULT NULL::character varying,
    source varchar(16) NULL DEFAULT NULL::character varying,
	deleted bool NULL DEFAULT false,
	create_time timestamp(6) NULL,
	create_uid varchar(255) NULL,
	update_time timestamp(6) NULL,
	update_uid varchar(255) NULL,
	delete_time timestamp(6) NULL,
	delete_uid varchar(255) NULL,
	CONSTRAINT build_snapshot_pkey PRIMARY KEY (id)
);
CREATE INDEX seq_build_snapshot_task_id ON ci.build_snapshot (task_id);



-- 定时构建快照表
DROP TABLE IF EXISTS ci.build_schedule_snapshot;
CREATE TABLE ci.build_schedule_snapshot (
    id varchar(64) NOT NULL PRIMARY KEY,
    ci_schedule_url varchar(255) NULL DEFAULT NULL::character varying,
    ci_tool_url  varchar(255) NULL DEFAULT NULL::character varying,
    job_xml text NULL DEFAULT NULL::character varying, --- 脚本内容
    user_name varchar(255) NULL DEFAULT NULL::character varying,
    token varchar(255) NULL DEFAULT NULL::character varying,
    params text NULL DEFAULT NULL::character varying, --- 脚本内容
    tenant_id varchar(64) NULL DEFAULT NULL::character varying,
    create_time timestamp(6) NULL,
    create_uid varchar(255) NULL
);

-- 构建代码快照表
DROP TABLE IF EXISTS ci.build_code_snapshot;
DROP SEQUENCE IF EXISTS ci.build_code_snapshot_id_seq;
CREATE SEQUENCE ci.build_code_snapshot_id_seq
    START 1
    INCREMENT 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;
CREATE TABLE ci.build_code_snapshot (
    id INT DEFAULT nextval('ci.build_code_snapshot_id_seq') PRIMARY KEY,
	task_id varchar(64) NULL DEFAULT NULL::character varying, --- 任务ID
    build_snapshot_id varchar(64) NULL DEFAULT NULL::character varying, --- 记录ID
    vcs_id varchar(2048) NULL DEFAULT NULL::character varying, --- 代码库地址
    vcs_name varchar(2048) NULL DEFAULT NULL::character varying, --- 代码库地址
	vcs_repository varchar(2048) NULL DEFAULT NULL::character varying,
	vcs_branch varchar(255) NULL DEFAULT NULL::character varying,
	vcs_clone_type varchar(255) NULL DEFAULT NULL::character varying, --- 代码拉取类型
    vcs_submodule bool NULL,  --- 是否使用子模块
    commit_id varchar(255) NULL DEFAULT NULL::character varying,
	commit_message text NULL, --- 代码变更记录
    change_message text NULL, --- 代码变更记录
	space_id varchar(64) NULL DEFAULT NULL::character varying,
    tenant_id varchar(64) NULL DEFAULT NULL::character varying, --- 租户ID
    deleted bool NULL DEFAULT false, --- 公共字段
    create_time timestamp(6),
    create_uid varchar(255),
    update_time timestamp(6),
    update_uid varchar(255),
    delete_time timestamp(6),
    delete_uid varchar(255)
);
CREATE INDEX seq_build_code_snapshot_task_id ON ci.build_code_snapshot (task_id);
CREATE INDEX seq_build_code_snapshot_build_snapshot_id ON ci.build_code_snapshot (build_snapshot_id);


-- 步骤快照表
DROP TABLE IF EXISTS ci.build_step_snapshot;
DROP SEQUENCE IF EXISTS ci.build_step_snapshot_id_seq;
CREATE SEQUENCE ci.build_step_snapshot_id_seq
    START 1
    INCREMENT 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;
CREATE TABLE ci.build_step_snapshot (
    id INT DEFAULT nextval('ci.build_step_snapshot_id_seq') PRIMARY KEY,
	task_id varchar(64) NULL,
    build_snapshot_id varchar(64) NULL DEFAULT NULL::character varying, --- 记录ID
	name varchar(64) NULL,
    type varchar(64) NULL, -- 步骤类型
	serial int4 NULL,
	config text NULL, --- 步骤配置
    has_open bool NULL, --- 是否开启
    step_state varchar(64) NULL, --- 步骤执行状态
    step_result varchar(64) NULL, --- 步骤执行结果
    start_time timestamp(6) NULL DEFAULT NULL, --- 开始时间
    end_time timestamp(6) NULL DEFAULT NULL, --- 结束时间
    duration int4 NULL DEFAULT NULL, --- 耗时
    step_log text NULL, --- 步骤日志，存300行
    step_log_path varchar(200) NULL DEFAULT NULL::character varying, --- 步骤日志路径
    step_status varchar(16) NULL DEFAULT NULL::character varying, --- 步骤状态
    space_id varchar(64) NULL DEFAULT NULL::character varying,
    tenant_id varchar(64) NULL DEFAULT NULL::character varying, --- 租户ID
    deleted bool NULL DEFAULT false, --- 公共字段
    create_time timestamp(6),
    create_uid varchar(255),
    update_time timestamp(6),
    update_uid varchar(255),
    delete_time timestamp(6),
    delete_uid varchar(255)
);
CREATE INDEX seq_build_step_snapshot_task_id ON ci.build_step_snapshot (task_id);
CREATE INDEX seq_build_step_snapshot_build_snapshot_id ON ci.build_step_snapshot (build_snapshot_id);



-- 构建产物表
DROP TABLE IF EXISTS ci.build_artifact;
DROP SEQUENCE IF EXISTS ci.build_artifact_id_seq;
CREATE SEQUENCE ci.build_artifact_id_seq
    START 1
    INCREMENT 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;
CREATE TABLE ci.build_artifact (
    id INT DEFAULT nextval('ci.build_artifact_id_seq') PRIMARY KEY,
	task_id varchar(64) NULL DEFAULT NULL::character varying, --- 任务ID
    build_snapshot_id varchar(64) NULL DEFAULT NULL::character varying, --- 记录ID
    build_number varchar(10) NULL DEFAULT NULL::character varying, --- 构建号
    build_version varchar(200) NULL DEFAULT NULL::character varying, --- 构建号
    build_artifact_name varchar(200) NULL DEFAULT NULL::character varying, --- 构建产物名称
    build_artifact_real_name varchar(200) NULL DEFAULT NULL::character varying, --- 构建产物名称
    build_artifact_path varchar(200) NULL DEFAULT NULL::character varying, --- 构建产物路径
    build_artifact_version varchar(200) NULL DEFAULT NULL::character varying, --- 构建产物名称
    build_artifact_url varchar(200) NULL DEFAULT NULL::character varying, --- 构建产物地址
    build_artifact_type varchar(200) NULL DEFAULT NULL::character varying, --- 构建产物类型
    repository_name varchar(200) NULL DEFAULT NULL::character varying, --- 仓库名称
	space_id varchar(64) NULL DEFAULT NULL::character varying,
    tenant_id varchar(64) NULL DEFAULT NULL::character varying, --- 租户ID
    deleted bool NULL DEFAULT false, --- 公共字段
    create_time timestamp(6),
    create_uid varchar(255),
    update_time timestamp(6),
    update_uid varchar(255),
    delete_time timestamp(6),
    delete_uid varchar(255)
);
CREATE INDEX seq_build_artifact_task_id ON ci.build_artifact (task_id);
CREATE INDEX seq_build_artifact_build_snapshot_id ON ci.build_artifact (build_snapshot_id);


-- 参数快照表
DROP TABLE IF EXISTS ci.build_param_snapshot;
DROP SEQUENCE IF EXISTS ci.build_param_snapshot_id_seq;
CREATE SEQUENCE ci.build_param_snapshot_id_seq
    START 1
    INCREMENT 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;
CREATE TABLE ci.build_param_snapshot (
	id INT DEFAULT nextval('ci.build_param_snapshot_id_seq') PRIMARY KEY,
	task_id varchar(64) NULL DEFAULT NULL::character varying,
	param_name varchar(255) NULL DEFAULT NULL::character varying,
	param_type int4 NULL,
	param_value text NULL,
	param_value_type int4 NULL,
	param_options text NULL,
	param_discrib text NULL,
	is_open bool NULL DEFAULT true,
	is_running bool NULL DEFAULT false,
	is_encrypt bool NULL DEFAULT false,
	space_id varchar(64) NULL DEFAULT NULL::character varying,
	tenant_id varchar(64) NULL DEFAULT NULL::character varying,
	deleted bool NULL DEFAULT false,
	create_time timestamp NULL,
	create_uid varchar(255) NULL,
	update_time timestamp NULL,
	update_uid varchar(255) NULL,
	delete_time timestamp NULL,
	delete_uid varchar(255) NULL,
	build_snapshot_id varchar(255) NULL
);
CREATE INDEX seq_build_param_snapshot_task_id ON ci.build_param_snapshot (task_id);


-- 构建环境表
DROP TABLE IF EXISTS ci.build_environment;
DROP SEQUENCE IF EXISTS ci.build_environment_id_seq;
CREATE SEQUENCE ci.build_environment_id_seq
    START 1
    INCREMENT 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;
CREATE TABLE ci.build_environment (
	id INT DEFAULT nextval('ci.build_environment_id_seq') PRIMARY KEY,
	environment_name varchar(200) DEFAULT NULL::character varying,
	environment_type varchar(16) DEFAULT NULL::character varying,
	environment_desc text DEFAULT NULL::character varying,
	region varchar(16) DEFAULT NULL::character varying,
	environment_url varchar(200) DEFAULT NULL::character varying,
	environment_config text DEFAULT NULL::character varying,
	is_use_cache bool,
	cache_dir varchar(200) DEFAULT NULL::character varying,
	access_key varchar(200) DEFAULT NULL::character varying,
	secret_key varchar(200) DEFAULT NULL::character varying,
	space_id varchar(64) DEFAULT NULL::character varying,
	tenant_id varchar(64) DEFAULT NULL::character varying,
	deleted bool DEFAULT false,
	create_time timestamp,
	create_uid varchar(255),
	update_time timestamp,
	update_uid varchar(255),
	delete_time timestamp,
	delete_uid varchar(255)
);


-- Jenkins节点表
DROP TABLE IF EXISTS ci.build_jenkins_node;
DROP SEQUENCE IF EXISTS ci.build_jenkins_node_id_seq;
CREATE SEQUENCE ci.build_jenkins_node_id_seq
    START 1
    INCREMENT 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;
CREATE TABLE ci.build_jenkins_node (
    id INT DEFAULT nextval('ci.build_jenkins_node_id_seq') PRIMARY KEY,
    name varchar(200) NULL DEFAULT NULL::character varying, --- 环境名称
    type varchar(10) NULL DEFAULT NULL::character varying, --- HOST/KUBENETES
    node_status varchar(10) NULL DEFAULT NULL::character varying, --- 节点状态
    node_start_time timestamp NULL,
    node_url varchar(200) NULL DEFAULT NULL::character varying, --- 节点地址/如果是主机需要暴露端口
    build_environment_id varchar(64) NOT NULL,
    space_id varchar(64) NULL DEFAULT NULL::character varying,
    tenant_id varchar(64) NULL DEFAULT NULL::character varying, --- 租户ID
    deleted bool NULL DEFAULT false, --- 公共字段
    create_time timestamp(6),
    create_uid varchar(255),
    update_time timestamp(6),
    update_uid varchar(255),
    delete_time timestamp(6),
    delete_uid varchar(255)
);


-- 构建工具表
DROP TABLE IF EXISTS ci.build_tool;
CREATE TABLE ci.build_tool (
    id varchar(255) NOT NULL,
    tool_name varchar(255) DEFAULT NULL,
    tool_image varchar(255) DEFAULT NULL,
    args varchar(1024) DEFAULT NULL,
    type varchar(255) DEFAULT NULL,
    CONSTRAINT build_tool_pkey PRIMARY KEY (id)
);


-- 构建节点表
DROP TABLE IF EXISTS ci.build_node;
DROP SEQUENCE IF EXISTS ci.build_node_id_seq;
CREATE SEQUENCE ci.build_node_id_seq
    START 1
    INCREMENT 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;
CREATE TABLE ci.build_node (
    id INT DEFAULT nextval('ci.build_node_id_seq') PRIMARY KEY,
    build_environment_id INT NOT NULL,
    build_node_path varchar(200) NULL DEFAULT NULL::character varying, --- 构建节点路径
    build_node_type varchar(200) NULL DEFAULT NULL::character varying, --- 构建节点类型
    build_node_name varchar(200) NULL DEFAULT NULL::character varying, --- 构建节点名称
    build_node_status varchar(200) NULL DEFAULT NULL::character varying, --- 构建节点状态
    space_id varchar(64) NULL DEFAULT NULL::character varying,
    tenant_id varchar(64) NULL DEFAULT NULL::character varying, --- 租户ID
    deleted bool NULL DEFAULT false, --- 公共字段
    create_time timestamp(6),
    create_uid varchar(255),
    update_time timestamp(6),
    update_uid varchar(255),
    delete_time timestamp(6),
    delete_uid varchar(255)
);


-- 构建分组表
DROP TABLE IF EXISTS ci.build_group;
DROP SEQUENCE IF EXISTS ci.build_group_id_seq;
CREATE SEQUENCE ci.build_group_id_seq
    START 1
    INCREMENT 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;
CREATE TABLE ci.build_group (
	id INT DEFAULT nextval('ci.build_group_id_seq') PRIMARY KEY,
	group_name varchar(255) NULL DEFAULT NULL::character varying,
	group_describe varchar(255) NULL DEFAULT NULL::character varying,
	group_sign varchar(255) NULL DEFAULT NULL::character varying,
	space_id varchar(255) NULL DEFAULT NULL::character varying,
	tenant_id varchar(255) NULL DEFAULT NULL::character varying,
	deleted bool NULL DEFAULT false,
	create_uid varchar(255) NULL DEFAULT NULL::character varying,
	create_time timestamp(6) NULL DEFAULT NULL::timestamp without time zone,
	update_uid varchar(255) NULL DEFAULT NULL::character varying,
	update_time timestamp(6) NULL DEFAULT NULL::timestamp without time zone,
	delete_uid varchar(255) NULL DEFAULT NULL::character varying,
	delete_time timestamp(6) NULL DEFAULT NULL::timestamp without time zone
);


-- 分组关联表
DROP TABLE IF EXISTS ci.build_group_relation;
DROP SEQUENCE IF EXISTS ci.build_group_relation_id_seq;
CREATE SEQUENCE ci.build_group_relation_id_seq
    START 1
    INCREMENT 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;
CREATE TABLE ci.build_group_relation (
	id INT DEFAULT nextval('ci.build_group_relation_id_seq') PRIMARY KEY,
	business_data_id varchar(255) NULL DEFAULT NULL::character varying,
	group_id INT NULL DEFAULT NULL::character varying,
	tenant_id varchar(255) NULL DEFAULT NULL::character varying,
	deleted bool NULL DEFAULT false,
	create_uid varchar(255) NULL DEFAULT NULL::character varying,
	create_time timestamp(6) NULL DEFAULT NULL::timestamp without time zone,
	update_uid varchar(255) NULL DEFAULT NULL::character varying,
	update_time timestamp(6) NULL DEFAULT NULL::timestamp without time zone,
	delete_uid varchar(255) NULL DEFAULT NULL::character varying,
	delete_time timestamp(6) NULL DEFAULT NULL::timestamp without time zone
);


-- 构建模板表
DROP TABLE IF EXISTS ci.build_template;
DROP SEQUENCE IF EXISTS ci.build_template_id_seq;
CREATE SEQUENCE ci.build_template_id_seq
    START 1
    INCREMENT 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;
CREATE TABLE ci.build_template (
	id INT DEFAULT nextval('ci.build_template_id_seq') PRIMARY KEY,
	title varchar(255) NULL,
	template_describe text NULL,
	icon varchar(255) NULL,
	build_config text NULL,
	template_type int4 NULL,
	space_id varchar(255) NULL,
	template_group varchar(255) NULL,
	tenant_id varchar(255) NULL,
	deleted bool NULL DEFAULT false,
	create_uid varchar(255) NULL,
	create_time timestamp(6) NULL,
	update_uid varchar(255) NULL,
	update_time timestamp(6) NULL,
	delete_uid varchar(255) NULL,
	delete_time timestamp(6) NULL
);



-- 构建模板收藏表
DROP TABLE IF EXISTS ci.build_template_favorites;
DROP SEQUENCE IF EXISTS ci.build_template_favorites_id_seq;
CREATE SEQUENCE ci.build_template_favorites_id_seq
    START 1
    INCREMENT 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;
CREATE TABLE ci.build_template_favorites (
	id INT DEFAULT nextval('ci.build_template_favorites_id_seq') PRIMARY KEY,
	user_id varchar(36) NOT NULL,
	template_id INT NOT NULL,
	space_id varchar(255) NULL,
	tenant_id varchar(255) NULL,
	deleted bool NULL DEFAULT false,
	create_time timestamp NULL,
	update_time timestamp NULL
);
CREATE UNIQUE INDEX build_template_favorites_user_id_template_id_key ON ci.build_template_favorites (user_id, template_id);
