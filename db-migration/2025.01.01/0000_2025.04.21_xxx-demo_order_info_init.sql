-- 删除表（如果存在）
DROP TABLE IF EXISTS demo_order_info;

-- 创建订单表
-- 创建订单表
CREATE TABLE demo_order_info (
    order_id BIGSERIAL PRIMARY KEY,  -- 自增主键（对应 MySQL 的 BIGINT）
    user_id INT NOT NULL,            -- 用户ID
    product_id INT NOT NULL,         -- 商品ID
    quantity INT NOT NULL,           -- 购买数量
    total_amount DECIMAL(10, 2) NOT NULL,  -- 订单总金额
    status VARCHAR(20) DEFAULT 'pending',  -- 订单状态（默认 pending）
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP  -- 创建时间（自动填充）
);

-- 添加字段注释
COMMENT ON COLUMN demo_order_info.order_id IS '订单ID';
COMMENT ON COLUMN demo_order_info.user_id IS '用户ID';
COMMENT ON COLUMN demo_order_info.product_id IS '商品ID';
COMMENT ON COLUMN demo_order_info.quantity IS '购买数量';
COMMENT ON COLUMN demo_order_info.total_amount IS '订单总金额';
COMMENT ON COLUMN demo_order_info.status IS '订单状态（如 pending/paid/cancelled）';
COMMENT ON COLUMN demo_order_info.created_time IS '创建时间';

-- 添加表注释（可选）
COMMENT ON TABLE demo_order_info IS '示例订单信息表';
