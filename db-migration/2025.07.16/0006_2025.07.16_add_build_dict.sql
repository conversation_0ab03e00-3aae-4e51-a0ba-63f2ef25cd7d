-- 字典脚本表
DROP TABLE IF EXISTS ci.build_dict;
DROP SEQUENCE IF EXISTS ci.build_dict_id_seq;
CREATE SEQUENCE ci.build_dict_id_seq
    START 1
    INCREMENT 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;
CREATE TABLE ci.build_dict (
   id INT DEFAULT nextval('ci.build_dict_id_seq') PRIMARY KEY,
   dict_name varchar(200) NULL DEFAULT NULL::character varying, --- 字典名称
   dict_key text NULL DEFAULT NULL::character varying, --- 字段键
   dict_value varchar(64) NULL DEFAULT NULL::character varying --- 字段值
);

INSERT INTO ci.build_dict(dict_name, dict_key, dict_value) VALUES('jdk8镜像名称', 'jdk8', '8.0.392-tem');
INSERT INTO ci.build_dict(dict_name, dict_key, dict_value) VALUES('jdk11镜像名称', 'jdk11', '11.0.21-tem');
INSERT INTO ci.build_dict(dict_name, dict_key, dict_value) VALUES('jdk17镜像名称', 'jdk17', '17.0.9-tem');
INSERT INTO ci.build_dict(dict_name, dict_key, dict_value) VALUES('jdk21镜像名称', 'jdk21', '21.0.2-tem');