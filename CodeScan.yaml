# 扫描java项目, CodeScan.yaml应与pom.xml/build.gradle在同一目录
# java maven项目:
#   - maven
#   - maven-cmd
# java gradle项目:
#   - gradle
#   - gradle-cmd
---
approaches: # 启用哪些扫描方式(以下列出了全部可选项，根据项目实际的构建方式可以选择部分)
  # 扫描java maven项目使用以下配置，如果是扫描gradle项目需删除
  - maven
  - maven-cmd

environments: # 配置项目构建时的环境变量(可选，根据实际需要设置)
  JAVA_HOME: '${jdk17_home}'     #  如果待扫描应用是jdk17 请配置该属性，如果待扫描应用是jdk8 不需要配置该属性，需删除。

java: # java扫描参数
  mvn_cmd_options: ["-s", ".cmit/settings.xml"] # 如果是maven项目该属性值为：项目自定义settings.xml文件在该项目中的相对路径，如果是gradle项目删除该配置

force: false # 是否忽略检测错误并强制输出结果
