apiVersion: v1
kind: Pod
metadata:
  name: jenkins-slave
  labels:
    app: "jenkins-slave"
spec:
  hostAliases:
    - ip: "**************"
      hostnames:
        - "sgyf-luchi-pod1-core-157-199"
    - ip: "**************"
      hostnames:
        - "sgyf-luchi-pod1-core-157-200"
  containers:
    - name: jnlp
      image: jenkins-slave-image
      imagePullPolicy: Always
      resources:
        limits:
          cpu: '8'
          memory: 8Gi
        requests:
          cpu: 400m
          memory: 2048Mi
      securityContext:
        runAsUser: 0
        privileged: true
      volumeMounts:
        - name: config
          mountPath: /etc/buildkitd.toml
          subPath: buildkitd.toml
        - name: config
          mountPath: /etc/docker/daemon.json
          subPath: daemon.json

  volumes:
    - name: config
      configMap:
        defaultMode: 420
        items:
          - key: buildkitd.toml
            path: buildkitd.toml
          - key: daemon.json
            path: daemon.json
        name: inbound-agent-config
