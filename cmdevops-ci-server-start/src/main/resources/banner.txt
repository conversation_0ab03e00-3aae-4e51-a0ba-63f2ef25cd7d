${AnsiColor.BRIGHT_BLUE}

  ______ .___  ___.  _______   ___________    ____  ______   .______     _______.
 /      ||   \/   | |       \ |   ____\   \  /   / /  __  \  |   _  \   /       |
|  ,----'|  \  /  | |  .--.  ||  |__   \   \/   / |  |  |  | |  |_)  | |   (----`
|  |     |  |\/|  | |  |  |  ||   __|   \      /  |  |  |  | |   ___/   \   \
|  `----.|  |  |  | |  '--'  ||  |____   \    /   |  `--'  | |  |   .----)   |
 \______||__|  |__| |_______/ |_______|   \__/     \______/  | _|   |_______/

${AnsiColor.BRIGHT_GREEN}:: Spring Boot ::${AnsiColor.DEFAULT}      v${spring-boot.version}
${AnsiColor.BRIGHT_YELLOW}:: Application ::${AnsiColor.DEFAULT}      ${spring.application.name}
${AnsiColor.BRIGHT_CYAN}:: Java Version ::${AnsiColor.DEFAULT}     ${java.version}
${AnsiColor.BRIGHT_MAGENTA}:: Git branch ::${AnsiColor.DEFAULT}       ${git.branch}
${AnsiColor.BRIGHT_MAGENTA}:: Git version ::${AnsiColor.DEFAULT}      ${git.commit.id.abbrev}
${AnsiColor.BRIGHT_MAGENTA}:: Git Commit time ::${AnsiColor.DEFAULT}  ${git.commit.time}
${AnsiColor.BRIGHT_MAGENTA}:: Git build time::${AnsiColor.DEFAULT}    ${git.build.time}
