middleware:
  db:
    driver-class-name: org.postgresql.Driver
    database: cmdevops
    master:
      hosts: ************:17700
    slave:
      hosts: ************:17700
    ci:
      username: devops
      password: <PERSON><PERSON>(gqewFSf81CBdusAv2wW1m0mZIhIPw6D2)
  redis:
    sentinel-master: mymaster
    sentinel-nodes: cmdevops-redis-sentinel.cmdevops-middleware:26379
    sentinel-password: ENC(yxFM2iJGtwkYn9Cebb50SeJ8obS527sl)
  s3:
    access-key: "8OoR4PMcv8227GdT"
    secret-key: "FAuY6Ln3Vn7oiAks1UnujK1QjDVyk7kl"
    internal-endpoint: "http://************:9000"
    external-endpoint: "http://************:9000"
  kafka:
    host: cmdevops-kafka.cmdevops-middleware:9092
  rocketmq:
#    name-server: cmdevops-rocketmq.cmdevops-middleware:9876
    name-server: *************:9876
  powerjob:
    host: ***********:30126

global:
  env-tag: cmdevops-dev
  moss:
    auth:
      secret: e4cx7KtxnvWAh9U3R4kLNVnd/fYL5hSNFjhOecH16XAYZgGJupLTzsg25lpTWkWypELsA3mTZ02mRlwXzyZ1kg==

url:
  platform:
    internal-endpoint: ""
    external-endpoint: ""
  openapi:
    internal-endpoint: "http://rdcloud.4c-uat.hq.cmcc:20030"
    external-endpoint: ""

management:
  otlp:
    metrics:
      export:
        url: http://otel-agent.paas-monitor:4318

ci:
  external-api:
    dev: true


powerjob:
  worker:
    enabled: true
    enable-test-mode: false
    app-name: cmdevops-ci-server
    port: 27777
    protocol: http
    store-strategy: disk
    max-result-length: 4096
    max-appended-wf-context-length: 4096
    max-lightweight-task-num: 1024
    max-heavy-task-num: 64
    password: cmdevops-ci-server
