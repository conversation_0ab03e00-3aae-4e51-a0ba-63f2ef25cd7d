{"success": true, "code": "", "message": "", "data": [{"id": "1", "name": "cmdevops-ci-schedule-server", "path": "", "note": "", "codeGroupId": 0, "gitProjectId": 0, "gitGroupId": 0, "lastUpdated": "yyyy-MM-dd HH:mm:ss", "language": "", "createTime": "", "updateTime": "", "createdBy": "", "updatedBy": "", "tenantId": "", "systemName": "", "systemId": 0, "apps": [{"appId": 0, "appName": ""}]}, {"id": "2", "name": "cmdevops-ci-server", "path": "", "note": "", "codeGroupId": 0, "gitProjectId": 0, "gitGroupId": 0, "lastUpdated": "yyyy-MM-dd HH:mm:ss", "language": "", "createTime": "", "updateTime": "", "createdBy": "", "updatedBy": "", "tenantId": "", "systemName": "", "systemId": 0, "apps": [{"appId": 0, "appName": ""}]}, {"id": "cmdevops-frontend-flow", "name": "cmdevops-frontend-flow", "path": "", "note": ""}, {"id": "ruby", "name": "ruby", "path": "", "note": ""}, {"id": "maven-jdk8", "name": "maven-jdk8", "path": "", "note": ""}, {"id": "maven-jdk17", "name": "maven-jdk17", "path": "", "note": ""}, {"id": "ant-jdk8", "name": "ant-jdk8", "path": "", "note": ""}, {"id": "gradle-jdk8", "name": "gradle-jdk8", "path": "", "note": ""}, {"id": "gradle-jdk17", "name": "gradle-jdk17", "path": "", "note": ""}, {"id": "python", "name": "python", "path": "", "note": ""}, {"id": "cmake", "name": "cmake", "path": "", "note": ""}, {"id": "lua", "name": "lua", "path": "", "note": ""}, {"id": "rust", "name": "rust", "path": "", "note": ""}, {"id": "scala", "name": "scala", "path": "", "note": ""}, {"id": "p4", "name": "p4", "path": "", "note": ""}, {"id": "android", "name": "android", "path": "", "note": ""}, {"id": "php", "name": "php", "path": "", "note": ""}, {"id": "go", "name": "go", "path": "", "note": ""}, {"id": "androidTest", "name": "androidTest", "path": "", "note": ""}, {"id": "gulp", "name": "gulp", "path": "", "note": ""}, {"id": "grunt", "name": "grunt", "path": "", "note": ""}, {"id": "npm", "name": "npm", "path": "", "note": ""}], "pageNo": 0, "pageSize": 0, "count": 0, "pageCount": 0}