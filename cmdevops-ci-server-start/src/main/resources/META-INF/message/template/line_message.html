<!DOCTYPE html>
<html lang=en xmlns="http://www.w3.org/1999/html">
<head>
    <meta charset=UTF-8>
    <title>Title</title>
</head>
<body>
<p>hi，您收到一条来易享-云流水线的操作通知！</p>
<p>&nbsp;</p>
<p><strong>通知内容：</strong></p>
<p><strong>项目名称：</strong> &nbsp;${pipelineProject.name}&nbsp;</p>
<p><strong>流水线名称：</strong> &nbsp;${lineTaskDTO.taskName}&nbsp;</p>
<p><strong>执行人：</strong> ${lineExecuteLogDTO.userCreate}</p>
<p><strong>结果：</strong>
    #if($lineExecuteLogDTO.executeStatus == 1)
    成功
    #elseif($lineExecuteLogDTO.executeStatus == 2)
    失败
    #end
</p>
<p><strong>流水线源信息：</strong></p>
#foreach ( $ele in $lineTaskDTO.lineSourceCodes )
<p style="padding-left: 20px;">代码仓库： ${ele.repoName}</p>
#end
<p><strong>代码变更：</strong></p>
#if( !$lineCodeChangeDTOList || $lineCodeChangeDTOList.size() == 0 )
<p>没有提交记录</p>
#else
#foreach ( $ele in $lineCodeChangeDTOList )
#if( !$ele )
<p>没有提交记录</p>
#else
<p style="padding-left: 20px;"><strong>仓库：</strong> ${ele.gitRepoName}</p>
<p style="padding-left: 20px;"><strong>分支：</strong> ${ele.branch}</p>
#if( $ele.commit && $ele.commit.size() > 0 )
<p style="padding-left: 20px;"><strong>代码提交记录：</strong></p>
#set( $counter = 1 )
#foreach ( $commit in $ele.commit )
<p style="padding-left: 40px;">${counter}. 提交人：${commit.authorName} (${commit.authorEmail})，提交消息：${commit.message}，<a href="${commit.webUrl}">链接:${commit.shortId}</a></p>
#set( $counter = $counter + 1 )
#end
#else
<p style="padding-left: 20px;">没有提交记录</p>
#end
#end
#end
#end
<p><strong>查看链接：</strong> <a href="${email}/e-sharecloud/pipeline/project/${pipelineProject.id}/lineList/lineDetail?lineId=${lineTaskDTO.id}">${email}/e-sharecloud/pipeline/project/${pipelineProject.id}/lineList/lineDetail?lineId=${lineTaskDTO.id}</a></p>
<p>请点击此处进入系统查看详情</p>
<p>如已知晓，请忽略！</p>
</body>
</html>