<flow-definition plugin="workflow-job@2.40">
    <actions/>
    <description/>
    <keepDependencies>false</keepDependencies>
    <properties>
        <hudson.plugins.jira.JiraProjectProperty plugin="jira@3.1.3"/>
        <org.jenkinsci.plugins.workflow.job.properties.DisableConcurrentBuildsJobProperty/>
        <jenkins.model.BuildDiscarderProperty>
            <strategy class="hudson.tasks.LogRotator">
                <daysToKeep>-1</daysToKeep>
                <numToKeep>10</numToKeep>
                <artifactDaysToKeep>-1</artifactDaysToKeep>
                <artifactNumToKeep>-1</artifactNumToKeep>
            </strategy>
        </jenkins.model.BuildDiscarderProperty>
        <hudson.model.ParametersDefinitionProperty>
            <parameterDefinitions>
                #foreach ( $ele in $paramList )
                <hudson.model.StringParameterDefinition>
                    <name>$ele.paramName</name>
                    <description></description>
                    <defaultValue>$ele.paramValue</defaultValue>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                #end
            </parameterDefinitions>
        </hudson.model.ParametersDefinitionProperty>
    </properties>
    <definition class="org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition" plugin="workflow-cps@2.86">
        <script><![CDATA[${pipelineShell}]]></script>
        <sandbox>true</sandbox>
    </definition>
    <triggers/>
    <quietPeriod>0</quietPeriod>
    <disabled>false</disabled>
</flow-definition>
