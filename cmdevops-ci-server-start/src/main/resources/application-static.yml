spring:
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  datasource:
    master:
      type: com.zaxxer.hikari.HikariDataSource
      driver-class-name: ${middleware.db.driver-class-name}
      jdbcUrl: jdbc:postgresql://${middleware.db.master.hosts}/paas?currentSchema=ci&useSSL=false&serverTimezone=UTC&useUnicode=true&characterEncoding=utf-8&rewritebatchedstatements=true&useAffectedRows=true&allowPublicKeyRetrieval=true
      username: ${middleware.db.ci.username}
      password: ${middleware.db.ci.password}
      hikari:
        connection-timeout: 30000
        idle-timeout: 60000
        keepaliveTime: 75000
        max-lifetime: 120000
        minimum-idle: 2
        maximum-pool-size: 20
        pool-name: masterPool
    slave:
      type: com.zaxxer.hikari.HikariDataSource
      driver-class-name: ${middleware.db.driver-class-name}
      jdbcUrl: jdbc:postgresql://${middleware.db.master.hosts}/paas?currentSchema=ci&useSSL=false&serverTimezone=UTC&useUnicode=true&characterEncoding=utf-8&rewritebatchedstatements=true&useAffectedRows=true&allowPublicKeyRetrieval=true
      username: ${middleware.db.ci.username}
      password: ${middleware.db.ci.password}
      hikari:
        connection-timeout: 30000
        idle-timeout: 60000
        keepaliveTime: 75000
        max-lifetime: 120000
        minimum-idle: 2
        maximum-pool-size: 20
        pool-name: slavePool

apiGw:
  client:
    appKey: app_continuous_integration_ms_1749520691103
    appSecret: app_b7GDkjp8erBP79-OX2xEFEzO7U9hiLl1XP8Ml3AktD8
    gatewayUrl:
      versions: v1
      v1: ${url.openapi.internal-endpoint}

# 以下组件按需要修改enabled配置来启用/禁用
component:
  auth:
    filterUrlPatterns: # 哪些请求需要经过认证的Filter，获取并检查用户、租户ID
      - /cmdevops-ci/server/*
    tenant:
      enabled: true
      auto-fill:
        sql: none # 可选值：true(检查sql是否包含tenant_id，并自动增加tenant_id) false(只检查tenant_id) none(不检查也不自动增加tenant_id)
      ignoreUrls: # 默认情况下，每个请求需要带上 tenant-id 的请求头，无需租户的请求配置在这里
        - /cmdevops-ci/server/openapi/**
      ignoreTables: # 默认所有表都开启多租户的功能，不需要的配置在这里
        - demo_example1
    moss:
      enabled: true
      ignoreUrls:
        - /cmdevops-ci/server/openapi/**
        - /cmdevops-ci/server/api/build/downArtifact
#  rocketmq:
#    namesrvAddr: ${middleware.rocketmq.name-server}  # NameServer 地址，集群使用';'隔开
#    producerGroup: pipeline-producer-group  # 生产者组名称，改为自己服务的名称
#    sendMsgTimeout: 3000
#    retryTimesWhenSendFailed: 3
#    consumerGroup: pipeline-execution-consumer-group
#    auth:
#      access-key: ${middleware.rocketmq.access-key:}
#      secret-key: ${middleware.rocketmq.secret-key:}
  oss:
    factory: s3
    s3:
      endpoint: ${middleware.s3.internal-endpoint}
      access-key: ${middleware.s3.access-key}
      secret-key: ${middleware.s3.secret-key}
      bucket: build-cache
      connect-timeout: 60000
      read-timeout: 120000
      write-timeout: 180000

ci-tool:
  schedule-image: ***********:20200/devops-test/cmdevops-ci-schedule-server:master
  tool-image: ***********:20200/devops/jenkins:devops-k8s
  slave-image: ***********:20200/devops/jenkins-slave:v2
  binfmt-image: ***********:20200/devops/tonistiigi/binfmt
  buildkit-image: ***********:20200/devops/moby/buildkit:buildx-stable-1
  ci-tool-cm:
    buildkitdToml: |
      debug = true
      [registry."***********:20200"]
        http = true
        insecure = true

    daemonJson: |
      {
          "insecure-registries": ["***********:20200"],
          "bip":"*************/24"
      }


rocketmq:
  name-server: ${middleware.rocketmq.name-server}  # NameServer 地址，集群使用';'隔开
  producerGroup: pipeline-producer-group  # 生产者组名称，改为自己服务的名称
  sendMsgTimeout: 3000
  retryTimesWhenSendFailed: 3
  consumerGroup: pipeline-execution-consumer-group
  auth:
    access-key: ${middleware.rocketmq.access-key:}
    secret-key: ${middleware.rocketmq.secret-key:}
