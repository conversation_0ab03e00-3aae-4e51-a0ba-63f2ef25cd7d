services:
  # icecream调度器节点
  icecc-scheduler:
    image: icecream-centos7:20250930
    container_name: icecc-scheduler
    hostname: icecc-scheduler
    ports:
      - "10245:10245/tcp"
      - "8765:8765/tcp"
      - "8766:8766/tcp"
      - "8765:8765/udp"
    networks:
      - icecream-net
    command:
      - /bin/sh
      - -c
      - |
        echo "启动icecream调度器..."
        icecc-scheduler -u nobody -l /tmp/icecc-scheduler.log -vvv --persistent-client-connection
    restart: unless-stopped
    
  # icecream工作节点1
  icecc-worker-1:
    image: icecream-centos7:20250930_v3
    container_name: icecc-worker-1
    hostname: icecc-worker-1
    depends_on:
      - icecc-scheduler
    networks:
      - icecream-net
    command:
      - /bin/sh
      - -c
      - |
        echo "等待调度器启动..."
        sleep 5
        echo "启动icecream工作节点1..."
        iceccd -u nobody -d -s icecc-scheduler:8765 -l /tmp/icecc-worker.log -vvv
        tail -f /dev/null
    restart: unless-stopped
    
  # icecream工作节点2
  icecc-worker-2:
    image: icecream-centos7:20250930_v3
    container_name: icecc-worker-2
    hostname: icecc-worker-2
    depends_on:
      - icecc-scheduler
    networks:
      - icecream-net
    command:
      - /bin/sh
      - -c
      - |
        echo "等待调度器启动..."
        sleep 8
        echo "启动icecream工作节点2..."
        iceccd -u nobody -d -s icecc-scheduler:8765 -l /tmp/icecc-worker.log -vvv
        tail -f /dev/null
    restart: unless-stopped

networks:
  icecream-net:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
