<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.cmcc.cmdevops.ci</groupId>
        <artifactId>cmdevops-ci-server</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>cmdevops-ci-server-service</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.cmcc.cmdevops.ci</groupId>
            <artifactId>cmdevops-ci-server-dal</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cmcc.cmdevops.component</groupId>
            <artifactId>oss-springboot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cmcc.cmdevops.component</groupId>
            <artifactId>readwrite-springboot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId>
            <version>2.3</version>
        </dependency>
        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
            <version>1.10.1</version>
        </dependency>
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
            <version>8.5.7</version> <!-- 使用最新版本 -->
        </dependency>
        <!-- https://mvnrepository.com/artifact/io.kubernetes/client-java -->
        <dependency>
            <groupId>io.kubernetes</groupId>
            <artifactId>client-java</artifactId>
            <version>22.0.1</version>
        </dependency>
        <dependency>
            <groupId>io.kubernetes</groupId>
            <artifactId>client-java-api</artifactId>
            <version>22.0.1</version>
        </dependency>
        <dependency>
            <groupId>io.kubernetes</groupId>
            <artifactId>client-java-proto</artifactId>
            <version>22.0.1</version>
        </dependency>
        <dependency>
            <groupId>tech.powerjob</groupId>
            <artifactId>powerjob-worker-spring-boot-starter</artifactId>
            <version>4.3.9</version>
        </dependency>
        <dependency>
            <groupId>tech.powerjob</groupId>
            <artifactId>powerjob-client</artifactId>
            <version>4.3.9</version>
        </dependency>
        <dependency>
            <groupId>tech.powerjob</groupId>
            <artifactId>powerjob-worker</artifactId>
            <version>4.3.9</version>
        </dependency>
        <dependency>
            <groupId>com.cmcc.cmdevops.component</groupId>
            <artifactId>rocketmq-springboot-starter</artifactId>
        </dependency>
    </dependencies>

</project> 
