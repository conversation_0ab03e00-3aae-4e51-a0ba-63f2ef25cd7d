package com.cmcc.cmdevops.ci.job.config;

import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import tech.powerjob.worker.PowerJobSpringWorker;
import tech.powerjob.worker.common.PowerJobWorkerConfig;
import tech.powerjob.worker.common.constants.StoreStrategy;

@Configuration
@ConditionalOnProperty(name = "powerjob.worker.enabled", havingValue = "true")
public class PowerJobWorkerConfiguration {
    private Logger logger = LoggerFactory.getLogger(PowerJobWorkerConfiguration.class);

    @Value("${powerjob.worker.app-name}")
    private String appName;

    @Value("${middleware.powerjob.host}")
    private String serverAddress;

    @Value("${powerjob.worker.port}")
    private Integer port;

    @Bean
    public PowerJobSpringWorker initPowerJobWorker() {
        // 1. 创建配置文件
        PowerJobWorkerConfig config = new PowerJobWorkerConfig();
        config.setPort(port);
        config.setAppName(appName);

        if (StringUtils.isNotEmpty(serverAddress) && serverAddress.contains("http://")){
            serverAddress = serverAddress.replace("http://","");
        }
        config.setServerAddress(Lists.newArrayList(serverAddress));
        // 如果没有大型 Map/MapReduce 的需求，建议使用内存来加速计算
        config.setStoreStrategy(StoreStrategy.MEMORY);

        // 2. 创建 Worker 对象，设置配置文件（注意 Spring 用户需要使用 PowerJobSpringWorker，而不是 PowerJobWorker）
        PowerJobSpringWorker worker = new PowerJobSpringWorker(config);
        return worker;
    }
}
