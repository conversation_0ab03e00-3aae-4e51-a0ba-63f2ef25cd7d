package com.cmcc.cmdevops.ci.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cmcc.cmdevops.ci.service.bo.BuildTriggerConfigBO;
import com.cmcc.cmdevops.ci.service.business.BuildTaskTriggerService;
import com.cmcc.cmdevops.ci.service.business.BuildTriggerConfigBizService;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import tech.powerjob.client.PowerJobClient;
import tech.powerjob.common.enums.ExecuteType;
import tech.powerjob.common.enums.ProcessorType;
import tech.powerjob.common.enums.TimeExpressionType;
import tech.powerjob.common.request.http.SaveJobInfoRequest;
import tech.powerjob.common.response.JobInfoDTO;
import tech.powerjob.common.response.ResultDTO;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class BuildTaskTriggerServiceImpl implements BuildTaskTriggerService {

    private Logger logger = LoggerFactory.getLogger(BuildTaskTriggerServiceImpl.class);

    @Value("${powerjob.worker.app-name}")
    private String appName;

    @Value("${middleware.powerjob.host}")
    private String serverAddress;

    @Value("${powerjob.worker.password}")
    private String password;

    @Resource
    private BuildTriggerConfigBizService buildTriggerConfigBizService;

    @Override
    public void createJob(String taskId, String cron) {
        logger.info("开始创建构建定时执行任务，taskId：{}，cron：{}", taskId, cron);
        if (StringUtils.isNotEmpty(serverAddress) && serverAddress.contains("http://")){
            serverAddress = serverAddress.replace("http://","");
        }
        PowerJobClient client = new PowerJobClient(serverAddress, appName, password);
        // 查询任务是否存在
        ResultDTO<List<JobInfoDTO>> result = client.fetchAllJob();
        if (!result.isSuccess() || result.getData() == null) {
            throw new RuntimeException("获取任务列表失败: " + result.getMessage());
        }
        JobInfoDTO existingJob = result.getData().stream()
                .filter(job -> taskId.equals(job.getJobName()))
                .findFirst()
                .orElse(null);
        SaveJobInfoRequest jobInfo = new SaveJobInfoRequest();
        //jobId，任务 ID，可选，null 代表创建任务，否则填写需要修改的任务 ID
        if (existingJob != null) {
            logger.info("任务已存在，开始更新任务，任务ID：{}", existingJob.getId());
            jobInfo.setId(existingJob.getId()); // 设置为更新
        }else{
            jobInfo.setId(null);
        }
        //jobName，任务名称
        jobInfo.setJobName(taskId);
        //jobDescription，任务描述
        jobInfo.setJobDescription(taskId);
        //jobParams，任务参数，Processor#process 方法入参TaskContext对象的jobParams字段
        JSONObject param = new JSONObject();
        param.put("taskId", taskId);
        jobInfo.setJobParams(JSON.toJSONString(param));
        //timeExpressionType，时间表达式类型，枚举值
        jobInfo.setTimeExpressionType(TimeExpressionType.CRON);
        //timeExpression，时间表达式，填写类型由 timeExpressionType 决定，比如 CRON 需要填写 CRON 表达式
        jobInfo.setTimeExpression(cron);
        //executeType执行配置，单机执行STANDALONE
        jobInfo.setExecuteType(ExecuteType.STANDALONE);
        //processorType处理器类型，内建BUILT_IN
        jobInfo.setProcessorType(ProcessorType.BUILT_IN);
        //processorInfo全限定类名
        jobInfo.setProcessorInfo("com.cmcc.cmdevops.ci.job.processor.BuildTaskProcessor");
        //designatedWorkers指定机器执行，设置该参数后只有列表中的机器允许执行该任务，空代表不指定机器
        jobInfo.setDesignatedWorkers("");
        //minCpuCores,最小可用 CPU 核心数，CPU 可用核心数小于该值的 Worker 将不会执行该任务，0 代表无任何限制
        jobInfo.setMinCpuCores(0);
        //minMemorySpace,最小内存大小（GB），可用内存小于该值的Worker 将不会执行该任务，0 代表无任何限制
        jobInfo.setMinMemorySpace(0);
        //minDiskSpace，最小磁盘大小（GB），可用磁盘空间小于该值的Worker 将不会执行该任务，0 代表无任何限制
        jobInfo.setMinDiskSpace(0);
        //是否启用该任务，未启用的任务不会被调度
        jobInfo.setEnable(true);
        logger.info("create Job param: {}", JSON.toJSONString(jobInfo));
        ResultDTO<Long> resultDTO = client.saveJob(jobInfo);
        logger.info("create Job result : {}", JSON.toJSONString(resultDTO));
        if (resultDTO.isSuccess()) {
            logger.info(taskId + "任务创建成功！！！");
            BuildTriggerConfigBO triggerTask = buildTriggerConfigBizService.getBuildTriggerConfigByTaskId(taskId);
            triggerTask.setJobExeTime(LocalDateTime.now());
            buildTriggerConfigBizService.updateBuildTriggerConfig(triggerTask);
        }
    }

    @Override
    public void deleteJob(String taskId) {
        logger.info("开始删除构建定时执行任务，taskId：{}", taskId);
        PowerJobClient client = new PowerJobClient(serverAddress, appName, password);
        // 查询任务是否存在
        ResultDTO<List<JobInfoDTO>> result = client.fetchAllJob();
        if (!result.isSuccess() || result.getData() == null) {
            throw new RuntimeException("获取任务列表失败: " + result.getMessage());
        }
        JobInfoDTO existingJob = result.getData().stream()
                .filter(job -> taskId.equals(job.getJobName()))
                .findFirst()
                .orElse(null);
        if (existingJob != null) {
            ResultDTO<Void> resultDTO = client.deleteJob(existingJob.getId());
            if (resultDTO.isSuccess()) {
                logger.info(taskId + "任务已删除");
            }
        }else{
            logger.info(taskId + "任务不存在，无需删除");
        }
    }
}
