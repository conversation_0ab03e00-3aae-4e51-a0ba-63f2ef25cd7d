package com.cmcc.cmdevops.ci.service.business.impl.buildStrategy;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.cmcc.cmdevops.ci.service.bo.BuildSnapshotBO;
import com.cmcc.cmdevops.ci.service.bo.BuildStepConfigBO;
import com.cmcc.cmdevops.ci.service.bo.BuildStepsCustomizeBO;
import com.cmcc.cmdevops.ci.service.bo.BuildToolBO;
import com.cmcc.cmdevops.ci.service.business.BuildStrategy;
import com.cmcc.cmdevops.ci.service.business.SWorkerOpenBizService;
import com.cmcc.cmdevops.ci.service.business.util.EmptyValidator;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class CustomizeBuildStrategy implements BuildStrategy {

    @Resource
    private SWorkerOpenBizService sWorkerOpenBizService;

    @Override
    public String stageShell(BuildSnapshotBO buildSnapshotBO, BuildStepConfigBO buildStepBO, List<BuildToolBO> buildToolBOList) {
        StringBuilder buildShell = new StringBuilder();
        String repositoryAuth = sWorkerOpenBizService.dockerRepositoryAuth(buildSnapshotBO.getCreateUid());
        String repositoryUrl = sWorkerOpenBizService.dockerRepositoryUrl(buildSnapshotBO.getTenantId(), buildSnapshotBO.getCreateUid());
        BuildStepsCustomizeBO buildStepsCustomizeBO = buildStepBO.getConfig().toJavaObject(BuildStepsCustomizeBO.class);
        buildStepsCustomizeBO.setRepositoryUrl(repositoryUrl);
        buildStepsCustomizeBO.setRepositoryU(repositoryAuth.split("#######")[0]);
        buildStepsCustomizeBO.setRepositoryP(repositoryAuth.split("#######")[1]);
        BuildToolBO buildToolBO = null;
        Optional<BuildToolBO> toolBOOptional = buildToolBOList.stream()
                .filter(buildTool -> buildTool.getToolName().equals("customize"))
                .findAny();
        if (toolBOOptional.isPresent()) {
            buildToolBO = toolBOOptional.get();
        }
        buildStepBO.setConfig(JSON.parseObject(JSONObject.toJSONString(buildStepsCustomizeBO)));
        buildToolBO.setToolImage(buildStepsCustomizeBO.getRepositoryUrl()
                + "/" + buildStepsCustomizeBO.getRepositoryName()
                + "/" + buildStepsCustomizeBO.getArtifactName()
                + ":" + buildStepsCustomizeBO.getArtifactVersion());
        buildShell.append("stage('").append(buildStepBO.getName()).append("-").append(buildStepBO.getSerial()).append("') { \n")
                .append(getAgent(buildToolBO, buildStepsCustomizeBO,buildSnapshotBO))
                .append("steps { \n")
                .append("sh '''").append(JenkinsUtils.commandReplace(buildStepsCustomizeBO.getCommand())).append("'''\n")
                .append("} \n")
                .append("} \n");
        return buildShell.toString();
    }

    private String getAgent(BuildToolBO buildToolBO, BuildStepsCustomizeBO buildStepsCustomizeBO,BuildSnapshotBO buildSnapshotBO) {
        StringBuilder agent = new StringBuilder();
        if (EmptyValidator.isNotEmpty(buildToolBO)) {
            String cache = "";
            if (EmptyValidator.isNotEmpty(buildToolBO.getCacheDir())) {
                cache = buildToolBO.getCacheDir();
            }
            agent.append("agent {\n" +
                    "                docker {\n" +
                    "                  image '" + buildToolBO.getToolImage() + "'\n" +
                    "                  args '" + buildToolBO.getArgs() + ' ' + cache + "'\n" +
                    "                  reuseNode true\n" +
                    "                  registryUrl 'https://" + buildStepsCustomizeBO.getRepositoryUrl() + "'\n" +
                    "                  registryCredentialsId '" + buildSnapshotBO.getId() + "###" +  buildStepsCustomizeBO.getRepositoryU() + "'\n" +
                    "                }\n" +
                    "            }\n");
        }
        return agent.toString();
    }
}
