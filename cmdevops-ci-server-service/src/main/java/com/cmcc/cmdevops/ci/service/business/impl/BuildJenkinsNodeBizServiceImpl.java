package com.cmcc.cmdevops.ci.service.business.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.atom.BuildJenkinsNodeAtomService;
import com.cmcc.cmdevops.ci.service.bo.BuildJenkinsNodeBO;
import com.cmcc.cmdevops.ci.service.business.BuildJenkinsNodeBizService;
import com.cmcc.cmdevops.ci.service.dao.BuildJenkinsNodeDO;
import com.cmcc.cmdevops.util.BeanCloner;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Jenkins节点业务实现类
 */
@Service
public class BuildJenkinsNodeBizServiceImpl implements BuildJenkinsNodeBizService {

    private final BuildJenkinsNodeAtomService buildJenkinsNodeAtomService;

    public BuildJenkinsNodeBizServiceImpl(BuildJenkinsNodeAtomService buildJenkinsNodeAtomService) {
        this.buildJenkinsNodeAtomService = buildJenkinsNodeAtomService;
    }

    @Override
    public void createBuildJenkinsNode(BuildJenkinsNodeBO buildJenkinsNodeBO) {
        BuildJenkinsNodeDO buildJenkinsNodeDO = BeanCloner.clone(buildJenkinsNodeBO, BuildJenkinsNodeDO.class);
        buildJenkinsNodeAtomService.save(buildJenkinsNodeDO);
    }

    @Override
    public void updateBuildJenkinsNode(BuildJenkinsNodeBO buildJenkinsNodeBO) {
        BuildJenkinsNodeDO buildJenkinsNodeDO = BeanCloner.clone(buildJenkinsNodeBO, BuildJenkinsNodeDO.class);

        UpdateWrapper<BuildJenkinsNodeDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", buildJenkinsNodeBO.getId());

        buildJenkinsNodeAtomService.update(buildJenkinsNodeDO, updateWrapper);
    }

    @Override
    public void deleteBuildJenkinsNode(BuildJenkinsNodeBO buildJenkinsNodeBO) {
        buildJenkinsNodeAtomService.removeById(buildJenkinsNodeBO.getId());
    }

    @Override
    public BuildJenkinsNodeBO getBuildJenkinsNodeById(String id) {
        BuildJenkinsNodeDO buildJenkinsNodeDO = buildJenkinsNodeAtomService.getById(id);
        return (buildJenkinsNodeDO != null) ? BeanCloner.clone(buildJenkinsNodeDO, BuildJenkinsNodeBO.class) : null;
    }

    @Override
    public List<BuildJenkinsNodeBO> list(BuildJenkinsNodeBO pageRequest) {
        QueryWrapper<BuildJenkinsNodeDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("type", pageRequest.getType());
        queryWrapper.eq("build_environment_id", pageRequest.getBuildEnvironmentId());
        List<BuildJenkinsNodeDO> list = buildJenkinsNodeAtomService.list(queryWrapper);
        if (list != null && !list.isEmpty()) {
            return BeanCloner.clone(list, BuildJenkinsNodeBO.class);
        }
        return null;
    }

    @Override
    public PageResponse<List<BuildJenkinsNodeBO>> pageBuildJenkinsNodes(PageRequest pageRequest) {
        Page<BuildJenkinsNodeDO> page = new Page<>(pageRequest.getPageNo(), pageRequest.getPageSize());
        Page<BuildJenkinsNodeDO> result = buildJenkinsNodeAtomService.page(page);
        List<BuildJenkinsNodeBO> list = BeanCloner.clone(result.getRecords(), BuildJenkinsNodeBO.class);
        return PageResponse.success(list, result.getTotal(), pageRequest.getPageNo(), pageRequest.getPageSize());
    }
}
