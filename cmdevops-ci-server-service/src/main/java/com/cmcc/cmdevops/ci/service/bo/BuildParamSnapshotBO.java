package com.cmcc.cmdevops.ci.service.bo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> @since 2025-05-20
 */
@Getter
@Setter
@Accessors(chain = true)
public class BuildParamSnapshotBO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private String taskId;

    private String paramName;

    private Integer paramType;

    private String paramValue;

    private Integer paramValueType;

    private String paramOptions;

    private String paramDiscrib;

    private Boolean isOpen;

    private Boolean isRunning;

    private Boolean isEncrypt;

    private String spaceId;

    private String tenantId;

    private Boolean deleted;

    private LocalDateTime createTime;

    private String createUid;

    private LocalDateTime updateTime;

    private String updateUid;

    private LocalDateTime deleteTime;

    private String deleteUid;

    private String buildSnapshotId;
}
