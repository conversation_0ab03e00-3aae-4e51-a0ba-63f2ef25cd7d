package com.cmcc.cmdevops.ci.service.bo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class BuildProjectRelationBO implements Serializable {
    private Integer id;

    private String taskId;

    private String projectCode;

    private String tenantId;

    private Boolean deleted;

    private String createUid;

    private LocalDateTime createTime;

    private String updateUid;

    private LocalDateTime updateTime;

    private String deleteUid;

    private LocalDateTime deleteTime;
}
