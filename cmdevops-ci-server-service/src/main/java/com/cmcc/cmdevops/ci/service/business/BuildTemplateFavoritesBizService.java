package com.cmcc.cmdevops.ci.service.business;

import com.cmcc.cmdevops.ci.service.bo.BuildTemplateBO;

import java.util.List;

/**
 * 构建模板收藏业务服务接口
 */
public interface BuildTemplateFavoritesBizService {

    /**
     * 收藏模板
     *
     * @param templateId 模板ID
     * @param userId 用户ID
     * @param spaceId 工作空间ID
     * @return 操作是否成功
     */
    boolean favoriteTemplate(Integer templateId, String userId, String spaceId);

    /**
     * 取消收藏模板
     *
     * @param templateId 模板ID
     * @param userId 用户ID
     * @param spaceId 研发空间ID
     * @return 操作是否成功
     */
    boolean unfavoriteTemplate(String templateId, String userId, String spaceId);

    /**
     * 获取用户收藏的所有模板
     *
     * @param userId 用户ID
     * @param spaceId 工作空间ID
     * @return 收藏的模板列表
     */
    List<BuildTemplateBO> getFavoriteTemplates(String userId, String spaceId);
}
