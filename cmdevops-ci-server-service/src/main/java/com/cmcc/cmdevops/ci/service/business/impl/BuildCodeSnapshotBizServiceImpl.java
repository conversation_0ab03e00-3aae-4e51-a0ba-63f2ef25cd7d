package com.cmcc.cmdevops.ci.service.business.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.atom.BuildCodeSnapshotAtomService;
import com.cmcc.cmdevops.ci.service.bo.BuildCodeSnapshotBO;
import com.cmcc.cmdevops.ci.service.business.BuildCodeSnapshotBizService;
import com.cmcc.cmdevops.ci.service.business.util.EmptyValidator;
import com.cmcc.cmdevops.ci.service.dao.BuildCodeSnapshotDO;
import com.cmcc.cmdevops.util.BeanCloner;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 构建代码快照业务实现类
 */
@Service
public class BuildCodeSnapshotBizServiceImpl implements BuildCodeSnapshotBizService {

    private final BuildCodeSnapshotAtomService buildCodeSnapshotAtomService;

    public BuildCodeSnapshotBizServiceImpl(BuildCodeSnapshotAtomService buildCodeSnapshotAtomService) {
        this.buildCodeSnapshotAtomService = buildCodeSnapshotAtomService;
    }

    @Override
    public void createBuildCodeSnapshot(BuildCodeSnapshotBO buildCodeSnapshotBO) {
        BuildCodeSnapshotDO buildCodeSnapshotDO = BeanCloner.clone(buildCodeSnapshotBO, BuildCodeSnapshotDO.class);
        buildCodeSnapshotAtomService.save(buildCodeSnapshotDO);
    }

    @Override
    public void updateBuildCodeSnapshot(BuildCodeSnapshotBO buildCodeSnapshotBO) {
        BuildCodeSnapshotDO buildCodeSnapshotDO = BeanCloner.clone(buildCodeSnapshotBO, BuildCodeSnapshotDO.class);

        UpdateWrapper<BuildCodeSnapshotDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", buildCodeSnapshotBO.getId());

        buildCodeSnapshotAtomService.update(buildCodeSnapshotDO, updateWrapper);
    }

    @Override
    public void deleteBuildCodeSnapshot(BuildCodeSnapshotBO buildCodeSnapshotBO) {
        buildCodeSnapshotAtomService.removeById(buildCodeSnapshotBO.getId());
    }

    @Override
    public BuildCodeSnapshotBO getBuildCodeSnapshotById(String id) {
        BuildCodeSnapshotDO buildCodeSnapshotDO = buildCodeSnapshotAtomService.getById(id);
        return (buildCodeSnapshotDO != null) ? BeanCloner.clone(buildCodeSnapshotDO, BuildCodeSnapshotBO.class) : null;
    }

    @Override
    public BuildCodeSnapshotBO getBuildCodeSnapshotBySnapshotId(String snapshotId) {
        BuildCodeSnapshotDO buildCacheDOS = buildCodeSnapshotAtomService.getOne(new QueryWrapper<BuildCodeSnapshotDO>().eq("build_snapshot_id", snapshotId));
        if (EmptyValidator.isNotEmpty(buildCacheDOS)) {
            return BeanCloner.clone(buildCacheDOS, BuildCodeSnapshotBO.class);
        }
        return new BuildCodeSnapshotBO();
    }

    @Override
    public List<BuildCodeSnapshotBO> getBuildCodeSnapshotBySnapshotIds(List<String> snapshotIds) {
        if (EmptyValidator.isNotEmpty(snapshotIds)) {
            List<BuildCodeSnapshotDO> buildCacheDOS = buildCodeSnapshotAtomService.list(new QueryWrapper<BuildCodeSnapshotDO>().in("build_snapshot_id", snapshotIds));
            return BeanCloner.clone(buildCacheDOS, BuildCodeSnapshotBO.class);
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    public List<BuildCodeSnapshotBO> getBuildCodeSnapshotByTaskId(String taskId) {
        List<BuildCodeSnapshotDO> buildCacheDOS = buildCodeSnapshotAtomService.list(new QueryWrapper<BuildCodeSnapshotDO>().eq("task_id", taskId));
        return BeanCloner.clone(buildCacheDOS, BuildCodeSnapshotBO.class);
    }

    @Override
    public PageResponse<List<BuildCodeSnapshotBO>> pageBuildCodeSnapshots(PageRequest pageRequest) {
        Page<BuildCodeSnapshotDO> page = new Page<>(pageRequest.getPageNo(), pageRequest.getPageSize());
        Page<BuildCodeSnapshotDO> result = buildCodeSnapshotAtomService.page(page);
        List<BuildCodeSnapshotBO> list = BeanCloner.clone(result.getRecords(), BuildCodeSnapshotBO.class);
        return PageResponse.success(list, result.getTotal(), pageRequest.getPageNo(), pageRequest.getPageSize());
    }
}
