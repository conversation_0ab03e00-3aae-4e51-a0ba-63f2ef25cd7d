package com.cmcc.cmdevops.ci.service.business;

import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.bo.BuildStepConfigBO;

import java.util.List;

/**
 * 构建步骤配置业务接口
 */
public interface BuildStepConfigBizService {
    /**
     * 创建构建步骤配置
     *
     * @param buildStepConfigBO 构建步骤配置数据对象
     */
    void createBuildStepConfig(BuildStepConfigBO buildStepConfigBO);

    /**
     * 更新构建步骤配置
     *
     * @param buildStepConfigBO 构建步骤配置数据对象
     */
    void updateBuildStepConfig(BuildStepConfigBO buildStepConfigBO);

    /**
     * 删除构建步骤配置
     *
     * @param buildStepConfigBO 构建步骤配置数据对象
     */
    void deleteBuildStepConfig(BuildStepConfigBO buildStepConfigBO);

    /**
     * 根据ID获取构建步骤配置
     *
     * @param id 构建步骤配置ID
     * @return 构建步骤配置数据对象
     */
    BuildStepConfigBO getBuildStepConfigById(String id);

    List<BuildStepConfigBO> getBuildStepConfigListByTaskId(String taskId);

    /**
     * 分页查询构建步骤配置列表
     *
     * @param pageRequest 分页请求参数
     * @return 分页响应结果
     */
    PageResponse<List<BuildStepConfigBO>> pageBuildStepConfigs(PageRequest pageRequest);

    void deleteByTaskIds(List<String> taskIds);
}
