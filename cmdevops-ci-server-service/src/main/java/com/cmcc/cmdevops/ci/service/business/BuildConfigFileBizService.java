package com.cmcc.cmdevops.ci.service.business;

import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.bo.BuildConfigFileBO;
import com.cmcc.cmdevops.ci.service.bo.BuildStepConfigBO;

import java.util.List;

/**
 * 配置文件业务接口
 */
public interface BuildConfigFileBizService {

    void save(BuildConfigFileBO buildConfigFileBO);


    void update(BuildConfigFileBO buildConfigFileBO);


    void delete(String id);


    BuildConfigFileBO detail(String id);


    PageResponse<List<BuildConfigFileBO>> page(PageRequest pageRequest, BuildConfigFileBO buildConfigFileBO);

    List<BuildStepConfigBO> getBuildStepsByFileId(String id);

}
