package com.cmcc.cmdevops.ci.service.business.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.atom.BuildCacheAtomService;
import com.cmcc.cmdevops.ci.service.bo.BuildCacheBO;
import com.cmcc.cmdevops.ci.service.bo.BuildSnapshotBO;
import com.cmcc.cmdevops.ci.service.business.BuildCacheBizService;
import com.cmcc.cmdevops.ci.service.business.BuildSnapshotBizService;
import com.cmcc.cmdevops.ci.service.business.CiScheduleBizService;
import com.cmcc.cmdevops.ci.service.business.util.EmptyValidator;
import com.cmcc.cmdevops.ci.service.business.util.UserUtils;
import com.cmcc.cmdevops.ci.service.dao.BuildCacheDO;
import com.cmcc.cmdevops.util.BeanCloner;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 构建缓存业务实现类
 */
@Service
@RequiredArgsConstructor
public class BuildCacheBizServiceImpl implements BuildCacheBizService {

    private final BuildCacheAtomService buildCacheAtomService;
    private final CiScheduleBizService ciScheduleBizService;
    private final BuildSnapshotBizService buildSnapshotBizService;

    @Override
    public void createBuildCache(BuildCacheBO buildCacheBO) {
        BuildCacheDO buildCacheDO = BeanCloner.clone(buildCacheBO, BuildCacheDO.class);
        buildCacheDO.setTenantId(UserUtils.getTenantId());
        buildCacheDO.setCreateUid(UserUtils.getUserId());
        buildCacheAtomService.save(buildCacheDO);
    }

    @Override
    public void updateBuildCache(BuildCacheBO buildCacheBO) {
        BuildCacheDO buildCacheDO = BeanCloner.clone(buildCacheBO, BuildCacheDO.class);
        buildCacheDO.setUpdateUid(UserUtils.getUserId());
        buildCacheAtomService.updateById(buildCacheDO);
    }

    @Override
    public void deleteBuildCache(Integer cacheId) {
        buildCacheAtomService.removeById(cacheId);
    }

    @Override
    public BuildCacheBO getBuildCacheById(Integer id) {
        BuildCacheDO buildCacheDO = buildCacheAtomService.getById(id);
        return (buildCacheDO != null) ? BeanCloner.clone(buildCacheDO, BuildCacheBO.class) : null;
    }

    @Override
    public List<BuildCacheBO> getListByTaskId(String taskId) {
        LambdaQueryWrapper<BuildCacheDO> queryWrapper = new LambdaQueryWrapper<BuildCacheDO>().eq(BuildCacheDO::getTaskId, taskId);
        queryWrapper.orderByDesc(BuildCacheDO::getCreateTime);
        List<BuildCacheDO> buildCacheDOS = buildCacheAtomService.list(queryWrapper);
        return BeanCloner.clone(buildCacheDOS, BuildCacheBO.class);
    }

    @Override
    public String cleanBuildCache(String taskId) {
        PageRequest pageRequest = new PageRequest(1, 1);
        PageResponse<List<BuildSnapshotBO>> pageResponse = buildSnapshotBizService.list(pageRequest, new BuildSnapshotBO().setTaskId(taskId));
        List<BuildSnapshotBO> list = pageResponse.getData();
        if (EmptyValidator.isNotEmpty(list)) {
            BuildSnapshotBO buildSnapshotBO = list.get(0);
            ciScheduleBizService.cleanBuildCache(taskId, buildSnapshotBO.getId());
        }
        return null;
    }

    @Override
    public PageResponse<List<BuildCacheBO>> pageBuildCaches(PageRequest pageRequest) {
        Page<BuildCacheDO> page = new Page<>(pageRequest.getPageNo(), pageRequest.getPageSize());
        Page<BuildCacheDO> result = buildCacheAtomService.page(page);
        List<BuildCacheBO> list = BeanCloner.clone(result.getRecords(), BuildCacheBO.class);
        return PageResponse.success(list, result.getTotal(), pageRequest.getPageNo(), pageRequest.getPageSize());
    }

    @Override
    public void deleteByTaskIds(List<String> taskIds) {
        LambdaQueryWrapper<BuildCacheDO> wrapper = new LambdaQueryWrapper<BuildCacheDO>().in(BuildCacheDO::getTaskId, taskIds);
        buildCacheAtomService.remove(wrapper);
    }
}
