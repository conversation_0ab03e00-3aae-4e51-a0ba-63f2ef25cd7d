package com.cmcc.cmdevops.ci.service.bo;

public enum StatusEnum {
    // 状态：未执行  执行中 执行失败  执行成功  等待审批   正在停止   手动停止
    // 执行成功
    PENDING(0, "待执行"),
    QUEUED(1, "排队中"),
    RUNNING(2, "执行中"),
    SUCCESS(3, "成功"),
    FAILED(4, "失败"),
    CANCELLED(5, "已取消"),
    TIMEOUT(6, "已暂停"),
    SKIPPED(7, "已跳过"),
    PAUSED(8, "超时"),
    STOP(9, "已停止");

    Integer code;
    String code2;
    String msg;

    StatusEnum() {
    }

    StatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    StatusEnum(String code2, String msg) {
        this.code2 = code2;
        this.msg = msg;
    }

    public String getCode2() {
        return code2;
    }

    public void setCode2(String code2) {
        this.code2 = code2;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }


    public static String getNameByCode(Integer code) {
        for (StatusEnum value : StatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getMsg();
            }
        }
        return null;
    }

    public static String fromCode(int code) {
        StatusEnum[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            StatusEnum status = var1[var3];
            if (status.code == code) {
                return status.toString();
            }
        }

        throw new IllegalArgumentException("未知的执行状态码: " + code);
    }
}
