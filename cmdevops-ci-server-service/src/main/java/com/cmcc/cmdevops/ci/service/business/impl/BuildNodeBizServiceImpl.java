package com.cmcc.cmdevops.ci.service.business.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.atom.BuildNodeAtomService;
import com.cmcc.cmdevops.ci.service.bo.BuildEnvironmentBO;
import com.cmcc.cmdevops.ci.service.bo.BuildJenkinsNodeBO;
import com.cmcc.cmdevops.ci.service.bo.BuildNodeBO;
import com.cmcc.cmdevops.ci.service.business.BuildEnvironmentBizService;
import com.cmcc.cmdevops.ci.service.business.BuildJenkinsNodeBizService;
import com.cmcc.cmdevops.ci.service.business.BuildNodeBizService;
import com.cmcc.cmdevops.ci.service.dao.BuildNodeDO;
import com.cmcc.cmdevops.util.BeanCloner;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 构建节点业务实现类
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class BuildNodeBizServiceImpl implements BuildNodeBizService {

    private final BuildNodeAtomService buildNodeAtomService;

    private final BuildEnvironmentBizService buildEnvironmentBizService;

    private final BuildJenkinsNodeBizService buildJenkinsNodeBizService;


    @Override
    public void createBuildNode(BuildNodeBO buildNodeBO) {
        BuildNodeDO buildNodeDO = BeanCloner.clone(buildNodeBO, BuildNodeDO.class);
        buildNodeAtomService.save(buildNodeDO);
    }

    @Override
    public void updateBuildNode(BuildNodeBO buildNodeBO) {
        BuildNodeDO buildNodeDO = BeanCloner.clone(buildNodeBO, BuildNodeDO.class);

        UpdateWrapper<BuildNodeDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", buildNodeBO.getId());

        buildNodeAtomService.update(buildNodeDO, updateWrapper);
    }

    @Override
    public void deleteBuildNode(BuildNodeBO buildNodeBO) {
        buildNodeAtomService.removeById(buildNodeBO.getId());
    }

    @Override
    public BuildNodeBO getBuildNodeById(Integer id) {
        BuildNodeDO buildNodeDO = buildNodeAtomService.getById(id);
        return (buildNodeDO != null) ? BeanCloner.clone(buildNodeDO, BuildNodeBO.class) : null;
    }

    @Override
    public List<BuildNodeBO> list(BuildNodeBO buildNodeBO) {
        List<BuildNodeDO> list = buildNodeAtomService.list();
        return BeanCloner.clone(list, BuildNodeBO.class);
    }

    @Override
    public Map<String, String> getCiToolEnvs(Integer id) {
        Map<String, String> map = new HashMap<>();
        BuildNodeDO buildNodeDO = buildNodeAtomService.getById(id);
        Integer buildEnvironmentId = buildNodeDO.getBuildEnvironmentId();
        String buildNodeType = buildNodeDO.getBuildNodeType();
        BuildEnvironmentBO buildEnvironmentBO = buildEnvironmentBizService.detail(buildEnvironmentId);
        String environmentAccessUrl = buildEnvironmentBO.getEnvironmentUrl();
        BuildJenkinsNodeBO buildJenkinsNodeBO = new BuildJenkinsNodeBO();
        buildJenkinsNodeBO.setType(buildNodeType);
        buildJenkinsNodeBO.setBuildEnvironmentId(buildEnvironmentId);
        List<BuildJenkinsNodeBO> buildJenkinsNodeBOS = buildJenkinsNodeBizService.list(buildJenkinsNodeBO);
        Collections.shuffle(buildJenkinsNodeBOS);
        // 随机取一个
        BuildJenkinsNodeBO buildJenkinsNodeBO1 = buildJenkinsNodeBOS.get(0);
        map.put("ciScheduleUrl", environmentAccessUrl);
        map.put("ciToolUrl", buildJenkinsNodeBO1.getNodeUrl());
        return map;
    }

    @Override
    public PageResponse<List<BuildNodeBO>> pageBuildNodes(PageRequest pageRequest) {
        Page<BuildNodeDO> page = new Page<>(pageRequest.getPageNo(), pageRequest.getPageSize());
        Page<BuildNodeDO> result = buildNodeAtomService.page(page);
        List<BuildNodeBO> list = BeanCloner.clone(result.getRecords(), BuildNodeBO.class);
        return PageResponse.success(list, result.getTotal(), pageRequest.getPageNo(), pageRequest.getPageSize());
    }
}
