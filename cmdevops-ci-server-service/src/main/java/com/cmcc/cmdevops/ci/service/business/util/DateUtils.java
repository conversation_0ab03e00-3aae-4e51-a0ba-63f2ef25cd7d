package com.cmcc.cmdevops.ci.service.business.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * <p>Title: DateUtils </p>
 * <p>Description: Date操作工具类 </p>
 *
 */
public class DateUtils {
	static final String DEFAULT_PATTERN = "yyyy-MM-dd HH:mm:ss";

	public static String format(Long date) {
		return format(new Date(date), DEFAULT_PATTERN);
	}

	public static String format(Date date) {
		return format(date, DEFAULT_PATTERN);
	}

	public static String format(Date date, String pattern) {
		return format(date, pattern, Locale.CANADA);
	}

	public static String format(Date date, String pattern, Locale locale) {
		SimpleDateFormat sdf = new SimpleDateFormat(pattern, locale);
		return sdf.format(date);
	}

	public static Date parse(String sDate) {
		return parse(sDate, DEFAULT_PATTERN);
	}

	public static Date parse(String sDate, String pattern) {
		try {
			SimpleDateFormat sdf = new SimpleDateFormat(pattern);
			return sdf.parse(sDate);
		} catch (ParseException e) {
			throw new RuntimeException(e.getMessage(), e);
		}
	}
}
