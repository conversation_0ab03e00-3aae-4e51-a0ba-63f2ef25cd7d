package com.cmcc.cmdevops.ci.service.bo;

import lombok.Data;

import java.io.Serializable;


/**
* <p>Title: CipBuildStepsAntDTO</p>
* <p>Description:  </p>
* <p>Copyright: Copyright (c) 2018</p>
* <p>Company: SI-TECH </p>
* <AUTHOR>
* @version 1.0
* @createtime 2021-05-06 15:34:50
*
*/
@Data
public class BuildStepsAntBO implements Serializable {
    private static final long serialVersionUID = 1L;

    public BuildStepsAntBO() {

    }

    /**
    *
    */
    private String toolVersion;
    private String jdkVersion;
    /**
    *
    */
    private String command;

}
