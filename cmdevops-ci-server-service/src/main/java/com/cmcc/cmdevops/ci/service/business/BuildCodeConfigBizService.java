package com.cmcc.cmdevops.ci.service.business;

import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.bo.BuildCodeConfigBO;
import java.util.List;

/**
 * 构建代码配置业务接口
 */
public interface BuildCodeConfigBizService {
    /**
     * 创建构建代码配置
     * @param buildCodeConfigBO 构建代码配置数据对象
     */
    void createBuildCodeConfig(BuildCodeConfigBO buildCodeConfigBO);

    /**
     * 更新构建代码配置
     * @param buildCodeConfigBO 构建代码配置数据对象
     */
    void updateBuildCodeConfig(BuildCodeConfigBO buildCodeConfigBO);

    /**
     * 删除构建代码配置
     * @param buildCodeConfigBO 构建代码配置数据对象
     */
    void deleteBuildCodeConfig(BuildCodeConfigBO buildCodeConfigBO);

    /**
     * 根据ID获取构建代码配置
     * @param id 构建代码配置ID
     * @return 构建代码配置数据对象
     */
    BuildCodeConfigBO getBuildCodeConfigById(String id);

    BuildCodeConfigBO getBuildCodeConfigByTaskId(String taskId);

    /**
     * 分页查询构建代码配置列表
     * @param pageRequest 分页请求参数
     * @return 分页响应结果
     */
    PageResponse<List<BuildCodeConfigBO>> pageBuildCodeConfigs(PageRequest pageRequest);

    void deleteByTaskIds(List<String> taskIds);
}
