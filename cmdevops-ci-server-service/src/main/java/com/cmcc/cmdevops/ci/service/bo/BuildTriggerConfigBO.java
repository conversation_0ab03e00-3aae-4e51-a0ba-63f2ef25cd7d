package com.cmcc.cmdevops.ci.service.bo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> @since 2025-05-20
 */
@Getter
@Setter
@Accessors(chain = true)
public class BuildTriggerConfigBO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private String taskId;

    private Boolean codeTriggerSwitch;

    private String codeTriggerEvent;

    private String codeTriggerFilter;

    private Boolean timeTriggerSwitch;

    private String timeTriggerCrontab;

    private Boolean timeTriggerCodeChangeTimeTriggerSwitch;

    private String spaceId;

    private String tenantId;

    private Boolean deleted;

    private LocalDateTime createTime;

    private String createUid;

    private LocalDateTime updateTime;

    private String updateUid;

    private LocalDateTime deleteTime;

    private String deleteUid;

    private String timeTriggerType;

    private LocalDateTime jobExeTime;
}
