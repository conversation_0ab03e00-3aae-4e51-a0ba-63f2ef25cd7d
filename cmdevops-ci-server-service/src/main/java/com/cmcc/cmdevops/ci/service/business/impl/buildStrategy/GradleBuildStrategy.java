package com.cmcc.cmdevops.ci.service.business.impl.buildStrategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cmcc.cmdevops.ci.service.atom.BuildDictAtomService;
import com.cmcc.cmdevops.ci.service.bo.*;
import com.cmcc.cmdevops.ci.service.business.BuildStrategy;
import com.cmcc.cmdevops.ci.service.business.util.SpringUtils;
import com.cmcc.cmdevops.ci.service.dao.BuildDictDO;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * Created by 51746 on 2021/2/25.
 */
@Service
public class GradleBuildStrategy implements BuildStrategy {

    @Override
    public String stageShell(BuildSnapshotBO buildSnapshotBO, BuildStepConfigBO buildStepsBO, List<BuildToolBO> buildToolBOList) {
        StringBuilder buildShell = new StringBuilder();
        BuildStepsGradleBO cipBuildStepsGradleDTO = buildStepsBO.getConfig().toJavaObject(BuildStepsGradleBO.class);
        BuildToolBO buildToolBO = null;
        Optional<BuildToolBO> toolBOOptional = buildToolBOList.stream()
                .filter(buildTool -> buildTool.getToolName().equals(cipBuildStepsGradleDTO.getToolVersion()))
                .findAny();
        if (toolBOOptional.isPresent()) {
            buildToolBO = toolBOOptional.get();
        }
        // gradle名称
        String gradleName = cipBuildStepsGradleDTO.getToolVersion().replace("gradle", "");
        // jdk名称
        BuildDictAtomService buildDictAtomService = SpringUtils.getBean(BuildDictAtomService.class);
        LambdaQueryWrapper<BuildDictDO> queryWrapper = new LambdaQueryWrapper<BuildDictDO>().eq(BuildDictDO::getDictKey, cipBuildStepsGradleDTO.getJdkVersion());
        BuildDictDO buildDictDO = buildDictAtomService.getOne(queryWrapper);
        String jdkName = buildDictDO.getDictValue();
        String sdkmanCommand = "sdk default java " + jdkName + " && sdk default gradle " + gradleName;
        buildShell.append("stage('").append(buildStepsBO.getName()).append("-").append(buildStepsBO.getSerial()).append("') { \n")
                .append(JenkinsUtils.getAgent(buildToolBO))
                .append("steps { \n")
                .append("  strictShNoOut('").append(sdkmanCommand).append("')\n")
                .append("sh '''").append(JenkinsUtils.commandReplace(cipBuildStepsGradleDTO.getCommand())).append("'''\n")
                .append("} \n")
                .append("} \n");
        return buildShell.toString();
    }
}
