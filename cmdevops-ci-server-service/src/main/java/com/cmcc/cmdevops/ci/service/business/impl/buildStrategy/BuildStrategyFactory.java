package com.cmcc.cmdevops.ci.service.business.impl.buildStrategy;

import java.util.HashMap;
import java.util.Map;

import static com.cmcc.cmdevops.ci.service.business.BuildType.*;

/**
 * <AUTHOR>
 */
public class BuildStrategyFactory {

    private static BuildStrategyFactory factory = new BuildStrategyFactory();

    private static Map<String, String> strategyMap = new HashMap<>();

    static {
        strategyMap.put(MAVEN, "mavenBuildStrategy");
        strategyMap.put(ANT, "antBuildStrategy");
        strategyMap.put(NODE, "nodejsBuildStrategy");
        strategyMap.put(NPM, "npmBuildStrategy");
        strategyMap.put(YARN, "yarnBuildStrategy");
        strategyMap.put(GRUNT, "gruntBuildStrategy");
        strategyMap.put(GULP, "gulpBuildStrategy");
        strategyMap.put(VITE, "viteBuildStrategy");
        strategyMap.put(CANGJIE, "cangjieBuildStrategy");
        strategyMap.put(PYTHON, "pythonBuildStrategy");
        strategyMap.put(GRADLE, "gradleBuildStrategy");
        strategyMap.put(FIS, "fisBuildStrategy");
        strategyMap.put(GO, "goBuildStrategy");
        strategyMap.put(SHELL, "shellBuildStrategy");
        strategyMap.put(UPLOAD_ARTIFACT, "uploadArtifactBuildStrategy");
        strategyMap.put(UPLOAD_CACHE_DIR, "uploadCacheDirStrategy");
        strategyMap.put(DOWNLOAD_CACHE_DIR, "downloadCacheDirStrategy");
        strategyMap.put(UPLOAD_DOCKER, "uploadDockerBuildStrategy");
        strategyMap.put(JUNIT, "junitBuildStrategy");
        strategyMap.put(DEFAULT_COMMAND, "defaultCommandStrategy");
        strategyMap.put(SONAR, "sonarBuildStrategy");
        strategyMap.put(CUSTOMIZE, "customizeBuildStrategy");
        strategyMap.put(COMP_SCAN, "compScanBuildStrategy");
        strategyMap.put(CMAKE, "cmakeBuildStrategy");
        strategyMap.put(PHP, "phpBuildStrategy");
        strategyMap.put(MSBUILD, "msbuildBuildStrategy");
        strategyMap.put(SWIFT, "swiftBuildStrategy");
        strategyMap.put(PY_INSTALLER, "pyInstallerBuildStrategy");
        strategyMap.put(SETUPTOOLS, "setuptoolsBuildStrategy");
        strategyMap.put(RUBY, "rubyBuildStrategy");
        strategyMap.put(LUA, "luaBuildStrategy");
        strategyMap.put(RUST, "rustBuildStrategy");
        strategyMap.put(SCALA, "scalaBuildStrategy");
        strategyMap.put(P4, "p4BuildStrategy");
        strategyMap.put(ANDROID, "androidBuildStrategy");
    }

    public String creator(String type) {
        return strategyMap.get(type);
    }

    public static BuildStrategyFactory getInstance() {
        return factory;
    }

}
