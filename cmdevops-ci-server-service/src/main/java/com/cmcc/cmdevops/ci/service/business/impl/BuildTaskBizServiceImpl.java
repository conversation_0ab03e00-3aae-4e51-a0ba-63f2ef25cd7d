package com.cmcc.cmdevops.ci.service.business.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.atom.BuildGroupRelationAtomService;
import com.cmcc.cmdevops.ci.service.atom.BuildProjectRelationAtomService;
import com.cmcc.cmdevops.ci.service.atom.BuildSnapshotAtomService;
import com.cmcc.cmdevops.ci.service.atom.BuildTaskAtomService;
import com.cmcc.cmdevops.ci.service.bo.*;
import com.cmcc.cmdevops.ci.service.business.*;
import com.cmcc.cmdevops.ci.service.business.impl.buildStrategy.BuildStrategyFactory;
import com.cmcc.cmdevops.ci.service.business.util.DateUtils;
import com.cmcc.cmdevops.ci.service.business.util.EmptyValidator;
import com.cmcc.cmdevops.ci.service.business.util.TemplateUtil;
import com.cmcc.cmdevops.ci.service.business.util.UUIDGenerator;
import com.cmcc.cmdevops.ci.service.business.util.UserUtils;
import com.cmcc.cmdevops.ci.service.dao.BuildGroupRelationDO;
import com.cmcc.cmdevops.ci.service.dao.BuildProjectRelationDO;
import com.cmcc.cmdevops.ci.service.dao.BuildSnapshotDO;
import com.cmcc.cmdevops.ci.service.dao.BuildTaskDO;
import com.cmcc.cmdevops.component.auth.user.model.UserInfo;
import com.cmcc.cmdevops.component.auth.user.utils.JwtHeaderInfo;
import com.cmcc.cmdevops.component.oss.client.OssClient;
import com.cmcc.cmdevops.exception.BusinessException;
import com.cmcc.cmdevops.util.BeanCloner;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanMap;
import org.apache.commons.compress.utils.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Scanner;
import java.util.stream.Collectors;

import static com.cmcc.cmdevops.ci.service.business.BuildType.K8S;

/**
 * 业务实现类，此类中应仅用于组装。不应放置业务逻辑处理。如状态的if判断，分支判断以及各种转换
 * 如果需要业务逻辑处理，需要封装相应的业务类。按照变通DDD模式进行
 * 注意：单个对象复制使用 BeanCloner.copy()
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BuildTaskBizServiceImpl implements BuildTaskBizService {

    private final BuildTaskAtomService buildTaskAtomService;
    private final BuildAdvancedConfigBizService buildAdvancedConfigBizService;
    private final BuildArtifactBizService buildArtifactBizService;
    private final BuildCacheBizService buildCacheBizService;
    private final BuildCodeConfigBizService buildCodeConfigBizService;
    private final BuildCodeSnapshotBizService buildCodeSnapshotBizService;
    private final BuildEnvironmentBizService buildEnvironmentBizService;
    private final BuildJenkinsNodeBizService buildJenkinsNodeBizService;
    private final BuildNodeBizService buildNodeBizService;
    private final BuildParameterBizService buildParameterBizService;
    private final BuildParamSnapshotBizService buildParamSnapshotBizService;
    private final BuildScriptBizService buildScriptBizService;
    private final BuildSnapshotBizService buildSnapshotBizService;
    private final BuildStepConfigBizService buildStepConfigBizService;
    private final BuildStepSnapshotBizService buildStepSnapshotBizService;
    private final BuildToolBizService buildToolBizService;
    private final BuildTriggerConfigBizService buildTriggerConfigBizService;
    private final BuildVersionBizService buildVersionBizService;
    private final ResourceLoader resourceLoader;
    private final ApplicationContext applicationContext;
    private final CiScheduleBizService ciScheduleBizService;
    private final BuildScheduleSnapshotBizService buildScheduleSnapshotBizService;
    private final SWorkerOpenBizService sWorkerOpenBizService;
    private final BuildSnapshotAtomService buildSnapshotAtomService;
    private final BuildGroupRelationAtomService buildGroupRelationAtomService;
    private final BuildTemplateBizService buildTemplateService;
    private final OssClient ossClient;
    private final BuildConfigFileBizService buildConfigFileBizService;
    private final BuildProjectRelationAtomService buildProjectRelationAtomService;
    private final BuildTaskTriggerService buildTaskTriggerService;

    @Value("${ci-tool.slave-image}")
    private String slaveImage;


    @Override
    public PageResponse<List<BuildTaskBO>> pageBuildTasks(PageRequest pageRequest, BuildTaskBO buildTaskBO) {
        Page<BuildTaskDO> page = new Page<>(pageRequest.getPageNo(), pageRequest.getPageSize());
        LambdaQueryWrapper<BuildTaskDO> queryWrapper = new LambdaQueryWrapper<>();
        if (EmptyValidator.isNotEmpty(buildTaskBO)) {
            if (EmptyValidator.isNotEmpty(buildTaskBO.getId())) {
                queryWrapper.eq(BuildTaskDO::getId, buildTaskBO.getId());
            }
            if (EmptyValidator.isNotEmpty(buildTaskBO.getTaskName())) {
                queryWrapper.like(BuildTaskDO::getTaskName, buildTaskBO.getTaskName());
            }
            if (EmptyValidator.isNotEmpty(buildTaskBO.getBuildStatus())) {
                queryWrapper.eq(BuildTaskDO::getBuildStatus, buildTaskBO.getBuildStatus());
            }
            if (EmptyValidator.isNotEmpty(buildTaskBO.getSpaceId())) {
                queryWrapper.eq(BuildTaskDO::getSpaceId, buildTaskBO.getSpaceId());
            }
            if (EmptyValidator.isNotEmpty(buildTaskBO.getBuildGroup())) {
                String buildGroup = buildTaskBO.getBuildGroup();
                // 检查是否是"未分组"查询
                if ("UNGROUPED".equals(buildGroup)) {
                    // 查询所有已分组的任务ID
                    LambdaQueryWrapper<BuildGroupRelationDO> allGroupWrapper = new LambdaQueryWrapper<>();
                    List<BuildGroupRelationDO> allGroupRelationDOList = buildGroupRelationAtomService.list(allGroupWrapper);
                    if (EmptyValidator.isNotEmpty(allGroupRelationDOList)) {
                        // 提取所有已分组的任务ID
                        List<String> groupedTaskIdList = allGroupRelationDOList.stream()
                                .map(BuildGroupRelationDO::getBusinessDataId)
                                .distinct()
                                .collect(Collectors.toList());
                        // 排除已分组的任务，查询未分组的任务
                        queryWrapper.notIn(BuildTaskDO::getId, groupedTaskIdList);
                    } else {
                        // 如果没有任何分组关系，说明所有任务都是未分组的，不需要额外条件
                    }
                } else {
                    // 原有的分组查询逻辑
                    Integer groupId = Integer.valueOf(buildGroup);
                    // 查询分组关系
                    LambdaQueryWrapper<BuildGroupRelationDO> groupWrapper = new LambdaQueryWrapper<>();
                    groupWrapper.eq(BuildGroupRelationDO::getGroupId, groupId);
                    List<BuildGroupRelationDO> groupRelationDOList = buildGroupRelationAtomService.list(groupWrapper);
                    if (EmptyValidator.isEmpty(groupRelationDOList)) {
                        return PageResponse.success(Collections.emptyList(), 0, pageRequest.getPageNo(), pageRequest.getPageSize());
                    }
                    // 提取任务ID列表
                    List<String> taskIdList = groupRelationDOList.stream()
                            .map(BuildGroupRelationDO::getBusinessDataId)
                            .collect(Collectors.toList()); // 使用collect替代toList()，兼容性更好
                    queryWrapper.in(BuildTaskDO::getId, taskIdList);
                }
            }
        }
        queryWrapper.orderByDesc(BuildTaskDO::getCreateTime);
        Page<BuildTaskDO> result = buildTaskAtomService.page(page, queryWrapper);
        List<BuildTaskBO> list = BeanCloner.clone(result.getRecords(), BuildTaskBO.class);
        // 构建code快照
        List<String> buildSnapshotIds = list.stream().map(BuildTaskBO::getLastBuildId).toList();
        List<BuildCodeSnapshotBO> buildCodeSnapshotBOs = buildCodeSnapshotBizService.getBuildCodeSnapshotBySnapshotIds(buildSnapshotIds);
        Map<String, List<BuildCodeSnapshotBO>> snapshotMap = buildCodeSnapshotBOs.stream().collect(Collectors.groupingBy(BuildCodeSnapshotBO::getBuildSnapshotId));

        for (BuildTaskBO t : list) {
            String lastBuildId = t.getLastBuildId();
            if (EmptyValidator.isNotEmpty(lastBuildId) && snapshotMap.containsKey(lastBuildId)) {
                List<BuildCodeSnapshotBO> buildCodeSnapshotBOS = snapshotMap.get(lastBuildId);
                if (EmptyValidator.isNotEmpty(buildCodeSnapshotBOS)) {
                    BuildCodeSnapshotBO buildCodeSnapshotBO = buildCodeSnapshotBOs.get(0);
                    buildCodeSnapshotBO.setCommitMsg(JSONObject.parseObject(buildCodeSnapshotBO.getCommitMessage()));
                    t.setBuildCodeSnapshot(buildCodeSnapshotBO);
                }
            }
        }
        return PageResponse.success(list, result.getTotal(), pageRequest.getPageNo(), pageRequest.getPageSize());
    }

    @Override
    public void createTask(BuildTaskBO buildTaskBO) {
        if (EmptyValidator.isEmpty(buildTaskBO.getId())) {
            buildTaskBO.setId(UUIDGenerator.create());
        }
        buildTaskBO.setTenantId(UserUtils.getTenantId());
        buildTaskBO.setCreateUid(UserUtils.getUserId());
        this.saveBuildCodeConfig(buildTaskBO);
        this.saveBuildSteps(buildTaskBO);
        this.saveBuildScript(buildTaskBO);
//        this.saveBuildAdvancedConfig(buildTaskBO);
        this.saveBuildTriggerConfig(buildTaskBO);
        BuildTaskDO buildTaskDO = BeanCloner.clone(buildTaskBO, BuildTaskDO.class);
        buildTaskAtomService.saveOrUpdate(buildTaskDO);
    }

    public String initTask(String spaceId, Integer templateId) {
        // 根据模板获取 构建的配置信息
        List<BuildStepConfigBO> stepConfigBOS = new ArrayList<>();
        BuildTemplateBO templateBO = buildTemplateService.getBuildTemplateById(templateId);
        JSONArray stepAs = JSONObject.parseObject(templateBO.getBuildConfig()).getJSONArray("steps");
        for (int i = 0; i < stepAs.size(); i++) {
            stepAs.getJSONObject(i).remove("id");
            BuildStepConfigBO stepConfigBO = stepAs.getObject(i, BuildStepConfigBO.class);
            stepConfigBOS.add(stepConfigBO);
        }
        BuildTaskBO buildTaskBO = new BuildTaskBO();
        buildTaskBO.setTaskName("BUILD-" + DateUtils.format(new Date(), "YYYYmmddHHMM"));
        buildTaskBO.setTenantId(UserUtils.getTenantId());
        buildTaskBO.setCreateUid(UserUtils.getUserId());
        buildTaskBO.setSpaceId(spaceId);
        buildTaskBO.setBuildSource("BUILD");
        buildTaskBO.setPermissionType("global");
        buildTaskBO.setMaxConcurrentCount(10);
        buildTaskBO.setConcurrentStrategy(2);
        buildTaskBO.setCodeConfig(new BuildCodeConfigBO());
        buildTaskBO.setStepConfigs(stepConfigBOS);
        buildTaskBO.setOvertime((short) 10);
        //BuildAdvancedConfigBO advancedConfigBO = new BuildAdvancedConfigBO();
        //advancedConfigBO.setOvertime((short) 10);
        // advancedConfigBO.setAssignedNode("kubernetes");
        //buildTaskBO.setAdvancedConfig(advancedConfigBO);
        if (EmptyValidator.isEmpty(buildTaskBO.getId())) {
            buildTaskBO.setId(UUIDGenerator.create());
            buildTaskBO.setBuildType("gui");
        }

        BuildTriggerConfigBO triggerConfigBO = new BuildTriggerConfigBO();
        triggerConfigBO.setCodeTriggerSwitch(false);
        triggerConfigBO.setTimeTriggerSwitch(false);
        triggerConfigBO.setTimeTriggerCodeChangeTimeTriggerSwitch(false);
        triggerConfigBO.setTimeTriggerType("1");
        buildTaskBO.setBuildTriggerConfig(triggerConfigBO);

        this.saveBuildCodeConfig(buildTaskBO);
        // this.saveBuildScript(buildTaskBO);
        this.saveBuildSteps(buildTaskBO);
        //this.saveBuildAdvancedConfig(buildTaskBO);
        this.saveBuildTriggerConfig(buildTaskBO);
        // 保存默认缓存
        this.saveDefaultCache(buildTaskBO);
        BuildTaskDO buildTaskDO = BeanCloner.clone(buildTaskBO, BuildTaskDO.class);
        buildTaskDO.setDeleted(false);
        buildTaskAtomService.save(buildTaskDO);
        return buildTaskBO.getId();
    }

    @Override
    public String copyTask(String taskId) {
        checkTaskIsDisable(taskId);
        BuildTaskBO buildTaskBO = this.getTask(taskId);

        buildTaskBO.setId(UUIDGenerator.create());
        buildTaskBO.setTaskName(buildTaskBO.getTaskName() + "_copy");
        buildTaskBO.setCreateTime(LocalDateTime.now());
        buildTaskBO.getCodeConfig().setId(null);
        //buildTaskBO.getAdvancedConfig().setId(null);
        buildTaskBO.setLastBuildTime(null);
        buildTaskBO.setBuildStatus(null);
        buildTaskBO.setLastBuildId("");
        buildTaskBO.setBuildNumber("");
        this.createTask(buildTaskBO);
        // 参数复制
        buildTaskBO.getParameters().forEach(ele -> {
            ele.setId(null);
            ele.setTaskId(buildTaskBO.getId());
        });
        buildParameterBizService.batchSave(buildTaskBO.getParameters());
        // 缓存复制
        buildTaskBO.getCaches().forEach(ele -> {
            ele.setId(null);
            ele.setTaskId(buildTaskBO.getId());
            buildCacheBizService.createBuildCache(ele);
        });
        return buildTaskBO.getId();
    }

    private void saveBuildCodeConfig(BuildTaskBO buildTaskBO) {
        BuildCodeConfigBO codeConfig = buildTaskBO.getCodeConfig();
        codeConfig.setSpaceId(buildTaskBO.getSpaceId());
        codeConfig.setTenantId(buildTaskBO.getTenantId());
        codeConfig.setCreateUid(buildTaskBO.getCreateUid());
        if (EmptyValidator.isNotEmpty(codeConfig.getId())) {
            String vcsId = codeConfig.getVcsId();
            JSONObject codeMetadata = sWorkerOpenBizService.getCodeMetadata(buildTaskBO.getTenantId(), vcsId);
            String httpPath = codeMetadata.getString("httpPath");
            codeConfig.setVcsRepository(httpPath);
            buildCodeConfigBizService.updateBuildCodeConfig(codeConfig);
        } else {
            codeConfig.setTaskId(buildTaskBO.getId());
            codeConfig.setVcsCloneType("branch");
            buildCodeConfigBizService.createBuildCodeConfig(codeConfig);
        }
    }

    private void saveBuildScript(BuildTaskBO buildTaskBO) {
        BuildScriptBO buildScript = buildTaskBO.getBuildScript();
        buildScript.setSpaceId(buildTaskBO.getSpaceId());
        buildScript.setTenantId(buildTaskBO.getTenantId());
        buildScript.setCreateUid(buildTaskBO.getCreateUid());
        if (EmptyValidator.isNotEmpty(buildScript.getId())) {
            buildScriptBizService.updateBuildScript(buildScript);
        } else {
            buildScript.setTaskId(buildTaskBO.getId());
            buildScriptBizService.createBuildScript(buildScript);
        }
    }

    private void saveBuildSteps(BuildTaskBO buildTaskBO) {
        List<BuildStepConfigBO> stepConfigs = buildStepConfigBizService.getBuildStepConfigListByTaskId(buildTaskBO.getId());
        stepConfigs.forEach(buildStepConfigBizService::deleteBuildStepConfig);
        if (EmptyValidator.isNotEmpty(buildTaskBO.getStepConfigs())) {
            for (int i = 0; i < buildTaskBO.getStepConfigs().size(); i++) {
                BuildStepConfigBO ele = buildTaskBO.getStepConfigs().get(i);
                ele.setSpaceId(buildTaskBO.getSpaceId());
                ele.setTenantId(buildTaskBO.getTenantId());
                ele.setTaskId(buildTaskBO.getId());
                ele.setSerial(i);
                ele.setSpaceId(buildTaskBO.getSpaceId());
                ele.setTenantId(buildTaskBO.getTenantId());
                ele.setCreateUid(UserUtils.getUserId());
                buildStepConfigBizService.createBuildStepConfig(ele);
            }
        }
    }

    private void setBuildParamList(BuildTaskBO buildTaskBO) {
        if (EmptyValidator.isEmpty(buildTaskBO.getParameters())) {
            buildTaskBO.setParameters(new ArrayList<>());
        }
        List<BuildParameterBO> buildParamDTOS = buildTaskBO.getParameters();
        buildParamDTOS.forEach(ele -> {
            ele.setTaskId(buildTaskBO.getId());
            ele.setSpaceId(buildTaskBO.getSpaceId());
            ele.setTenantId(buildTaskBO.getTenantId());
            ele.setCreateUid(UserUtils.getUserId());
        });
        buildParameterBizService.batchSave(buildParamDTOS);
    }

    private void saveBuildAdvancedConfig(BuildTaskBO buildTaskBO) {
        BuildAdvancedConfigBO advancedConfig = buildTaskBO.getAdvancedConfig();
        advancedConfig.setSpaceId(buildTaskBO.getSpaceId());
        advancedConfig.setTenantId(buildTaskBO.getTenantId());
        advancedConfig.setCreateUid(UserUtils.getUserId());
        if (EmptyValidator.isNotEmpty(advancedConfig.getId())) {
            buildAdvancedConfigBizService.updateBuildAdvancedConfig(advancedConfig);
        } else {
            advancedConfig.setTaskId(buildTaskBO.getId());
            buildAdvancedConfigBizService.createBuildAdvancedConfig(advancedConfig);
        }
    }

    private void saveDefaultCache(BuildTaskBO buildTaskBO) {
        // Maven缓存
        BuildCacheBO buildCacheBO = new BuildCacheBO();
        buildCacheBO.setTaskId(buildTaskBO.getId());
        buildCacheBO.setHasOpen(true);
        buildCacheBO.setDirectory("/root/.m2");
        buildCacheBO.setTenantId(buildTaskBO.getTenantId());
        buildCacheBO.setSpaceId(buildTaskBO.getSpaceId());
        buildCacheBO.setCreateUid(UserUtils.getUserId());
        buildCacheBizService.createBuildCache(buildCacheBO);
        // "gradle依赖缓存":"/root/.gradle/caches",
        BuildCacheBO buildCacheBO1 = new BuildCacheBO();
        buildCacheBO1.setTaskId(buildTaskBO.getId());
        buildCacheBO1.setHasOpen(true);
        buildCacheBO1.setDirectory("/root/.gradle/caches");
        buildCacheBO1.setTenantId(buildTaskBO.getTenantId());
        buildCacheBO1.setSpaceId(buildTaskBO.getSpaceId());
        buildCacheBO1.setCreateUid(UserUtils.getUserId());
        buildCacheBizService.createBuildCache(buildCacheBO1);
        //    "npm依赖全局缓存":"/root/.npm",
        BuildCacheBO buildCacheBO2 = new BuildCacheBO();
        buildCacheBO2.setTaskId(buildTaskBO.getId());
        buildCacheBO2.setHasOpen(true);
        buildCacheBO2.setDirectory("/root/.npm");
        buildCacheBO2.setTenantId(buildTaskBO.getTenantId());
        buildCacheBO2.setSpaceId(buildTaskBO.getSpaceId());
        buildCacheBO2.setCreateUid(UserUtils.getUserId());
        buildCacheBizService.createBuildCache(buildCacheBO2);
        //    "yarn依赖全局缓存":"/root/.yarn",
        BuildCacheBO buildCacheBO3 = new BuildCacheBO();
        buildCacheBO3.setTaskId(buildTaskBO.getId());
        buildCacheBO3.setHasOpen(true);
        buildCacheBO3.setDirectory("/root/.yarn");
        buildCacheBO3.setTenantId(buildTaskBO.getTenantId());
        buildCacheBO3.setSpaceId(buildTaskBO.getSpaceId());
        buildCacheBO3.setCreateUid(UserUtils.getUserId());
        buildCacheBizService.createBuildCache(buildCacheBO3);
        //    "go mod缓存":"/go/pkg/mod",
        BuildCacheBO buildCacheBO4 = new BuildCacheBO();
        buildCacheBO4.setTaskId(buildTaskBO.getId());
        buildCacheBO4.setHasOpen(true);
        buildCacheBO4.setDirectory("/go/pkg/mod");
        buildCacheBO4.setTenantId(buildTaskBO.getTenantId());
        buildCacheBO4.setSpaceId(buildTaskBO.getSpaceId());
        buildCacheBO4.setCreateUid(UserUtils.getUserId());
        buildCacheBizService.createBuildCache(buildCacheBO4);
        //    "其它缓存":"/root/.cache
        BuildCacheBO buildCacheBO5 = new BuildCacheBO();
        buildCacheBO5.setTaskId(buildTaskBO.getId());
        buildCacheBO5.setHasOpen(true);
        buildCacheBO5.setDirectory("/root/.cache");
        buildCacheBO5.setTenantId(buildTaskBO.getTenantId());
        buildCacheBO5.setSpaceId(buildTaskBO.getSpaceId());
        buildCacheBO5.setCreateUid(UserUtils.getUserId());
        buildCacheBizService.createBuildCache(buildCacheBO5);
        //    "C/C++缓存":"/root/.ccache
        BuildCacheBO buildCacheBO6 = new BuildCacheBO();
        buildCacheBO6.setTaskId(buildTaskBO.getId());
        buildCacheBO6.setHasOpen(true);
        buildCacheBO6.setDirectory("/root/.ccache");
        buildCacheBO6.setTenantId(buildTaskBO.getTenantId());
        buildCacheBO6.setSpaceId(buildTaskBO.getSpaceId());
        buildCacheBO6.setCreateUid(UserUtils.getUserId());
        buildCacheBizService.createBuildCache(buildCacheBO6);
    }

    @Override
    public void deleteTask(String taskId) {
        checkAndDeleteRelatedData(Collections.singletonList(taskId));
        buildTaskAtomService.removeById(taskId);
    }

    @Override
    public void batchDeleteTask(List<String> taskIds) {
        checkAndDeleteRelatedData(taskIds);
        buildTaskAtomService.removeByIds(taskIds);
    }

    /**
     * 删除构建任务时关联删除其他数据
     */
    private void checkAndDeleteRelatedData(List<String> taskIds) {
        // 校验任务状态
        for (int i = 0; i < taskIds.size(); i++) {
            String taskId = taskIds.get(i);
            BuildTaskDO taskDO = buildTaskAtomService.getById(taskId);
            if (String.valueOf(StatusEnum.PENDING.getCode()).equals(taskDO.getBuildStatus()) || String.valueOf(StatusEnum.QUEUED.getCode()).equals(taskDO.getBuildStatus()) || String.valueOf(StatusEnum.RUNNING.getCode()).equals(taskDO.getBuildStatus())) {
                throw new BusinessException("待执行/排队中/执行中状态的任务无法删除");
            }
        }
        buildStepConfigBizService.deleteByTaskIds(taskIds);
        buildCodeConfigBizService.deleteByTaskIds(taskIds);
//        buildAdvancedConfigBizService.deleteByTaskIds(taskIds);
        buildCacheBizService.deleteByTaskIds(taskIds);
        buildParameterBizService.deleteByTaskIds(taskIds);
        this.deleteBuildTriggerConfig(taskIds);
    }

    @Override
    public void updateTask(BuildTaskBO buildTaskBO) {
        checkTaskIsDisable(buildTaskBO.getId());
        this.saveBuildCodeConfig(buildTaskBO);
        this.saveSystemProject(buildTaskBO);
        this.saveBuildSteps(buildTaskBO);
        this.saveBuildScript(buildTaskBO);
        //this.saveBuildAdvancedConfig(buildTaskBO);
        this.saveBuildTriggerConfig(buildTaskBO);
        BuildTaskDO buildTaskDO = BeanCloner.clone(buildTaskBO, BuildTaskDO.class);
        buildTaskDO.setUpdateUid(UserUtils.getUserId());
        buildTaskAtomService.updateById(buildTaskDO);

        // 保存历史记录
        this.saveHistory(buildTaskBO);
    }

    private void saveSystemProject(BuildTaskBO buildTaskBO) {
        BuildCodeConfigBO codeConfig = buildTaskBO.getCodeConfig();
        JSONObject codeRepoMetaData = sWorkerOpenBizService.getCodeRepoMetaData(codeConfig.getVcsId(), UserUtils.getTenantId());
        buildTaskBO.setSystemCode(codeRepoMetaData.getString("systemCode"));
        //删除旧的项目关联关系
        LambdaQueryWrapper<BuildProjectRelationDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BuildProjectRelationDO::getTaskId, buildTaskBO.getId());
        buildProjectRelationAtomService.remove(queryWrapper);

        JSONArray projects = codeRepoMetaData.getJSONArray("projects");
        List<BuildProjectRelationDO> list = new ArrayList<>();
        for (int i = 0; i < projects.size(); i++) {
            JSONObject project = projects.getJSONObject(i);
            BuildProjectRelationDO buildProjectRelationDO = new BuildProjectRelationDO();
            buildProjectRelationDO.setTaskId(buildTaskBO.getId());
            buildProjectRelationDO.setProjectCode(project.getString("projectCode"));
            buildProjectRelationDO.setTenantId(UserUtils.getTenantId());
            buildProjectRelationDO.setCreateUid(UserUtils.getUserId());
            list.add(buildProjectRelationDO);
        }
        buildProjectRelationAtomService.saveBatch(list);
    }

    public void saveHistory(BuildTaskBO buildTaskBO) {
        BuildTaskBO bo = this.getTask(buildTaskBO.getId());
        BuildVersionBO buildVersionBO = new BuildVersionBO();
        buildVersionBO.setTaskId(buildTaskBO.getId());
        bo.setLastBuildId(null);
        bo.setBuildNumber(null);
        bo.setBuildStatus(null);
        bo.setLastBuildTime(null);
        buildVersionBO.setBuildVersionContext(JSONObject.toJSONString(bo));
        buildVersionBO.setCreateUid(UserUtils.getUserId());
        buildVersionBizService.createBuildVersion(buildVersionBO);
    }

    @Override
    public BuildTaskBO getTask(String taskId) {
        BuildTaskDO buildTaskDO = buildTaskAtomService.getById(taskId);
        if (EmptyValidator.isEmpty(buildTaskDO)) {
            throw new BusinessException("构建任务不存在");
        }
        BuildTaskBO buildTaskBO = BeanCloner.clone(buildTaskDO, BuildTaskBO.class);
        // 获取代码配置
        BuildCodeConfigBO buildCodeConfigBO = buildCodeConfigBizService.getBuildCodeConfigByTaskId(taskId);

        BuildScriptBO buildScriptBO = buildScriptBizService.getBuildScriptByTaskId(taskId);
        // 获取步骤配置
        List<BuildStepConfigBO> buildStepConfigBOList = buildStepConfigBizService.getBuildStepConfigListByTaskId(taskId);
        // 获取参数
        List<BuildParameterBO> buildParameterBOList = buildParameterBizService.listByTaskId(taskId);
        List<BuildCacheBO> buildCacheBOS = buildCacheBizService.getListByTaskId(taskId);
        // 获取高级配置
//        BuildAdvancedConfigBO buildAdvancedConfigBO = buildAdvancedConfigBizService.getBuildAdvancedConfigByTaskId(taskId);
        // 获取执行计划配置
        BuildTriggerConfigBO buildTriggerConfigBO = buildTriggerConfigBizService.getBuildTriggerConfigByTaskId(taskId);
        //todo 获取系统和项目名称
        //获取关联项目
        List<String> projects = new ArrayList<>();
        LambdaQueryWrapper<BuildProjectRelationDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BuildProjectRelationDO::getTaskId, taskId);
        List<BuildProjectRelationDO> list = buildProjectRelationAtomService.list(queryWrapper);
        list.forEach(item -> {
            projects.add(item.getProjectCode());
        });

        buildTaskBO.setCodeConfig(buildCodeConfigBO);
        buildTaskBO.setStepConfigs(buildStepConfigBOList);
        buildTaskBO.setParameters(buildParameterBOList);
//        buildTaskBO.setAdvancedConfig(buildAdvancedConfigBO);
        buildTaskBO.setBuildScript(buildScriptBO);
        buildTaskBO.setCaches(buildCacheBOS);
        buildTaskBO.setBuildTriggerConfig(buildTriggerConfigBO);
        buildTaskBO.setProjects(String.join(",", projects));
        return buildTaskBO;
    }

    @Override
    public String startTask(String taskId, String source) {
        checkTaskIsDisable(taskId);
        BuildTaskBO buildTaskBO = this.getTask(taskId);
        return this.startTask(buildTaskBO, source);
    }

    @Override
    public String startTask(BuildTaskBO buildTaskBO, String source) {
        // 启动校验
        BuildCodeConfigBO buildCodeConfigBO = buildTaskBO.getCodeConfig();
        if (EmptyValidator.isEmpty(buildCodeConfigBO.getVcsId())) {
            throw new BusinessException("代码仓库没有配置");
        }
        //BuildAdvancedConfigBO buildAdvancedConfigBO = buildTaskBO.getAdvancedConfig();
        if (EmptyValidator.isEmpty(buildTaskBO.getAssignedNode())) {
            throw new BusinessException("没有选择构建节点");
        }

        checkTaskIsDisable(buildTaskBO.getId());
        UserInfo userInfo = JwtHeaderInfo.getUserInfo();
        String createUid = "";
        if (EmptyValidator.isEmpty(userInfo)) {
            createUid = buildTaskBO.getCreateUid();
        } else {
            createUid = userInfo.getUserId();
        }
        //生成记录
        BuildSnapshotBO buildSnapshotBO = new BuildSnapshotBO();
        buildSnapshotBO.setTaskId(buildTaskBO.getId());
        buildSnapshotBO.setId(UUIDGenerator.create());
        buildSnapshotBO.setBuildStatus(StatusEnum.RUNNING.getCode().toString());
        buildSnapshotBO.setSpaceId(buildTaskBO.getSpaceId());
        buildSnapshotBO.setTenantId(buildTaskBO.getTenantId());
        buildSnapshotBO.setCreateUid(createUid);
        buildSnapshotBO.setSource(source);

        String userId = buildSnapshotBO.getCreateUid();
        String tenantId = buildSnapshotBO.getTenantId();

        // 获取代码仓库详细信息
        BuildCodeConfigBO codeConfig = buildTaskBO.getCodeConfig();
        log.info("获取代码codeConfig:{}", codeConfig);
        JSONObject commitMsg = null;
        if ("commit".equals(codeConfig.getVcsCloneType())) {
            commitMsg = sWorkerOpenBizService.getCommitMsg(codeConfig.getTenantId(), codeConfig.getVcsId(), codeConfig.getVcsBranch(), userId);
            if (EmptyValidator.isEmpty(commitMsg)) {
                throw new BusinessException("代码commit输入异常");
            }
        } else if ("branch".equals(codeConfig.getVcsCloneType())) {
            commitMsg = sWorkerOpenBizService.getLastCommit(codeConfig.getTenantId(), codeConfig.getVcsId(), codeConfig.getVcsBranch(), userId);
            if (EmptyValidator.isEmpty(commitMsg)) {
                throw new BusinessException("获取代码分支最新提交记录异常");
            }
        } else {
            JSONArray tagList = sWorkerOpenBizService.getTagList(codeConfig.getTenantId(), codeConfig.getVcsId(), userId);
            String commitId = "";
            for (int i = 0; i < tagList.size(); i++) {
                if (tagList.getJSONObject(i).getString("name").equals(codeConfig.getVcsBranch())) {
                    commitId = tagList.getJSONObject(i).getString("createBaseCommitId");
                }
            }
            commitMsg = sWorkerOpenBizService.getCommitMsg(codeConfig.getTenantId(), codeConfig.getVcsId(), commitId, userId);
            if (EmptyValidator.isEmpty(commitMsg)) {
                throw new BusinessException("获取代码TAG提交记录异常");
            }
        }
        codeConfig.setCommitMsg(commitMsg);


        //获取默认的参数类型
        this.defaultParams(buildTaskBO, buildSnapshotBO, commitMsg);
        String jobXml = this.modifyJenkinsBuildJobByPipeline(buildTaskBO, buildSnapshotBO);
        // 获取个人密钥
        Map<String, String> privateToken = sWorkerOpenBizService.privateToken(tenantId, userId);
        Map<String, String> params = new HashMap<>();
        buildTaskBO.getParameters().forEach(ele -> params.put(ele.getParamName(), ele.getParamValue()));
        CiScheduleRequest ciScheduleR = new CiScheduleRequest();
        // 获取docker凭证
        List<DockerCredential> dockerCredentials = getDockerCredential(buildTaskBO);
        // 获取CI节点信息
        Map<String, String> ciToolEnvs = buildNodeBizService.getCiToolEnvs(buildTaskBO.getAssignedNode());
        BuildScheduleSnapshotBO ciScheduleBuilder = BuildScheduleSnapshotBO.builder()
                .id(buildSnapshotBO.getId())
                .ciScheduleUrl(ciToolEnvs.get("ciScheduleUrl"))
                .ciToolUrl(ciToolEnvs.get("ciToolUrl"))
                .token(privateToken.get("token"))
                .userName(privateToken.get("userAccount"))
                .jobXml(jobXml)
                .params(params)
                .dockerCredentials(dockerCredentials)
                .build();

        ciScheduleR.setBuildTaskDTO(ciScheduleBuilder);
        buildScheduleSnapshotBizService.create(ciScheduleBuilder);
        // 判断是否需要加入队列
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPageNo(1);
        pageRequest.setPageSize(20);
        BuildSnapshotBO queryBuildSnapshotBO = new BuildSnapshotBO();
        queryBuildSnapshotBO.setBuildStatus(StatusEnum.RUNNING.getCode().toString());
        queryBuildSnapshotBO.setTaskId(buildTaskBO.getId());
        PageResponse<List<BuildSnapshotBO>> buildSonapshotList = buildSnapshotBizService.list(pageRequest, queryBuildSnapshotBO);
        long count = buildSonapshotList.getCount();
        boolean startFlag = true;
        if (count >= buildTaskBO.getMaxConcurrentCount()) {
            if (buildTaskBO.getConcurrentStrategy().equals(1)) {
                buildSnapshotBO.setBuildStatus(StatusEnum.QUEUED.getCode().toString());
                startFlag = false;
            } else {
                throw new BusinessException("任务超过并发数,已忽略执行");
            }
        }
        // 启动任务
        if (startFlag) {
            ciScheduleBizService.startTask(ciScheduleR);
        }
        buildSnapshotBizService.createBuildSnapshot(buildSnapshotBO);
        // 更新任务状态
        buildTaskBO.setBuildStatus(StatusEnum.RUNNING.getCode().toString());
        buildTaskBO.setLastBuildId(buildSnapshotBO.getId());
        buildTaskBO.setLastBuildTime(LocalDateTime.now());
        buildTaskBO.setBuildNumber(buildSnapshotBO.getBuildNumber() + "");
        BuildTaskDO buildTaskDO = BeanCloner.clone(buildTaskBO, BuildTaskDO.class);
        buildTaskAtomService.updateById(buildTaskDO);
        // 保存代码检出的步驟
        this.saveBuildStepSnapshotList(buildTaskBO, buildSnapshotBO);
        // 生成代码快照
        this.saveBuildCodeSnapshot(codeConfig, buildSnapshotBO);
        // 生成参数快照
        this.saveBuildParametersSnapshot(buildTaskBO, buildSnapshotBO);
        // 放到调度里面
        if (startFlag) {
            buildSnapshotBizService.addJob(buildSnapshotBO.getId());
        }
        return buildSnapshotBO.getId();
    }

    private List<DockerCredential> getDockerCredential(BuildTaskBO buildTaskBO) {
        List<DockerCredential> dockerCredentialList = new ArrayList<>();
        Map<String, String> dockerCredentialMap = new HashMap<>();
        buildTaskBO.getStepConfigs().stream().filter(step -> step.getType().equals("customize")).forEach(step -> {
            BuildStepsCustomizeBO buildStepsCustomizeBO = step.getConfig().toJavaObject(BuildStepsCustomizeBO.class);
            if (!dockerCredentialMap.containsKey(buildStepsCustomizeBO.getRepositoryU())) {
                dockerCredentialMap.put(buildStepsCustomizeBO.getRepositoryU(), buildStepsCustomizeBO.getRepositoryP());
            }
        });
        dockerCredentialMap.forEach((k, v) -> {
            DockerCredential dockerCredential = new DockerCredential();
            dockerCredential.setUserKey(k);
            dockerCredential.setUserToken(v);
            dockerCredentialList.add(dockerCredential);
        });
        return dockerCredentialList;
    }

    void saveBuildStepSnapshotList(BuildTaskBO buildTaskBO, BuildSnapshotBO buildSnapshotBO) {
        BuildStepSnapshotBO buildStepSnapshotBO = new BuildStepSnapshotBO();
        buildStepSnapshotBO.setTaskId(buildTaskBO.getId());
        buildStepSnapshotBO.setBuildSnapshotId(buildSnapshotBO.getId());
        buildStepSnapshotBO.setSerial(0);
        buildStepSnapshotBO.setName("代码检出");
        buildStepSnapshotBO.setType("vcs");
        buildStepSnapshotBO.setSpaceId(buildTaskBO.getSpaceId());
        buildStepSnapshotBO.setTenantId(buildTaskBO.getTenantId());
        buildStepSnapshotBO.setCreateUid(UserUtils.getUserId());
        buildStepSnapshotBizService.createBuildStepSnapshot(buildStepSnapshotBO);
        List<BuildStepConfigBO> buildStepConfigBOList = buildTaskBO.getStepConfigs();
        for (int i = 0; i < buildStepConfigBOList.size(); i++) {
            BuildStepConfigBO buildStepConfigBO = buildStepConfigBOList.get(i);
            if (buildStepConfigBO.getHasOpen()) {
                BuildStepSnapshotBO buildStepSnapshotNext = BeanCloner.clone(buildStepConfigBO, BuildStepSnapshotBO.class);
                buildStepSnapshotNext.setId(null);
                buildStepSnapshotNext.setSerial(i + 1);
                buildStepSnapshotNext.setBuildSnapshotId(buildSnapshotBO.getId());
                buildStepSnapshotNext.setSpaceId(buildTaskBO.getSpaceId());
                buildStepSnapshotNext.setTenantId(buildTaskBO.getTenantId());
                buildStepSnapshotNext.setCreateUid(UserUtils.getUserId());
                buildStepSnapshotBizService.createBuildStepSnapshot(buildStepSnapshotNext);
            }
        }
    }

    void saveBuildCodeSnapshot(BuildCodeConfigBO codeConfig, BuildSnapshotBO buildSnapshotBO) {
        BuildCodeSnapshotBO buildCodeSnapshotBO = new BuildCodeSnapshotBO();
        buildCodeSnapshotBO.setTaskId(codeConfig.getTaskId());
        buildCodeSnapshotBO.setBuildSnapshotId(buildSnapshotBO.getId());
        buildCodeSnapshotBO.setVcsId(codeConfig.getVcsId());
        buildCodeSnapshotBO.setVcsName(codeConfig.getVcsName());
        buildCodeSnapshotBO.setVcsRepository(codeConfig.getVcsRepository());
        buildCodeSnapshotBO.setVcsBranch(codeConfig.getVcsBranch());
        buildCodeSnapshotBO.setVcsCloneType(codeConfig.getVcsCloneType());
        buildCodeSnapshotBO.setVcsSubmodule(codeConfig.getVcsSubmodule());
        buildCodeSnapshotBO.setCommitMessage(codeConfig.getCommitMsg().toString());
        buildCodeSnapshotBO.setCommitId(codeConfig.getCommitMsg().getString("id"));
        buildCodeSnapshotBO.setVcsSubmodule(codeConfig.getVcsSubmodule());
        buildCodeSnapshotBO.setTenantId(codeConfig.getTenantId());
        buildCodeSnapshotBO.setSpaceId(buildCodeSnapshotBO.getSpaceId());
        buildCodeSnapshotBO.setTenantId(buildCodeSnapshotBO.getTenantId());
        buildCodeSnapshotBO.setCreateUid(UserUtils.getUserId());
        buildCodeSnapshotBizService.createBuildCodeSnapshot(buildCodeSnapshotBO);
    }

    void saveBuildParametersSnapshot(BuildTaskBO buildTaskBO, BuildSnapshotBO buildSnapshotBO) {
        List<BuildParameterBO> parameters = buildTaskBO.getParameters();
        parameters.forEach(ele -> {
            BuildParamSnapshotBO buildParamSnapshotBO = BeanCloner.clone(ele, BuildParamSnapshotBO.class);
            buildParamSnapshotBO.setId(null);
            buildParamSnapshotBO.setBuildSnapshotId(buildSnapshotBO.getId());
            buildParamSnapshotBO.setSpaceId(buildTaskBO.getSpaceId());
            buildParamSnapshotBO.setTenantId(buildTaskBO.getTenantId());
            buildParamSnapshotBO.setCreateUid(UserUtils.getUserId());
            buildParamSnapshotBizService.createBuildParamSnapshot(buildParamSnapshotBO);
        });
    }

    void defaultParams(BuildTaskBO buildTaskBO, BuildSnapshotBO buildSnapshotBO, JSONObject commitMsg) {
        if (EmptyValidator.isEmpty(buildTaskBO.getParameters())) {
            buildTaskBO.setParameters(new ArrayList<>());
        }
        List<BuildParameterBO> parameters = buildTaskBO.getParameters();
        BuildCodeConfigBO codeConfigBO = buildTaskBO.getCodeConfig();
        // 代码仓库
        BuildParameterBO GIT_REPO_URL = new BuildParameterBO();
        GIT_REPO_URL.setParamName("GIT_REPO_URL");
        GIT_REPO_URL.setParamValue(codeConfigBO.getVcsRepository());
        // 代码分支
        BuildParameterBO GIT_BUILD_REF = new BuildParameterBO();
        GIT_BUILD_REF.setParamName("GIT_BUILD_REF");
        GIT_BUILD_REF.setParamValue(codeConfigBO.getVcsBranch());

        // 代码commit
        BuildParameterBO GIT_COMMIT = new BuildParameterBO();
        GIT_COMMIT.setParamName("GIT_COMMIT");
        GIT_COMMIT.setParamValue(commitMsg.getString("id"));

        // BUILD_TASK_NUMBER
        BuildParameterBO BUILD_TASK_NUMBER = new BuildParameterBO();
        BUILD_TASK_NUMBER.setParamName("BUILD_TASK_NUMBER");
        QueryWrapper<BuildSnapshotDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("task_id", buildSnapshotBO.getTaskId());
        long buildNumber = buildSnapshotAtomService.count(queryWrapper) + 1;
        BUILD_TASK_NUMBER.setParamValue(buildNumber + "");

        // BUILD_TASK_TIMES ，，当前时间年月日十分秒
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter compactFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        String formatted3 = now.format(compactFormatter);
        BuildParameterBO BUILD_TASK_TIMES = new BuildParameterBO();
        BUILD_TASK_TIMES.setParamName("BUILD_TASK_TIMES");
        BUILD_TASK_TIMES.setParamValue(formatted3);

        parameters.add(GIT_REPO_URL);
        parameters.add(GIT_BUILD_REF);
        parameters.add(GIT_COMMIT);
        parameters.add(BUILD_TASK_NUMBER);
        parameters.add(BUILD_TASK_TIMES);
    }

    @Override
    public String queryLog(String buildSnapshotId, boolean all) {
        BuildSnapshotBO buildSnapshotBO = buildSnapshotBizService.getBuildSnapshotById(buildSnapshotId);
        StringBuilder logSb = new StringBuilder();
        if (buildSnapshotBO.getBuildStatus().equals(StatusEnum.RUNNING.getCode().toString())) {
            JSONArray stepLogs = ciScheduleBizService.queryLog(buildSnapshotId);
            for (int i = 0; i < stepLogs.size(); i++) {
                logSb.append(stepLogs.getJSONObject(i).getString("stageLog"));
            }
        } else {
            BuildStepSnapshotBO buildStepSnapshotQ = new BuildStepSnapshotBO();
            buildStepSnapshotQ.setBuildSnapshotId(buildSnapshotId);
            List<BuildStepSnapshotBO> list = buildStepSnapshotBizService.list(buildStepSnapshotQ);
            list.forEach(ele -> {
                try {
                    if (EmptyValidator.isNotEmpty(ele.getStepLogPath())) {
                        InputStream stream = ossClient.getInputStream(ele.getStepLogPath());
                        // 读取文件内容
                        byte[] content = IOUtils.toByteArray(stream);
                        logSb.append(new String(content, StandardCharsets.UTF_8));
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });

        }
        // logSb只保留最后的500行
        if (!all) {
            return this.getLast500Lines(logSb.toString()).replaceAll("jenkins", "");
        }
        return logSb.toString().replaceAll("jenkins", "");
    }

    @Override
    public BuildSnapshotBO getBuildSnapshot(String buildSnapshotId) {
        return buildSnapshotBizService.getBuildSnapshotById(buildSnapshotId);
    }

    public String getLast500Lines(String input) {
        if (input == null || input.isEmpty()) {
            return "";
        }

        // 按行分割（兼容 Unix \n 和 Windows \r\n）
        String[] lines = input.split("\\r?\\n");

        // 如果不超过500行，直接返回原始内容
        if (lines.length <= 500) {
            return input;
        }

        // 否则拼接最后500行
        StringBuilder result = new StringBuilder();
        for (int i = lines.length - 500; i < lines.length; i++) {
            result.append(lines[i]).append("\n");
        }

        return result.toString();
    }

    @Override
    public List<BuildStepSnapshotBO> getBuildStepSnapshots(String buildSnapshotId) {
        BuildSnapshotBO buildSnapshotBO = buildSnapshotBizService.getBuildSnapshotById(buildSnapshotId);
        BuildStepSnapshotBO buildStepSnapshotQ = new BuildStepSnapshotBO();
        buildStepSnapshotQ.setBuildSnapshotId(buildSnapshotId);
        List<BuildStepSnapshotBO> list = buildStepSnapshotBizService.list(buildStepSnapshotQ);
        if (buildSnapshotBO.getBuildStatus().equals(StatusEnum.RUNNING.getCode().toString())) {
            JSONArray Stage = ciScheduleBizService.queryStage(buildSnapshotId);
            for (int i = 0; i < Stage.size(); i++) {
                list.get(i).setStepResult(Stage.getJSONObject(i).getString("result"));
                list.get(i).setStepState(Stage.getJSONObject(i).getString("state"));
                list.get(i).setDuration(Stage.getJSONObject(i).getInteger("durationInMillis"));
                list.get(i).setStartTime(Stage.getJSONObject(i).getLocalDateTime("startTime"));
            }
        }
        return list;
    }

    @Override
    public void stopTask(String buildSnapshotId) {
        BuildSnapshotBO buildSnapshotBO = buildSnapshotBizService.getBuildSnapshotById(buildSnapshotId);
        checkTaskIsDisable(buildSnapshotBO.getTaskId());
        if (EmptyValidator.isNotEmpty(buildSnapshotBO)) {
            if (buildSnapshotBO.getBuildStatus().equals(StatusEnum.RUNNING.getCode().toString())) {
                ciScheduleBizService.stopTask(buildSnapshotId);
                buildSnapshotBO.setBuildStatus(StatusEnum.STOP.getCode().toString());
                buildSnapshotBizService.updateBuildSnapshot(buildSnapshotBO);
                BuildTaskDO buildTask = buildTaskAtomService.getById(buildSnapshotBO.getTaskId());
                buildTask.setBuildStatus(StatusEnum.STOP.getCode().toString());
                buildTaskAtomService.updateById(buildTask);
            }
        }
    }

    @Override
    public void disableTask(String taskId) {
        // 判断是否存在执行中构建，如果存在，则无法禁用
        LambdaQueryWrapper<BuildSnapshotDO> wrapper = new LambdaQueryWrapper<>(BuildSnapshotDO.class)
                .eq(BuildSnapshotDO::getTaskId, taskId)
                .and(status -> status.eq(BuildSnapshotDO::getBuildStatus, StatusEnum.PENDING.getCode().toString())
                        .or(status1 -> status1.eq(BuildSnapshotDO::getBuildStatus, StatusEnum.QUEUED.getCode().toString()))
                        .or(status2 -> status2.eq(BuildSnapshotDO::getBuildStatus, StatusEnum.RUNNING.getCode().toString()))
                );
        long count = buildSnapshotAtomService.count(wrapper);
        if (count > 0) {
            throw new BusinessException("存在执行中的构建任务，无法禁用");
        }
        BuildTaskDO buildTaskDO = new BuildTaskDO();
        buildTaskDO.setId(taskId);
        buildTaskDO.setHasDisable(true);
        buildTaskAtomService.updateById(buildTaskDO);
    }

    @Override
    public void enableTask(String taskId) {
        BuildTaskDO buildTaskDO = new BuildTaskDO();
        buildTaskDO.setId(taskId);
        buildTaskDO.setHasDisable(false);
        buildTaskAtomService.updateById(buildTaskDO);
    }

    /**
     * 校验构建任务是否已禁用
     */
    private void checkTaskIsDisable(String taskId) {
        LambdaQueryWrapper<BuildTaskDO> queryWrapper = new LambdaQueryWrapper<BuildTaskDO>().eq(BuildTaskDO::getId, taskId).eq(BuildTaskDO::getHasDisable, true);
        if (buildTaskAtomService.count(queryWrapper) > 0) {
            throw new BusinessException("当前任务已禁用，无法执行该操作");
        }
    }

    private String modifyJenkinsBuildJobByPipeline(BuildTaskBO buildTaskBO, BuildSnapshotBO buildSnapshotBO) {
        JenkinsBuildTaskBO jenkinsBuildTaskBO = new JenkinsBuildTaskBO();
        // 设置节点
        Integer assignedNode = buildTaskBO.getAssignedNode();
        BuildNodeBO buildNodeBO = buildNodeBizService.getBuildNodeById(assignedNode);
        BuildEnvironmentBO buildEnvironmentBO = buildEnvironmentBizService.detail(buildNodeBO.getBuildEnvironmentId());
        String node = getNode(buildTaskBO, buildNodeBO, buildSnapshotBO);

        // 设置源码
        String vcsStage = getVcsStage(buildTaskBO, buildSnapshotBO);

        // 设置缓存
        // 设置超时等命令
        String optionShell = getOption(buildTaskBO);

        // 设置入参
        // 设置构建
        String buildShell = getBuildStage(buildTaskBO, buildSnapshotBO, buildEnvironmentBO);

        // 最终形成pipelineShell
        StringBuilder pipelineShell = new StringBuilder();
        pipelineShell
                .append(getCustom())
                .append("pipeline {\n")
                .append(node)
                .append(optionShell)
                .append("stages  {\n")
                .append(vcsStage)
                .append(buildShell)
                .append("}\n")
                .append(getPost())
                .append("}");

        jenkinsBuildTaskBO.setParamList(buildTaskBO.getParameters());
        jenkinsBuildTaskBO.setJenkinsProjectName(buildTaskBO.getId());
        jenkinsBuildTaskBO.setPipelineShell(pipelineShell.toString());

        Map params = new BeanMap(jenkinsBuildTaskBO);


        String jobXml = TemplateUtil.getRequestXml(params, "META-INF/jenkins/template/pipeline.xml");
        log.debug("jobXml:{}", jobXml);
        return jobXml;
    }

    private String getCustom() {
        StringBuilder sb = new StringBuilder();
        sb.append("""
                def strictShNoOut(String cmd) {
                    sh(script: "#!/bin/bash -e\\n${cmd} > /dev/null 2>&1")
                }
                """);
        sb.append("""
                def strictSh(String cmd) {
                    sh(script: "#!/bin/bash -e\\n${cmd}", returnStdout: false)
                }
                """);
        String noOutputSh = """
                def noOutputSh(cmd) {
                    return sh (script: '#!/bin/sh -e\\n' + cmd, returnStatus: true)
                }
                def metadata = [artifacts: []]
                """;
        sb.append(noOutputSh);
        return sb.toString();
    }

    private String getPost() {
        StringBuilder sb = new StringBuilder();
        String post = "    post {\n" +
                "        always {\n" +
                "            script {\n" +
                "                // 在 post 部分将元数据写入文件并归档\n" +
                "                writeFile file: 'metadata.json', text: groovy.json.JsonOutput.toJson(metadata)\n" +
                "                // 将文件保存为 Artifact\n" +
                "                archiveArtifacts artifacts: 'metadata.json'\n" +
                "            }\n" +
                "        }\n" +
                "    }\n";
        sb.append(post);
        return sb.toString();
    }

    private String getNode(BuildTaskBO buildTaskBO, BuildNodeBO buildNodeBO, BuildSnapshotBO buildSnapshotBO) {

        String agent = "";
        if (K8S.equals(buildNodeBO.getBuildNodeType())) {
            String k8sYaml = getK8sYaml().replace("jenkins-slave-image", slaveImage);
            agent = "agent {\n" +
                    "kubernetes {\n" +
                    "yaml '''\n" +
                    k8sYaml +
                    "'''\n" +
                    "}" +
                    "}\n";
        } else {
            agent = "agent {\n" +
                    "label '" + buildSnapshotBO.getId() + "'\n" +
                    "}\n";
        }

        return agent;
    }

    private String getK8sYaml() {
        try {
            // 如果classpath中没有找到，尝试从外部路径读取
            Resource resource = resourceLoader.getResource("classpath:" + "jenkins-kubernetes.yml");
            // 尝试从classpath读取
            if (resource.exists() || resource.isReadable()) { // 使用isReadable()来确保资源可读
                try (InputStream inputStream = resource.getInputStream();
                     Scanner scanner = new Scanner(inputStream, StandardCharsets.UTF_8.name())) {
                    return scanner.useDelimiter("\\A").next();
                }
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
        return "";
    }

    private String getVcsStage(BuildTaskBO buildTaskBO, BuildSnapshotBO buildSnapshotBO) {
        BuildCodeConfigBO CodeConfig = buildTaskBO.getCodeConfig();
        StringBuilder vcsShell = new StringBuilder();
        vcsShell.append("stage('1-代码检出') {\n" +
                "            steps {\n");
        vcsShell.append("checkout([\n" +
                "          $class: 'GitSCM',\n" +
                "          branches: [[name: env.GIT_BUILD_REF]],\n");
        if (EmptyValidator.isNotEmpty(CodeConfig.getVcsSubmodule()) && CodeConfig.getVcsSubmodule()) {
            vcsShell.append("          doGenerateSubmoduleConfigurations: false,\n" +
                    "          // 此处配置 Submodule 的检出规则\n" +
                    "          extensions: [[\n" +
                    "          $class: 'SubmoduleOption',\n" +
                    "           // 是否禁用检出 Submodule\n" +
                    "          disableSubmodules: false,\n" +
                    "          // 是否允许检出时使用 Parent Project 的用户凭据\n" +
                    "          parentCredentials: true,\n" +
                    "          // 是否递归检出所有 Submodule 的更新\n" +
                    "          recursiveSubmodules: true,\n" +
                    "          // 指定参考仓库的路径\n" +
                    "          reference: '',\n" +
                    "          // 是否追踪 .gitmodules 文件中配置的分支的最新提交\n" +
                    "          trackingSubmodules: false\n" +
                    "          ]],\n" +
                    "          submoduleCfg: [\n" +
                    "          ],\n");
        }
        vcsShell.append("userRemoteConfigs: [[\n" +
                "            url: env.GIT_REPO_URL,\n" +
                "            credentialsId: '" + buildSnapshotBO.getId() + "'\n" +
                "          ]]])\n");

        vcsShell.append("            }\n" +
                "        }\n");
        return vcsShell.toString();
    }

    private String getOption(BuildTaskBO buildTaskBO) {
        //BuildAdvancedConfigBO advancedConfigBO = buildTaskBO.getAdvancedConfig();
        int overtime = buildTaskBO.getOvertime();
        StringBuilder optionShell = new StringBuilder();
        optionShell.append("options { \n")
                .append("timeout (" + overtime + ") \n")
                .append("} \n");
        return optionShell.toString();
    }

    private String getBuildStage(BuildTaskBO buildTaskBO, BuildSnapshotBO buildSnapshotBO, BuildEnvironmentBO buildEnvironmentBO) {
        // 获取构建工具
        List<BuildToolBO> buildToolBOList = buildToolBizService.list(new BuildToolBO());

        Boolean useCache = buildEnvironmentBO.getIsUseCache();
        List<BuildCacheBO> caches = buildTaskBO.getCaches();
        if (EmptyValidator.isEmpty(caches)) {
            caches = new ArrayList<>();
        }
        //BuildAdvancedConfigBO buildAdvancedConfigBO = buildTaskBO.getAdvancedConfig();
        Integer assignedNode = buildTaskBO.getAssignedNode();
        StringBuilder buildShell = new StringBuilder();
        // 步骤排序
        String buildType = buildTaskBO.getBuildType();
        String buildCodeType = buildTaskBO.getBuildCodeType();
        List<BuildStepConfigBO> stepConfigs;
        if ("gui".equals(buildType)) {
            stepConfigs = buildTaskBO.getStepConfigs();
        } else {
            if ("online".equals(buildCodeType)) {
                stepConfigs = buildScriptBizService.getBuildStepsByScript(buildTaskBO.getBuildScript());
            } else {
                stepConfigs = buildConfigFileBizService.getBuildStepsByFileId(buildTaskBO.getFileId());
            }

        }

        // stepConfigs.sort(Comparator.comparing(BuildStepConfigBO::getSerial));
        StringBuilder stringBuffer = new StringBuilder();

        // 获取缓存目录
        String cacheDir = caches.stream().filter(BuildCacheBO::getHasOpen).map(BuildCacheBO::getDirectory).collect(Collectors.joining(","));
        if (EmptyValidator.isNotEmpty(cacheDir)) {
            String[] strings = cacheDir.split(",");
            for (String string : strings) {
                stringBuffer.append(" -v ").append(string).append(":").append(string);
            }
        }
        for (int i = 0; i < buildToolBOList.size(); i++) {
            BuildToolBO tool = buildToolBOList.get(i);
            tool.setCacheDir(stringBuffer.toString());
        }
        if (useCache) {
            if (EmptyValidator.isNotEmpty(stringBuffer)) {
                JSONObject cacheConfig = new JSONObject();
                cacheConfig.put("cacheDir", cacheDir);
                cacheConfig.put("s3Endpoint", buildEnvironmentBO.getCacheDir());
                cacheConfig.put("accessKey", buildEnvironmentBO.getAccessKey());
                cacheConfig.put("secretKey", buildEnvironmentBO.getSecretKey());

                BuildStepConfigBO downloadCacheDir = new BuildStepConfigBO();
                downloadCacheDir.setName("下载构建依赖缓存");
                downloadCacheDir.setType("downloadCacheDir");
                downloadCacheDir.setConfig(cacheConfig);
                downloadCacheDir.setSerial(1);
                downloadCacheDir.setHasOpen(true);
                stepConfigs.add(0, downloadCacheDir);

                BuildStepConfigBO uploadCacheDir = new BuildStepConfigBO();
                uploadCacheDir.setName("上传构建依赖缓存");
                uploadCacheDir.setType("uploadCacheDir");
                uploadCacheDir.setConfig(cacheConfig);
                uploadCacheDir.setSerial(stepConfigs.size());
                uploadCacheDir.setHasOpen(true);
                stepConfigs.add(stepConfigs.size(), uploadCacheDir);
            }
        }
        for (int i = 0; i < stepConfigs.size(); i++) {
            BuildStepConfigBO cipBuildStepsDTO = stepConfigs.get(i);
            cipBuildStepsDTO.setSerial(i);
            if (cipBuildStepsDTO.getHasOpen()) {
                buildShell.append(applicationContext.getBean(BuildStrategyFactory.getInstance().creator(cipBuildStepsDTO.getType()), BuildStrategy.class).stageShell(buildSnapshotBO, cipBuildStepsDTO, buildToolBOList));
            }
        }
        buildTaskBO.setStepConfigs(stepConfigs);
        return buildShell.toString();
    }

    public List<String> getTaskIdsByGroupId(Integer groupId) throws Exception {
        QueryWrapper<BuildGroupRelationDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("group_id", groupId);

        List<BuildGroupRelationDO> relations = buildGroupRelationAtomService.list(queryWrapper);

        return relations.stream()
                .map(BuildGroupRelationDO::getBusinessDataId)
                .collect(Collectors.toList());
    }

    @Override
    public List<BuildTaskBO> getTasksByGroupId(String groupId) {
        log.info("获取分组[{}]中的所有任务", groupId);

        try {
            // 查询分组中的所有任务ID
            List<String> taskIds = getTaskIdsByGroupId(Integer.valueOf(groupId));
            if (taskIds == null || taskIds.isEmpty()) {
                return new ArrayList<>();
            }
            // 查询所有任务详情
            List<BuildTaskDO> taskDOList = new ArrayList<>();
            for (String taskId : taskIds) {
                BuildTaskDO taskDO = buildTaskAtomService.getById(taskId);
                if (taskDO != null) {
                    taskDOList.add(taskDO);
                }
            }
            // 转换为业务对象返回
            return BeanCloner.clone(taskDOList, BuildTaskBO.class);
        } catch (Exception e) {
            log.error("获取分组任务异常", e);
            return new ArrayList<>();
        }
    }

    private void saveBuildTriggerConfig(BuildTaskBO buildTaskBO) {
        BuildTriggerConfigBO triggerConfig = buildTaskBO.getBuildTriggerConfig();
        triggerConfig.setSpaceId(buildTaskBO.getSpaceId());
        triggerConfig.setTenantId(buildTaskBO.getTenantId());
        triggerConfig.setCreateUid(UserUtils.getUserId());

        if (EmptyValidator.isNotEmpty(triggerConfig.getId())) {
            // 修改配置
            BuildTriggerConfigBO triggerConfigOld = buildTriggerConfigBizService.getBuildTriggerConfigById(String.valueOf(triggerConfig.getId()));
            triggerConfig.setUpdateTime(LocalDateTime.now());
            buildTriggerConfigBizService.updateBuildTriggerConfig(triggerConfig);
            // 关闭定时开关 -> 删除job
            if (triggerConfigOld.getTimeTriggerSwitch() && !triggerConfig.getTimeTriggerSwitch()) {
                try {
                    buildTaskTriggerService.deleteJob(buildTaskBO.getId());
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
        } else {
            // 配置
            triggerConfig.setTaskId(buildTaskBO.getId());
            buildTriggerConfigBizService.createBuildTriggerConfig(triggerConfig);
        }
        if(triggerConfig.getTimeTriggerSwitch() && EmptyValidator.isNotEmpty(triggerConfig.getTimeTriggerCrontab())){
            try {
                buildTaskTriggerService.createJob(triggerConfig.getTaskId(), triggerConfig.getTimeTriggerCrontab());
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }

    private void deleteBuildTriggerConfig(List<String> taskIds) {
        for (String taskId : taskIds) {
            BuildTriggerConfigBO buildTriggerConfigBO = buildTriggerConfigBizService.getBuildTriggerConfigByTaskId(taskId);
            if(buildTriggerConfigBO.getTimeTriggerSwitch()){
                try {
                    buildTaskTriggerService.deleteJob(taskId);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
        }
        buildTriggerConfigBizService.deleteByTaskIds(taskIds);
    }

    @Override
    public PageResponse<List<BuildVersionBO>> getBuildVersionList(PageRequest pageRequest, BuildVersionBO buildVersionBO) {
        return buildVersionBizService.pageBuildVersions(pageRequest, buildVersionBO);
    }

    @Override
    public String createAndStart(String taskId, BuildTaskBO buildTaskBO) {
        BuildTaskDO buildTaskDO = BeanCloner.clone(buildTaskBO, BuildTaskDO.class);
        buildTaskDO.setDeleted(false);
        buildTaskDO.setBuildSource("PIPELINE");
        buildTaskAtomService.saveOrUpdate(buildTaskDO);
        BuildCodeConfigBO codeConfig = buildTaskBO.getCodeConfig();
        String vcsId = codeConfig.getVcsId();
        JSONObject codeMetadata = sWorkerOpenBizService.getCodeMetadata(buildTaskBO.getTenantId(), vcsId);
        String httpPath = codeMetadata.getString("httpPath");
        codeConfig.setVcsRepository(httpPath);
        buildTaskBO.setMaxConcurrentCount(10);
        buildTaskBO.setConcurrentStrategy(2);
        return this.startTask(buildTaskBO, "PIPELINE");
    }


}
