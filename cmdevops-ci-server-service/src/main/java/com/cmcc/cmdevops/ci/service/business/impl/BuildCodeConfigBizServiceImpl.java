package com.cmcc.cmdevops.ci.service.business.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.atom.BuildCodeConfigAtomService;
import com.cmcc.cmdevops.ci.service.bo.BuildCodeConfigBO;
import com.cmcc.cmdevops.ci.service.business.BuildCodeConfigBizService;
import com.cmcc.cmdevops.ci.service.dao.BuildCodeConfigDO;
import com.cmcc.cmdevops.util.BeanCloner;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 构建代码配置业务实现类
 */
@Service
public class BuildCodeConfigBizServiceImpl implements BuildCodeConfigBizService {

    private final BuildCodeConfigAtomService buildCodeConfigAtomService;

    public BuildCodeConfigBizServiceImpl(BuildCodeConfigAtomService buildCodeConfigAtomService) {
        this.buildCodeConfigAtomService = buildCodeConfigAtomService;
    }

    @Override
    public void createBuildCodeConfig(BuildCodeConfigBO buildCodeConfigBO) {
        BuildCodeConfigDO buildCodeConfigDO = BeanCloner.clone(buildCodeConfigBO, BuildCodeConfigDO.class);
        buildCodeConfigAtomService.save(buildCodeConfigDO);
    }

    @Override
    public void updateBuildCodeConfig(BuildCodeConfigBO buildCodeConfigBO) {
        BuildCodeConfigDO buildCodeConfigDO = BeanCloner.clone(buildCodeConfigBO, BuildCodeConfigDO.class);

        UpdateWrapper<BuildCodeConfigDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", buildCodeConfigBO.getId());

        buildCodeConfigAtomService.update(buildCodeConfigDO, updateWrapper);
    }

    @Override
    public void deleteBuildCodeConfig(BuildCodeConfigBO buildCodeConfigBO) {
        buildCodeConfigAtomService.removeById(buildCodeConfigBO.getId());
    }

    @Override
    public BuildCodeConfigBO getBuildCodeConfigById(String id) {
        BuildCodeConfigDO buildCodeConfigDO = buildCodeConfigAtomService.getById(id);
        return (buildCodeConfigDO != null) ? BeanCloner.clone(buildCodeConfigDO, BuildCodeConfigBO.class) : new BuildCodeConfigBO();
    }

    @Override
    public BuildCodeConfigBO getBuildCodeConfigByTaskId(String taskId) {
        BuildCodeConfigDO buildCodeConfigDO = new BuildCodeConfigDO();
        buildCodeConfigDO.setTaskId(taskId);
        QueryWrapper<BuildCodeConfigDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("task_id", taskId);
        buildCodeConfigDO = buildCodeConfigAtomService.getOne(queryWrapper);
        return (buildCodeConfigDO != null) ? BeanCloner.clone(buildCodeConfigDO, BuildCodeConfigBO.class) : new BuildCodeConfigBO();
    }

    @Override
    public PageResponse<List<BuildCodeConfigBO>> pageBuildCodeConfigs(PageRequest pageRequest) {
        Page<BuildCodeConfigDO> page = new Page<>(pageRequest.getPageNo(), pageRequest.getPageSize());
        Page<BuildCodeConfigDO> result = buildCodeConfigAtomService.page(page);
        List<BuildCodeConfigBO> list = BeanCloner.clone(result.getRecords(), BuildCodeConfigBO.class);
        return PageResponse.success(list, result.getTotal(), pageRequest.getPageNo(), pageRequest.getPageSize());
    }

    @Override
    public void deleteByTaskIds(List<String> taskIds) {
        LambdaQueryWrapper<BuildCodeConfigDO> wrapper = new LambdaQueryWrapper<BuildCodeConfigDO>().in(BuildCodeConfigDO::getTaskId, taskIds);
        buildCodeConfigAtomService.remove(wrapper);
    }
}
