package com.cmcc.cmdevops.ci.service.business;

import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.bo.BuildVersionBO;
import java.util.List;

/**
 * 构建版本业务接口
 */
public interface BuildVersionBizService {
    /**
     * 创建构建版本
     * @param buildVersionBO 构建版本数据对象
     */
    void createBuildVersion(BuildVersionBO buildVersionBO);

    /**
     * 更新构建版本
     * @param buildVersionBO 构建版本数据对象
     */
    void updateBuildVersion(BuildVersionBO buildVersionBO);

    /**
     * 删除构建版本
     * @param buildVersionBO 构建版本数据对象
     */
    void deleteBuildVersion(BuildVersionBO buildVersionBO);

    /**
     * 根据ID获取构建版本
     * @param id 构建版本ID
     * @return 构建版本数据对象
     */
    BuildVersionBO getBuildVersionById(String id);

    /**
     * 分页查询构建版本列表
     * @param pageRequest 分页请求参数
     * @return 分页响应结果
     */
    PageResponse<List<BuildVersionBO>> pageBuildVersions(PageRequest pageRequest,BuildVersionBO buildVersionBO);
}
