package com.cmcc.cmdevops.ci.service.bo;

import java.util.List;

public class Metadata {
    private MetadataBasis metadataBasis;
    private MetadataPipeline metadataPipeline;
    private List<MetadataRepository> metadataRepository;
    private MetadataReport metadataReport;

    // Getters and setters
    public MetadataBasis getMetadataBasis() {
        return metadataBasis;
    }

    public void setMetadataBasis(MetadataBasis metadataBasis) {
        this.metadataBasis = metadataBasis;
    }

    public MetadataPipeline getMetadataPipeline() {
        return metadataPipeline;
    }

    public void setMetadataPipeline(MetadataPipeline metadataPipeline) {
        this.metadataPipeline = metadataPipeline;
    }

    public List<MetadataRepository> getMetadataRepository() {
        return metadataRepository;
    }

    public void setMetadataRepository(List<MetadataRepository> metadataRepository) {
        this.metadataRepository = metadataRepository;
    }

    public MetadataReport getMetadataReport() {
        return metadataReport;
    }

    public void setMetadataReport(MetadataReport metadataReport) {
        this.metadataReport = metadataReport;
    }
}
