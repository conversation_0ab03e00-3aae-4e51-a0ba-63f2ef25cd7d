package com.cmcc.cmdevops.ci.service.business.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cmcc.cmdevops.ci.service.atom.BuildCodeConfigAtomService;
import com.cmcc.cmdevops.ci.service.bo.BuildTriggerConfigBO;
import com.cmcc.cmdevops.ci.service.business.BuildCodeConfigBizService;
import com.cmcc.cmdevops.ci.service.business.BuildTaskBizService;
import com.cmcc.cmdevops.ci.service.business.BuildTriggerConfigBizService;
import com.cmcc.cmdevops.ci.service.business.util.EmptyValidator;
import com.cmcc.cmdevops.ci.service.dao.BuildCodeConfigDO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.annotation.SelectorType;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@RocketMQMessageListener(topic = "CMDEVOPS-CODE-SERVER-PROJECT-TOPIC", consumerGroup = "pipeline-execution-consumer-group", selectorType = SelectorType.TAG, selectorExpression="PROJECT-PUSH")
public class CodePushConsumer implements RocketMQListener<JSONObject> {
    @Resource
    private BuildCodeConfigAtomService buildCodeConfigAtomService;
    @Resource
    private BuildTriggerConfigBizService buildTriggerConfigBizService;
    @Resource
    private BuildTaskBizService buildTaskBizService;
     @Override
     public void onMessage(JSONObject message) {
         log.info("received message: {}", message);
		 // 业务逻辑
         String projectId = message.getJSONObject("data").getString("projectId");
         log.info("projectId: {}", projectId);
         LambdaQueryWrapper<BuildCodeConfigDO> queryWrapper = new LambdaQueryWrapper<>();
         queryWrapper.eq(BuildCodeConfigDO::getVcsId, projectId);
         queryWrapper.eq(BuildCodeConfigDO::getVcsCloneType, "branch");
         queryWrapper.eq(BuildCodeConfigDO::getVcsBranch, message.getJSONObject("data").getString("ref"));
         List<BuildCodeConfigDO> list = buildCodeConfigAtomService.list(queryWrapper);
         if (EmptyValidator.isNotEmpty(list)) {
             for (BuildCodeConfigDO buildCodeConfigDO : list) {
                 BuildTriggerConfigBO trigger = buildTriggerConfigBizService.getBuildTriggerConfigByTaskId(buildCodeConfigDO.getTaskId());
                 if (EmptyValidator.isNotEmpty(trigger.getCodeTriggerSwitch()) && trigger.getCodeTriggerSwitch()) {
                     //触发构建任务
                     buildTaskBizService.startTask(buildCodeConfigDO.getTaskId(), "CODE_PUSH");
                 }
             }
         }
     }
}
