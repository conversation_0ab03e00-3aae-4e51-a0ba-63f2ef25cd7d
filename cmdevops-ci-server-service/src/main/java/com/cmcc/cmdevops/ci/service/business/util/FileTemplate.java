package com.cmcc.cmdevops.ci.service.business.util;

import org.apache.velocity.Template;
import org.apache.velocity.app.VelocityEngine;
import org.apache.velocity.runtime.RuntimeConstants;
import org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader;

/**
 * <AUTHOR>
 */
public class FileTemplate {
//	private static VelocityEngine ve;
//
//	public static VelocityEngine getVelocityEngine() {
//		if (null == ve) {
//			synchronized (FileTemplate.class) {
//				if (null == ve) {
//					ve = new VelocityEngine();
//					ve.setProperty(RuntimeConstants.RESOURCE_LOADER, "classpath");
//					ve.setProperty("classpath.resource.loader.class", ClasspathResourceLoader.class.getName());
//					ve.init();
//				}
//			}
//		}
//		return ve;
//	}

	public static VelocityEngine getVelocityEngine() {
		VelocityEngine ve = new VelocityEngine();
		ve.setProperty(RuntimeConstants.RESOURCE_LOADERS, "classpath");
		ve.setProperty("resource.loader.classpath.class", ClasspathResourceLoader.class.getName());
		ve.init();
		return ve;
	}

	public static Template getRuleTemplate(String templateName) {
		return getVelocityEngine().getTemplate(templateName,"UTF-8");
	}
}
