package com.cmcc.cmdevops.ci.service.business.impl.buildStrategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cmcc.cmdevops.ci.service.atom.BuildDictAtomService;
import com.cmcc.cmdevops.ci.service.bo.BuildSnapshotBO;
import com.cmcc.cmdevops.ci.service.bo.BuildStepConfigBO;
import com.cmcc.cmdevops.ci.service.bo.BuildStepsMavenBO;
import com.cmcc.cmdevops.ci.service.bo.BuildToolBO;
import com.cmcc.cmdevops.ci.service.business.BuildStrategy;
import com.cmcc.cmdevops.ci.service.business.util.SpringUtils;
import com.cmcc.cmdevops.ci.service.dao.BuildDictDO;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * Created by 51746 on 2021/2/25.
 */
@Service
public class MavenBuildStrategy implements BuildStrategy {

    @Override
    public String stageShell(BuildSnapshotBO buildSnapshotBO, BuildStepConfigBO buildStepsBO, List<BuildToolBO> buildToolBOList) {
        StringBuilder buildShell = new StringBuilder();
        BuildStepsMavenBO cipBuildStepsMavenDTO = buildStepsBO.getConfig().toJavaObject(BuildStepsMavenBO.class);
        BuildToolBO buildToolBO = null;
        Optional<BuildToolBO> toolBOOptional = buildToolBOList.stream()
                .filter(buildTool -> buildTool.getToolName().equals(cipBuildStepsMavenDTO.getToolVersion()))
                .findAny();
        if (toolBOOptional.isPresent()) {
            buildToolBO = toolBOOptional.get();
        }
        // maven名称
        String mavenName = cipBuildStepsMavenDTO.getToolVersion().replace("maven", "");
        // jdk名称
        BuildDictAtomService buildDictAtomService = SpringUtils.getBean(BuildDictAtomService.class);
        LambdaQueryWrapper<BuildDictDO> queryWrapper = new LambdaQueryWrapper<BuildDictDO>().eq(BuildDictDO::getDictKey, cipBuildStepsMavenDTO.getJdkVersion());
        BuildDictDO buildDictDO = buildDictAtomService.getOne(queryWrapper);
        String jdkName = buildDictDO.getDictValue();
        String sdkmanCommand = "sdk default java " + jdkName + " && sdk default maven " + mavenName;
        buildShell.append("stage('").append(buildStepsBO.getName()).append("-").append(buildStepsBO.getSerial()).append("') { \n")
                .append(JenkinsUtils.getAgent(buildToolBO))
                .append("steps { \n")
                .append("  strictShNoOut('").append(sdkmanCommand).append("')\n")
                .append("sh '''").append(JenkinsUtils.commandReplace(cipBuildStepsMavenDTO.getCommand())).append("'''\n")
                .append("} \n")
                .append("} \n");
        return buildShell.toString();
    }
}
