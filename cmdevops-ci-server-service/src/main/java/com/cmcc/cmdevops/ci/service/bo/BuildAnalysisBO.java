package com.cmcc.cmdevops.ci.service.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class BuildAnalysisBO {
    /**
     * 总构建成功率
     */
    private Double buildSuccessRate;
    /**
     * 每天构建成功率
     */
    List<DayBuildRate> dailyBuildRates;
    /**
     * 构建最长时间
     */
    private Double longestBuildTime;
    /**
     * 构建最短时间
     */
    private Double shortestBuildTime;
    /**
     * 构建平均时间
     */
    private Double averageBuildTime;
    /**
     * 每个任务构建时间
     */
    private List<SnapshotBuildTime> snapshotBuildTimes;


    @Data
    @AllArgsConstructor
    public static class DayBuildRate {
        private String date;
        private Double buildSuccessRate;
    }

    @Data
    @AllArgsConstructor
    public static class SnapshotBuildTime {
        private Long buildNumber;
        private Double buildTime;
    }
}
