package com.cmcc.cmdevops.ci.service.bo;

import com.cmcc.cmdevops.PageRequest;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class BuildGroupBO extends PageRequest implements Serializable {
    private Integer id;

    private String groupName;

    private String groupDescribe;

    private String groupSign;

    private String spaceId;

    private String tenantId;

    private Boolean deleted;

    private String createUid;

    private LocalDateTime createTime;

    private String updateUid;

    private LocalDateTime updateTime;

    private String deleteUid;

    private LocalDateTime deleteTime;

    private List<String> businessDataId ;

    private String batchRelationType;
}
