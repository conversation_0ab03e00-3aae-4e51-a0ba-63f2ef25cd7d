package com.cmcc.cmdevops.ci.service.business.impl.buildStrategy;

import com.cmcc.cmdevops.ci.service.bo.BuildSnapshotBO;
import com.cmcc.cmdevops.ci.service.bo.BuildStepConfigBO;
import com.cmcc.cmdevops.ci.service.bo.BuildStepsMsbuildBO;
import com.cmcc.cmdevops.ci.service.bo.BuildToolBO;
import com.cmcc.cmdevops.ci.service.business.BuildStrategy;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class MsbuildBuildStrategy implements BuildStrategy {
    @Override
    public String stageShell(BuildSnapshotBO buildSnapshotBO, BuildStepConfigBO buildStepsBO, List<BuildToolBO> buildToolBOList) {
        StringBuilder buildShell = new StringBuilder();
        BuildStepsMsbuildBO cipBuildStepsMsbuildDTO = buildStepsBO.getConfig().toJavaObject(BuildStepsMsbuildBO.class);
        BuildToolBO buildToolBO = null;
        Optional<BuildToolBO> toolBOOptional = buildToolBOList.stream()
                .filter(buildTool -> buildTool.getToolName().equals(cipBuildStepsMsbuildDTO.getToolVersion()))
                .findAny();
        if (toolBOOptional.isPresent()) {
            buildToolBO = toolBOOptional.get();
        }
        buildShell.append("stage('").append(buildStepsBO.getName()).append("-").append(buildStepsBO.getSerial()).append("') { \n")
                .append(JenkinsUtils.getAgent(buildToolBO))
                .append("steps { \n")
                .append("sh '''").append(JenkinsUtils.commandReplace(cipBuildStepsMsbuildDTO.getCommand())).append("'''\n")
                .append("} \n")
                .append("} \n");
        return buildShell.toString();
    }
}
