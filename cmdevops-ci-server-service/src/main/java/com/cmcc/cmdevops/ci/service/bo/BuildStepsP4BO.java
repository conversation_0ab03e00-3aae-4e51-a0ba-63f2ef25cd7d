package com.cmcc.cmdevops.ci.service.bo;

import lombok.Data;

import java.io.Serializable;


/**
* <p>Title: CipBuildStepsMavenDTO</p>
* <p>Description:  </p>
* <p>Copyright: Copyright (c) 2018</p>
* <p>Company: SI-TECH </p>
* <AUTHOR>
* @version 1.0
* @createtime 2021-03-05 11:35:36
*
*/
@Data
public class BuildStepsP4BO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String toolVersion;
    /**
    *
    */
    private String command;

}
