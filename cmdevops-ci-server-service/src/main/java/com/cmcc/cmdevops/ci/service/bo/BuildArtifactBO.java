package com.cmcc.cmdevops.ci.service.bo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> @since 2025-05-20
 */
@Getter
@Setter
@Accessors(chain = true)
public class BuildArtifactBO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private String taskId;

    private String buildSnapshotId;

    private String buildNumber;

    private String buildVersion;

    private String buildArtifactName;

    private String buildArtifactRealName;

    private String buildArtifactPath;

    private String buildArtifactVersion;

    private String buildArtifactUrl;

    private String buildArtifactType;

    private String repositoryName;

    private String artifactSign;

    private String spaceId;

    private String tenantId;

    private Boolean deleted;

    private LocalDateTime createTime;

    private String createUid;

    private LocalDateTime updateTime;

    private String updateUid;

    private LocalDateTime deleteTime;

    private String deleteUid;
}
