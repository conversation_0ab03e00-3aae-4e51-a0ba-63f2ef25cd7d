package com.cmcc.cmdevops.ci.service.bo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;


/**
 * <p>Title: CipBuildTaskDTO</p>
 * <p>Description:  </p>
 * <p>Copyright: Copyright (c) 2018</p>
 * <p>Company: SI-TECH </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @createtime 2019-04-24 17:24:13
 */
@Data
public class JenkinsBuildTaskBO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 任务id
     */
    private String taskId;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 仓库代码标示
     */
    private String projectId;
    /**
     * jenkins自动创建的任务名称
     */
    private String jenkinsProjectName;
    /**
     * 仓库代码名称
     */
    private String projectName;
    /**
     * 仓库代码地址
     */
    private String projectRepository;
    /**
     *
     */
    private String packagePath;
    /**
     * 是否和云管理平台同步
     */
    private String ifSynchronous;
    /**
     * Maven/Ant
     */
    private String buildType;
    /**
     * JDK版本
     */
    private String jdkVersion;
    /**
     * Ant版本
     */
    private String antVersion;
    /**
     * Ant版本
     */
    private String mavenVersion;
    /**
     * 创建时间
     */
    private LocalDateTime utcCreate;
    /**
     * 创建人
     */
    private String userCreate;
    /**
     * 部署类型：host/docker
     */
    private String deployType;
    /**
     * Dockerfile路径(相对路径)
     */
    private String dockerfile;
    /**
     * 镜像标识
     */
    private String imageId;
    /**
     * 镜像版本
     */
    private String imageVersion;

    private String dockerRegistryUser;

    private String dockerRegistryPassword;

    private String dockerImageBuildShell;


    /**
     * 构建命令
     */
    private String buildShell;
    /**
     * 持续集成分支
     */
    private String ciBranch;
    /**
     * 发布类型：increment/full
     */
    private String releaseType;
    /**
     * 构建状态:执行中/构建成功/构建失败/构建超时
     */
    private String buildStatus;
    /**
     * 所属项目
     */
    private String ampProjectid;
    /**
     * 版本控制工具
     */
    private String versionControlTool;
    /**
     * 编译语言
     */
    private String language;
    /**
     * jenkins凭证
     */
    private String jenkinsCredentials;
    /**
     * jenkins插件目录
     */
    private String jenkinsPluginBaseDir;
    /**
     * 构建参数集合
     */
    private List<BuildParameterBO> paramList;
    /**
     * 构建参数集合
     */
    private String properties;
    /**
     * 最近构建时间
     */
    private LocalDateTime lastBuildTime;

    /**
     * 接收构建通知人员
     */
    private String receiveNotificationPerson;
    /**
     * 缓存jenkins job名称
     */
    private String dependJob;
    /**
     * 定时构建配置
     */
    private String timingBuildConfiguration;
    /**
     * 轮询scm
     */
    private String pollingScm;
    /**
     * 自定义jenkins 工作目录
     */
    private String customJobWorkspace;
    /**
     * maven构建命令
     */
    String mavenGoalsOptions;
    /**
     * build.xml targets
     */
    String antTargets;

    /*
     * 打包文件名称
     * */
    String uploadArtifactName;

    /**
     * packProjectName
     */
    private String packProjectName;

    /*
     * 回传配置
     * */
    String uploadWhere;


    private String nodeVersion;


    private String nodeConfig;


    private String goVersion;


    private String goConfig;

    private String gradleConfig;

    private String gradleVersion;

    private Integer mavenConfigOrder;

    private Integer assignedNode;

    private String pipelineShell;

}
