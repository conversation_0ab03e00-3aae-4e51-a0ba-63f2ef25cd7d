package com.cmcc.cmdevops.ci.service.business.impl.buildStrategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cmcc.cmdevops.ci.service.atom.BuildDictAtomService;
import com.cmcc.cmdevops.ci.service.bo.*;
import com.cmcc.cmdevops.ci.service.business.BuildStrategy;
import com.cmcc.cmdevops.ci.service.business.util.SpringUtils;
import com.cmcc.cmdevops.ci.service.dao.BuildDictDO;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * Created by 51746 on 2021/2/25.
 */
@Service
public class AndroidBuildStrategy implements BuildStrategy {

    @Override
    public String stageShell(BuildSnapshotBO buildSnapshotBO, BuildStepConfigBO buildStepsBO, List<BuildToolBO> buildToolBOList) {
        StringBuilder buildShell = new StringBuilder();
        BuildStepsAndroidBO cipBuildStepsAndroidDTO = buildStepsBO.getConfig().toJavaObject(BuildStepsAndroidBO.class);
        BuildToolBO buildToolBO = null;
        Optional<BuildToolBO> toolBOOptional = buildToolBOList.stream()
                .filter(buildTool -> buildTool.getToolName().equals(cipBuildStepsAndroidDTO.getToolVersion()))
                .findAny();
        if (toolBOOptional.isPresent()) {
            buildToolBO = toolBOOptional.get();
        }
        // gradle名称
        String gradleName = cipBuildStepsAndroidDTO.getToolVersion().replace("gradle", "");
        // jdk名称
        BuildDictAtomService buildDictAtomService = SpringUtils.getBean(BuildDictAtomService.class);
        LambdaQueryWrapper<BuildDictDO> queryWrapper = new LambdaQueryWrapper<BuildDictDO>().eq(BuildDictDO::getDictKey, cipBuildStepsAndroidDTO.getJdkVersion());
        BuildDictDO buildDictDO = buildDictAtomService.getOne(queryWrapper);
        String jdkName = buildDictDO.getDictValue();
        String sdkmanCommand = "sdk default java " + jdkName + " && sdk default gradle " + gradleName + "\n";
        buildShell.append("stage('").append(buildStepsBO.getName()).append("-").append(buildStepsBO.getSerial()).append("') { \n")
                .append(JenkinsUtils.getAgent(buildToolBO))
                .append("steps { \n")
                .append("sh '''\n")
                // 确保 SDK 路径配置（优先写入 local.properties）
                .append("echo \"sdk.dir=/opt/android-sdk\" > local.properties\n")  // 固定路径或从配置读取
                // 可选：同时设置环境变量（双重保障）
                .append("export ANDROID_HOME=/opt/android-sdk\n")
                // 原始命令（SDKMAN + 构建命令）
                .append(JenkinsUtils.commandReplace(sdkmanCommand + cipBuildStepsAndroidDTO.getCommand()))
                .append("\n'''\n")
                .append("} \n")
                .append("} \n");
        return buildShell.toString();
    }
}
