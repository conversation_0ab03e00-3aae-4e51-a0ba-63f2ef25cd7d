package com.cmcc.cmdevops.ci.service.business.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.atom.BuildParamSnapshotAtomService;
import com.cmcc.cmdevops.ci.service.bo.BuildParamSnapshotBO;
import com.cmcc.cmdevops.ci.service.business.BuildParamSnapshotBizService;
import com.cmcc.cmdevops.ci.service.business.util.EmptyValidator;
import com.cmcc.cmdevops.ci.service.dao.BuildParamSnapshotDO;
import com.cmcc.cmdevops.util.BeanCloner;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 构建参数快照业务实现类
 */
@Service
public class BuildParamSnapshotBizServiceImpl implements BuildParamSnapshotBizService {

    private final BuildParamSnapshotAtomService buildParamSnapshotAtomService;

    public BuildParamSnapshotBizServiceImpl(BuildParamSnapshotAtomService buildParamSnapshotAtomService) {
        this.buildParamSnapshotAtomService = buildParamSnapshotAtomService;
    }

    @Override
    public void createBuildParamSnapshot(BuildParamSnapshotBO buildParamSnapshotBO) {
        BuildParamSnapshotDO buildParamSnapshotDO = BeanCloner.clone(buildParamSnapshotBO, BuildParamSnapshotDO.class);
        buildParamSnapshotAtomService.save(buildParamSnapshotDO);
    }

    @Override
    public void updateBuildParamSnapshot(BuildParamSnapshotBO buildParamSnapshotBO) {
        BuildParamSnapshotDO buildParamSnapshotDO = BeanCloner.clone(buildParamSnapshotBO, BuildParamSnapshotDO.class);

        buildParamSnapshotAtomService.updateById(buildParamSnapshotDO);
    }

    @Override
    public void deleteBuildParamSnapshot(BuildParamSnapshotBO buildParamSnapshotBO) {
        buildParamSnapshotAtomService.removeById(buildParamSnapshotBO.getId());
    }

    @Override
    public BuildParamSnapshotBO getBuildParamSnapshotById(String id) {
        BuildParamSnapshotDO buildParamSnapshotDO = buildParamSnapshotAtomService.getById(id);
        return (buildParamSnapshotDO != null) ? BeanCloner.clone(buildParamSnapshotDO, BuildParamSnapshotBO.class) : null;
    }

    @Override
    public PageResponse<List<BuildParamSnapshotBO>> pageBuildParamSnapshots(PageRequest pageRequest) {
        Page<BuildParamSnapshotDO> page = new Page<>(pageRequest.getPageNo(), pageRequest.getPageSize());
        Page<BuildParamSnapshotDO> result = buildParamSnapshotAtomService.page(page);
        List<BuildParamSnapshotBO> list = BeanCloner.clone(result.getRecords(), BuildParamSnapshotBO.class);
        return PageResponse.success(list, result.getTotal(), pageRequest.getPageNo(), pageRequest.getPageSize());
    }

    @Override
    public List<BuildParamSnapshotBO> list(BuildParamSnapshotBO paramSnapshotBO) {
        LambdaQueryWrapper<BuildParamSnapshotDO> queryWrapper = new LambdaQueryWrapper<>();
        if (EmptyValidator.isNotEmpty(paramSnapshotBO.getTaskId())){
            queryWrapper.eq(BuildParamSnapshotDO::getTaskId, paramSnapshotBO.getTaskId());
        }
        if (EmptyValidator.isNotEmpty(paramSnapshotBO.getBuildSnapshotId())){
            queryWrapper.eq(BuildParamSnapshotDO::getBuildSnapshotId, paramSnapshotBO.getBuildSnapshotId());
        }
        if (EmptyValidator.isNotEmpty(paramSnapshotBO.getSpaceId())){
            queryWrapper.eq(BuildParamSnapshotDO::getSpaceId, paramSnapshotBO.getSpaceId());
        }
        queryWrapper.orderByDesc(BuildParamSnapshotDO::getCreateTime);
        List<BuildParamSnapshotDO> list = buildParamSnapshotAtomService.list(queryWrapper);
        return BeanCloner.clone(list, BuildParamSnapshotBO.class);
    }
}
