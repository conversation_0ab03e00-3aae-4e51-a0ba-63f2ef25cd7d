package com.cmcc.cmdevops.ci.service.business;

import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.bo.BuildParameterBO;

import java.util.List;

/**
 * 构建参数业务接口
 */
public interface BuildParameterBizService {
    /**
     * 创建构建参数配置
     *
     * @param buildParameterBO 构建参数数据对象
     */
    void save(BuildParameterBO buildParameterBO);


    void batchSave(List<BuildParameterBO> buildParameterBOs);

    /**
     * 更新构建参数配置
     *
     * @param buildParameterBO 构建参数数据对象
     */
    void update(BuildParameterBO buildParameterBO);

    /**
     * 删除构建参数配置
     *
     * @param id 构建参数数据对象id
     */
    void delete(Integer id);

    /**
     * 根据ID获取构建参数配置
     *
     * @param id 构建参数ID
     * @return 构建参数数据对象
     */
    BuildParameterBO detail(Integer id);

    List<BuildParameterBO> listByTaskId(String taskId);

    /**
     * 分页查询构建参数列表
     *
     * @param pageRequest 分页请求参数
     * @return 分页响应结果
     */
    PageResponse<List<BuildParameterBO>> page(PageRequest pageRequest, BuildParameterBO bo);

    void deleteByTaskIds(List<String> taskIds);
}
