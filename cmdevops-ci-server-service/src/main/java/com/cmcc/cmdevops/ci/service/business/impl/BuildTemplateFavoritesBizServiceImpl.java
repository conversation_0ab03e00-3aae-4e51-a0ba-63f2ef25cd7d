package com.cmcc.cmdevops.ci.service.business.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cmcc.cmdevops.ci.service.atom.BuildTemplateFavoritesBizAtomService;
import com.cmcc.cmdevops.ci.service.bo.BuildTemplateBO;
import com.cmcc.cmdevops.ci.service.business.BuildTemplateBizService;
import com.cmcc.cmdevops.ci.service.business.BuildTemplateFavoritesBizService;
import com.cmcc.cmdevops.ci.service.business.util.UserUtils;
import com.cmcc.cmdevops.ci.service.dao.BuildTemplateFavoritesDO;
import com.cmcc.cmdevops.ci.service.dao.mapper.BuildTemplateFavoritesMapper;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 构建模板收藏业务服务实现类
 */
@RequiredArgsConstructor
@Service
public class BuildTemplateFavoritesBizServiceImpl implements BuildTemplateFavoritesBizService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BuildTemplateFavoritesBizServiceImpl.class);

    private final BuildTemplateFavoritesBizAtomService buildTemplateFavoritesAtomService;
    private final BuildTemplateBizService buildTemplateBizService;
    private final BuildTemplateFavoritesMapper buildTemplateFavoritesMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean favoriteTemplate(Integer templateId, String userId, String spaceId) {
        LOGGER.info("用户 {} 收藏模板 {}", userId, templateId);
        // 首先检查该用户是否已经有过该模板的收藏记录（无论是否已删除）
        BuildTemplateFavoritesDO existingRecord = buildTemplateFavoritesMapper.findByUserIdAndTemplateIdIgnoreDeleted(userId, templateId, spaceId);
        if (existingRecord != null) {
            // 如果记录存在但已被标记为删除，则恢复该记录
            if (existingRecord.getDeleted()) {
                LOGGER.info("恢复用户 {} 之前的模板 {} 收藏记录", userId, templateId);
                existingRecord.setDeleted(false);
                existingRecord.setSpaceId(spaceId);
                return buildTemplateFavoritesMapper.updateDeletedRecord(existingRecord.getId(), spaceId);
            } else {
                // 如果记录存在且未被删除，说明已经收藏过
                LOGGER.info("用户 {} 已收藏模板 {}, 无需重复操作", userId, templateId);
                return true;
            }
        } else {
            // 如果不存在任何记录，创建新的收藏关系
            LOGGER.info("创建用户 {} 收藏模板 {} 的新记录", userId, templateId);
            BuildTemplateFavoritesDO favoritesDO = new BuildTemplateFavoritesDO();
            favoritesDO.setUserId(userId);
            favoritesDO.setTemplateId(templateId);
            favoritesDO.setSpaceId(spaceId);
            favoritesDO.setTenantId(UserUtils.getTenantId());
            return buildTemplateFavoritesAtomService.save(favoritesDO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unfavoriteTemplate(String templateId, String userId, String spaceId) {
        LOGGER.info("用户 {} 取消收藏模板 {}", userId, templateId);
        // 查找收藏关系
        QueryWrapper<BuildTemplateFavoritesDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                .eq("template_id", templateId)
                .eq("space_id", spaceId);
        // 使用MyBatis-Plus的逻辑删除方法，而不是手动设置deleted字段
        return buildTemplateFavoritesAtomService.remove(queryWrapper);
    }


    @Override
    public List<BuildTemplateBO> getFavoriteTemplates(String userId, String spaceId) {
        LOGGER.info("获取用户 {} 收藏的模板列表, spaceId: {}", userId, spaceId);
        // 查询用户收藏的模板ID列表
        QueryWrapper<BuildTemplateFavoritesDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("template_id") // 只查询模板ID列
                .eq("user_id", userId);
        // 如果提供了工作空间ID，添加筛选条件
        if (spaceId != null && !spaceId.isEmpty()) {
            queryWrapper.eq("space_id", spaceId);
        }
        List<BuildTemplateFavoritesDO> favoritesList = buildTemplateFavoritesAtomService.list(queryWrapper);
        // 如果没有收藏记录，返回空列表
        if (favoritesList == null || favoritesList.isEmpty()) {
            return new ArrayList<>();
        }
        // 提取模板ID列表
        List<Integer> templateIds = favoritesList.stream()
                .map(BuildTemplateFavoritesDO::getTemplateId)
                .collect(Collectors.toList());
        // 查询每个模板的详细信息
        List<BuildTemplateBO> templateList = new ArrayList<>();
        for (Integer templateId : templateIds) {
            BuildTemplateBO template = buildTemplateBizService.getBuildTemplateById(templateId);
            if (template != null && !template.getDeleted()) {
                templateList.add(template);
            }
        }
        return templateList;
    }
}
