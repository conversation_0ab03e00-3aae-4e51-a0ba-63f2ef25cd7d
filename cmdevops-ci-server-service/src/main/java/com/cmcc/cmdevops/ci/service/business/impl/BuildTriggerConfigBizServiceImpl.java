package com.cmcc.cmdevops.ci.service.business.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.atom.BuildTriggerConfigAtomService;
import com.cmcc.cmdevops.ci.service.bo.BuildTriggerConfigBO;
import com.cmcc.cmdevops.ci.service.business.BuildTriggerConfigBizService;
import com.cmcc.cmdevops.ci.service.business.util.EmptyValidator;
import com.cmcc.cmdevops.ci.service.dao.BuildTriggerConfigDO;
import com.cmcc.cmdevops.util.BeanCloner;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 构建触发配置业务实现类
 */
@Service
public class BuildTriggerConfigBizServiceImpl implements BuildTriggerConfigBizService {

    private final BuildTriggerConfigAtomService buildTriggerConfigAtomService;

    public BuildTriggerConfigBizServiceImpl(BuildTriggerConfigAtomService buildTriggerConfigAtomService) {
        this.buildTriggerConfigAtomService = buildTriggerConfigAtomService;
    }

    @Override
    public void createBuildTriggerConfig(BuildTriggerConfigBO buildTriggerConfigBO) {
        BuildTriggerConfigDO buildTriggerConfigDO = BeanCloner.clone(buildTriggerConfigBO, BuildTriggerConfigDO.class);
        buildTriggerConfigAtomService.save(buildTriggerConfigDO);
    }

    @Override
    public void updateBuildTriggerConfig(BuildTriggerConfigBO buildTriggerConfigBO) {
        BuildTriggerConfigDO buildTriggerConfigDO = BeanCloner.clone(buildTriggerConfigBO, BuildTriggerConfigDO.class);

        UpdateWrapper<BuildTriggerConfigDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", buildTriggerConfigBO.getId());

        buildTriggerConfigAtomService.update(buildTriggerConfigDO, updateWrapper);
    }

    @Override
    public void deleteBuildTriggerConfig(BuildTriggerConfigBO buildTriggerConfigBO) {
        buildTriggerConfigAtomService.removeById(buildTriggerConfigBO.getId());
    }

    @Override
    public BuildTriggerConfigBO getBuildTriggerConfigById(String id) {
        BuildTriggerConfigDO buildTriggerConfigDO = buildTriggerConfigAtomService.getById(id);
        return (buildTriggerConfigDO != null) ? BeanCloner.clone(buildTriggerConfigDO, BuildTriggerConfigBO.class) : null;
    }

    @Override
    public PageResponse<List<BuildTriggerConfigBO>> pageBuildTriggerConfigs(PageRequest pageRequest) {
        Page<BuildTriggerConfigDO> page = new Page<>(pageRequest.getPageNo(), pageRequest.getPageSize());
        Page<BuildTriggerConfigDO> result = buildTriggerConfigAtomService.page(page);
        List<BuildTriggerConfigBO> list = BeanCloner.clone(result.getRecords(), BuildTriggerConfigBO.class);
        return PageResponse.success(list, result.getTotal(), pageRequest.getPageNo(), pageRequest.getPageSize());
    }

    @Override
    public BuildTriggerConfigBO getBuildTriggerConfigByTaskId(String taskId) {
        BuildTriggerConfigDO buildTriggerConfigDO = buildTriggerConfigAtomService.getOne(new QueryWrapper<BuildTriggerConfigDO>().eq("task_id", taskId));
        if (EmptyValidator.isEmpty(buildTriggerConfigDO)) {
            return new BuildTriggerConfigBO();
        }
        return BeanCloner.clone(buildTriggerConfigDO, BuildTriggerConfigBO.class);
    }

    @Override
    public void deleteByTaskIds(List<String> taskIds) {
        // 先更新删除时间
        UpdateWrapper<BuildTriggerConfigDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("task_id", taskIds)
                .set("delete_time", LocalDateTime.now());

        buildTriggerConfigAtomService.update(updateWrapper);
        LambdaQueryWrapper<BuildTriggerConfigDO> wrapper = new LambdaQueryWrapper<BuildTriggerConfigDO>().in(BuildTriggerConfigDO::getTaskId, taskIds);
        buildTriggerConfigAtomService.remove(wrapper);
    }

    @Override
    public List<BuildTriggerConfigBO> getAllBuildTriggerConfigs() {
        List<BuildTriggerConfigDO> result = buildTriggerConfigAtomService.list();
        List<BuildTriggerConfigBO> list = BeanCloner.clone(result, BuildTriggerConfigBO.class);
        return list;
    }
}
