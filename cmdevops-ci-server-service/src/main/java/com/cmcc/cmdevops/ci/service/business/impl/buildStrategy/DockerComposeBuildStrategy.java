package com.cmcc.cmdevops.ci.service.business.impl.buildStrategy;

import com.cmcc.cmdevops.ci.service.bo.BuildSnapshotBO;
import com.cmcc.cmdevops.ci.service.bo.BuildStepConfigBO;
import com.cmcc.cmdevops.ci.service.bo.BuildStepsDockerComposeBO;
import com.cmcc.cmdevops.ci.service.bo.BuildToolBO;
import com.cmcc.cmdevops.ci.service.business.BuildStrategy;
import com.cmcc.cmdevops.ci.service.business.util.EmptyValidator;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>Title: DockerComposeBuildStrategy</p>
 * <p>Description: Docker Compose构建策略实现 - 支持内联配置</p>
 * @createtime 2025-07-28
 */
@Service
public class DockerComposeBuildStrategy implements BuildStrategy {

    @Override
    public String stageShell(BuildSnapshotBO buildSnapshotBO, BuildStepConfigBO buildStepsBO, List<BuildToolBO> buildToolBOList) {
        StringBuilder buildShell = new StringBuilder();
        BuildStepsDockerComposeBO dockerComposeConfig = buildStepsBO.getConfig().toJavaObject(BuildStepsDockerComposeBO.class);

        // 验证compose内容不为空
        if (EmptyValidator.isEmpty(dockerComposeConfig.getComposeContent())) {
            throw new RuntimeException("Docker Compose内容不能为空");
        }
        // 生成writeFile语句
        String writeFileStatement = generateWriteFileStatement(dockerComposeConfig.getComposeContent());

        // 构建docker-compose命令
        String dockerComposeCommand = buildDockerComposeCommand(dockerComposeConfig);

        buildShell.append("stage('").append(buildStepsBO.getName()).append("-").append(buildStepsBO.getSerial()).append("') { \n")
                .append("steps { \n")
                .append("script { \n")
                .append("echo '生成Docker Compose配置文件...'\n")
                .append(writeFileStatement)
                .append("echo '开始执行Docker Compose操作...'\n")
                .append("sh '''").append(JenkinsUtils.commandReplace(dockerComposeCommand)).append("'''\n")
                .append("echo 'Docker Compose操作完成'\n")
                .append("} \n")
                .append("} \n")
                .append("} \n");

        return buildShell.toString();
    }

    /**
     * 生成writeFile语句，将compose内容写入临时文件
     */
    private String generateWriteFileStatement(String composeContent) {
        return "writeFile encoding: 'UTF-8', file: './tmp/docker-compose.yml', text: '''" +
               JenkinsUtils.commandReplace(composeContent) + "'''\n";
    }

    /**
     * 构建docker-compose命令，使用临时文件
     */
    private String buildDockerComposeCommand(BuildStepsDockerComposeBO config) {
        StringBuilder command = new StringBuilder("docker-compose");

        // 使用临时文件路径
        command.append(" -f ./tmp/docker-compose.yml");

        // 添加项目名称
        if (EmptyValidator.isNotEmpty(config.getProjectName())) {
            command.append(" -p ").append(config.getProjectName());
        }

        // 添加操作类型
        String operation = EmptyValidator.isNotEmpty(config.getOperation()) ? config.getOperation() : "up";
        command.append(" ").append(operation);

        // 根据操作类型添加特定参数
        if ("up".equals(operation)) {
            // 后台运行
            if (config.getDetached() != null && config.getDetached()) {
                command.append(" -d");
            }

            // 添加额外构建参数
            if (EmptyValidator.isNotEmpty(config.getBuildArgs())) {
                command.append(" ").append(config.getBuildArgs());
            }
        } else if ("down".equals(operation)) {
            // 超时时间
            if (config.getTimeout() != null && config.getTimeout() > 0) {
                command.append(" -t ").append(config.getTimeout());
            }
        }

        // 移除孤立容器（适用于up和down操作）
        if (config.getRemoveOrphans() != null && config.getRemoveOrphans()) {
            command.append(" --remove-orphans");
        }

        // 添加服务名称（如果不是all的话）
        if (EmptyValidator.isNotEmpty(config.getServiceName()) && !"all".equals(config.getServiceName())) {
            command.append(" ").append(config.getServiceName());
        }

        return command.toString();
    }
}
