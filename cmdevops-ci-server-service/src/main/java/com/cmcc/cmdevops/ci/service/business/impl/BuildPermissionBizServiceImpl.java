package com.cmcc.cmdevops.ci.service.business.impl;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.atom.BuildConfigFileAtomService;
import com.cmcc.cmdevops.ci.service.atom.BuildPermissionAtomService;
import com.cmcc.cmdevops.ci.service.atom.BuildTaskAtomService;
import com.cmcc.cmdevops.ci.service.bo.BuildConfigFileBO;
import com.cmcc.cmdevops.ci.service.bo.BuildPermissionBO;
import com.cmcc.cmdevops.ci.service.bo.BuildStepConfigBO;
import com.cmcc.cmdevops.ci.service.bo.BuildTaskBO;
import com.cmcc.cmdevops.ci.service.business.BuildConfigFileBizService;
import com.cmcc.cmdevops.ci.service.business.BuildPermissionBizService;
import com.cmcc.cmdevops.ci.service.business.BuildTaskBizService;
import com.cmcc.cmdevops.ci.service.business.util.EmptyValidator;
import com.cmcc.cmdevops.ci.service.business.util.UUIDGenerator;
import com.cmcc.cmdevops.ci.service.business.util.UserUtils;
import com.cmcc.cmdevops.ci.service.dao.BuildConfigFileDO;
import com.cmcc.cmdevops.ci.service.dao.BuildPermissionDO;
import com.cmcc.cmdevops.ci.service.dao.BuildTaskDO;
import com.cmcc.cmdevops.ci.service.dao.mapper.BuildConfigFileMapper;
import com.cmcc.cmdevops.exception.BusinessException;
import com.cmcc.cmdevops.util.BeanCloner;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.yaml.snakeyaml.Yaml;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 构建环境业务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BuildPermissionBizServiceImpl implements BuildPermissionBizService {

    @Resource
    private BuildPermissionAtomService buildPermissionAtomService;
    @Resource
    private BuildTaskBizService buildTaskService;


    @Override
    public void save(List<BuildPermissionBO> bos) {
        for (BuildPermissionBO bo : bos) {
            List<String> permissions = bo.getPermissions();
            for (String permission : permissions) {
                BuildPermissionDO buildPermissionDO = new BuildPermissionDO();
                buildPermissionDO.setTaskId(bo.getTaskId());
                buildPermissionDO.setUserUid(bo.getUserUid());
                buildPermissionDO.setUserName(bo.getUserName());
                buildPermissionDO.setPermissionCode(permission);
                buildPermissionDO.setSpaceId(bo.getSpaceId());
                buildPermissionDO.setTenantId(UserUtils.getTenantId());
                buildPermissionDO.setCreateUid(UserUtils.getUserId());
                buildPermissionDO.setUpdateUid(UserUtils.getUserId());
                buildPermissionDO.setUpdateTime(LocalDateTime.now());
                buildPermissionAtomService.save(buildPermissionDO);
            }
        }
    }

    @Override
    public void update(BuildPermissionBO buildPermissionBO) {
        LambdaQueryWrapper<BuildPermissionDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BuildPermissionDO::getTaskId, buildPermissionBO.getTaskId());
        queryWrapper.eq(BuildPermissionDO::getUserUid, buildPermissionBO.getUserUid());
        buildPermissionAtomService.remove(queryWrapper);
        save(List.of(buildPermissionBO));
    }

    @Override
    public void delete(String userUid) {
        LambdaQueryWrapper<BuildPermissionDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BuildPermissionDO::getUserUid, userUid);
        buildPermissionAtomService.remove(queryWrapper);
    }

    @Override
    public PageResponse<List<BuildPermissionBO>> page(PageRequest pageRequest, BuildPermissionBO buildPermissionBO) {
        Integer pageNo = pageRequest.getPageNo();
        Integer pageSize = pageRequest.getPageSize();
        LambdaQueryWrapper<BuildPermissionDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BuildPermissionDO::getTaskId, buildPermissionBO.getTaskId());
        List<BuildPermissionDO> dos = buildPermissionAtomService.list(queryWrapper);
        List<BuildPermissionBO> list = new ArrayList<>();
        //按userUid分组
        Map<String, List<BuildPermissionDO>> groupedByUser = dos.stream()
                .collect(Collectors.groupingBy(BuildPermissionDO::getUserUid));
        groupedByUser.forEach((user, users) -> {
            List<BuildPermissionDO> buildPermissionDOS = groupedByUser.get(user);
            BuildPermissionBO bo = new BuildPermissionBO();
            bo.setTaskId(buildPermissionBO.getTaskId());
            bo.setUserUid(user);
            bo.setUserName(buildPermissionDOS.get(0).getUserName());
            bo.setUpdateTime(buildPermissionDOS.get(0).getUpdateTime());
            bo.setPermissions(buildPermissionDOS.stream().map(BuildPermissionDO::getPermissionCode).collect(Collectors.toList()));
            list.add(bo);
        });

        int total = list.size();
        int fromIndex = (pageNo - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, total);
        if (fromIndex >= total) {
            return PageResponse.success(Collections.emptyList(), total, pageNo, pageSize);
        }
        //list按修改时间降序排序
        list.sort((o1, o2) -> o2.getUpdateTime().compareTo(o1.getUpdateTime()));

        List<BuildPermissionBO> paginatedGroups = list.subList(fromIndex, toIndex);
        return PageResponse.success(paginatedGroups, total, pageRequest.getPageNo(), pageRequest.getPageSize());
    }

    @Override
    public Boolean validPermission(String taskId,String permissionCode) {
        BuildTaskBO task = buildTaskService.getTask(taskId);
        if (task.getCreateUid().equals(UserUtils.getUserId())) {
            return true;
        }
        String permissionType = task.getPermissionType();
        if (permissionType.equals("global")) {
            return true;
        } else {
            LambdaQueryWrapper<BuildPermissionDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BuildPermissionDO::getTaskId, taskId);
            queryWrapper.eq(BuildPermissionDO::getUserUid, UserUtils.getUserId());
            queryWrapper.eq(BuildPermissionDO::getPermissionCode, permissionCode);
            List<BuildPermissionDO> list = buildPermissionAtomService.list(queryWrapper);
            return EmptyValidator.isNotEmpty(list);
        }
    }

    @Override
    public String validPermissionBatch(List<BuildTaskBO> bos, String permissionCode) {
        List<String> result = new ArrayList<>();
        for (BuildTaskBO bo : bos) {
            if (!validPermission(bo.getId(), permissionCode)) {
                result.add(bo.getTaskName());
            }
        }
        return String.join(",", result);
    }
}
