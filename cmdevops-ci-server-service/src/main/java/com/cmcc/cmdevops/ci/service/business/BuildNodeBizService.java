package com.cmcc.cmdevops.ci.service.business;

import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.bo.BuildNodeBO;

import java.util.List;
import java.util.Map;

/**
 * 构建节点业务接口
 */
public interface BuildNodeBizService {
    /**
     * 创建构建节点配置
     *
     * @param buildNodeBO 构建节点数据对象
     */
    void createBuildNode(BuildNodeBO buildNodeBO);

    /**
     * 更新构建节点配置
     *
     * @param buildNodeBO 构建节点数据对象
     */
    void updateBuildNode(BuildNodeBO buildNodeBO);

    /**
     * 删除构建节点配置
     *
     * @param buildNodeBO 构建节点数据对象
     */
    void deleteBuildNode(BuildNodeBO buildNodeBO);

    /**
     * 根据ID获取构建节点配置
     *
     * @param id 构建节点ID
     * @return 构建节点数据对象
     */
    BuildNodeBO getBuildNodeById(Integer id);

    List<BuildNodeBO> list(BuildNodeBO buildNodeBO);

    Map<String, String> getCiToolEnvs(Integer id);

    /**
     * 分页查询构建节点列表
     *
     * @param pageRequest 分页请求参数
     * @return 分页响应结果
     */
    PageResponse<List<BuildNodeBO>> pageBuildNodes(PageRequest pageRequest);
}
