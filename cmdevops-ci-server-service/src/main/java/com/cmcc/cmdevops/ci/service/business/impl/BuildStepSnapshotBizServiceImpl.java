package com.cmcc.cmdevops.ci.service.business.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.atom.BuildStepSnapshotAtomService;
import com.cmcc.cmdevops.ci.service.bo.BuildStepSnapshotBO;
import com.cmcc.cmdevops.ci.service.business.BuildStepSnapshotBizService;
import com.cmcc.cmdevops.ci.service.business.util.EmptyValidator;
import com.cmcc.cmdevops.ci.service.dao.BuildStepSnapshotDO;
import com.cmcc.cmdevops.util.BeanCloner;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 构建步骤快照业务实现类
 */
@Service
public class BuildStepSnapshotBizServiceImpl implements BuildStepSnapshotBizService {

    private final BuildStepSnapshotAtomService buildStepSnapshotAtomService;

    public BuildStepSnapshotBizServiceImpl(BuildStepSnapshotAtomService buildStepSnapshotAtomService) {
        this.buildStepSnapshotAtomService = buildStepSnapshotAtomService;
    }

    @Override
    public void createBuildStepSnapshot(BuildStepSnapshotBO buildStepSnapshotBO) {
        BuildStepSnapshotDO buildStepSnapshotDO = BeanCloner.clone(buildStepSnapshotBO, BuildStepSnapshotDO.class);
        buildStepSnapshotAtomService.save(buildStepSnapshotDO);
    }

    @Override
    public void updateBuildStepSnapshot(BuildStepSnapshotBO buildStepSnapshotBO) {
        BuildStepSnapshotDO buildStepSnapshotDO = BeanCloner.clone(buildStepSnapshotBO, BuildStepSnapshotDO.class);

        UpdateWrapper<BuildStepSnapshotDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", buildStepSnapshotBO.getId());

        buildStepSnapshotAtomService.update(buildStepSnapshotDO, updateWrapper);
    }

    @Override
    public void deleteBuildStepSnapshot(BuildStepSnapshotBO buildStepSnapshotBO) {
        buildStepSnapshotAtomService.removeById(buildStepSnapshotBO.getId());
    }

    @Override
    public BuildStepSnapshotBO getBuildStepSnapshotById(String id) {
        BuildStepSnapshotDO buildStepSnapshotDO = buildStepSnapshotAtomService.getById(id);
        return (buildStepSnapshotDO != null)
                ? BeanCloner.clone(buildStepSnapshotDO, BuildStepSnapshotBO.class)
                : null;
    }

    @Override
    public PageResponse<List<BuildStepSnapshotBO>> pageBuildStepSnapshots(PageRequest pageRequest) {
        Page<BuildStepSnapshotDO> page = new Page<>(pageRequest.getPageNo(), pageRequest.getPageSize());
        Page<BuildStepSnapshotDO> result = buildStepSnapshotAtomService.page(page);
        List<BuildStepSnapshotBO> list = BeanCloner.clone(result.getRecords(), BuildStepSnapshotBO.class);
        return PageResponse.success(list, result.getTotal(), pageRequest.getPageNo(), pageRequest.getPageSize());
    }


    public List<BuildStepSnapshotBO> list(BuildStepSnapshotBO buildStepSnapshotBO) {
        QueryWrapper<BuildStepSnapshotDO> queryWrapper = new QueryWrapper<>();
        if (EmptyValidator.isNotEmpty(buildStepSnapshotBO.getTaskId())) {
            queryWrapper.eq("task_id", buildStepSnapshotBO.getTaskId());
        }
        if (EmptyValidator.isNotEmpty(buildStepSnapshotBO.getBuildSnapshotId())) {
            queryWrapper.eq("build_snapshot_id", buildStepSnapshotBO.getBuildSnapshotId());
        }
        queryWrapper.orderByAsc("serial");
        List<BuildStepSnapshotDO> result = buildStepSnapshotAtomService.list(queryWrapper);
        return BeanCloner.clone(result, BuildStepSnapshotBO.class);
    }
}
