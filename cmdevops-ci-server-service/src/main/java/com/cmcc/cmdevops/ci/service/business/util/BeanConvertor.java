package com.cmcc.cmdevops.ci.service.business.util;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Constructor;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import org.apache.commons.beanutils.PropertyUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class BeanConvertor {
    private static Logger logger = LoggerFactory.getLogger(BeanConvertor.class);

    private BeanConvertor() {
    }

    public static <T> List<T> getCopyList(Class<T> destClazz, List origList, String... ignores) {
        List<T> list = new ArrayList();
        Iterator var4 = origList.iterator();

        while(var4.hasNext()) {
            Object Obj = var4.next();
            list.add(getCopyObject(destClazz, Obj, ignores));
        }

        return list;
    }

    public static <T> T getCopyObject(Class<T> destClazz, Object orig, String... ignores) {
        if (orig == null) {
            return null;
        } else {
            T dest = null;
            PropertyDescriptor[] destDescs = PropertyUtils.getPropertyDescriptors(destClazz);

            try {
                dest = destClazz.newInstance();
                PropertyDescriptor[] var5 = destDescs;
                int var15 = destDescs.length;

                for(int var16 = 0; var16 < var15; ++var16) {
                    PropertyDescriptor propertyDescriptor = var5[var16];
                    Object origValue = propertyHandle(propertyDescriptor, orig, ignores);
                    if (origValue != null) {
                        PropertyUtils.setProperty(dest, propertyDescriptor.getName(), origValue);
                    }
                }
            } catch (Exception var14) {
                Constructor<?>[] constructors = destClazz.getDeclaredConstructors();
                constructors[0].setAccessible(true);
                Object[] objects = new Object[constructors[0].getParameterCount()];

                try {
                    dest = (T) constructors[0].newInstance(objects);
                    PropertyDescriptor[] var8 = destDescs;
                    int var9 = destDescs.length;

                    for(int var10 = 0; var10 < var9; ++var10) {
                        PropertyDescriptor propertyDescriptor = var8[var10];
                        Object origValue = propertyHandle(propertyDescriptor, orig, ignores);
                        if (origValue != null) {
                            PropertyUtils.setProperty(dest, propertyDescriptor.getName(), origValue);
                        }
                    }
                } catch (Exception var13) {
                    logger.error(orig.getClass().getName() + " copy to " + destClazz.getName() + " error", var13);
                }
            }

            return dest;
        }
    }

    private static boolean contains(String[] ignores, String name) {
        boolean ignored = false;

        for(int j = 0; ignores != null && j < ignores.length; ++j) {
            if (ignores[j].equals(name)) {
                ignored = true;
                break;
            }
        }

        return ignored;
    }

    private static <T> Object propertyHandle(PropertyDescriptor propertyDescriptor, Object orig, String... ignores) throws Exception {
        String propertyName = propertyDescriptor.getName();
        if (!contains(ignores, propertyName) && !propertyName.equals("class")) {
            Class origType = PropertyUtils.getPropertyType(orig, propertyName);
            if (origType == null) {
                return null;
            } else {
                Object origValue = PropertyUtils.getProperty(orig, propertyName);
                return origValue;
            }
        } else {
            return null;
        }
    }
}

