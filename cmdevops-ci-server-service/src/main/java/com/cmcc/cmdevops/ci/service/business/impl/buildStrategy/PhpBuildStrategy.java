package com.cmcc.cmdevops.ci.service.business.impl.buildStrategy;

import com.cmcc.cmdevops.ci.service.bo.BuildSnapshotBO;
import com.cmcc.cmdevops.ci.service.bo.BuildStepConfigBO;
import com.cmcc.cmdevops.ci.service.bo.BuildStepsPhpBO;
import com.cmcc.cmdevops.ci.service.bo.BuildToolBO;
import com.cmcc.cmdevops.ci.service.business.BuildStrategy;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class PhpBuildStrategy implements BuildStrategy {
    @Override
    public String stageShell(BuildSnapshotBO buildSnapshotBO, BuildStepConfigBO buildStepsBO, List<BuildToolBO> buildToolBOList) {
        StringBuilder buildShell = new StringBuilder();
        BuildStepsPhpBO cipBuildStepsPhpDTO = buildStepsBO.getConfig().toJavaObject(BuildStepsPhpBO.class);
        BuildToolBO buildToolBO = null;
        Optional<BuildToolBO> toolBOOptional = buildToolBOList.stream()
                .filter(buildTool -> buildTool.getToolName().equals(cipBuildStepsPhpDTO.getToolVersion()))
                .findAny();
        if (toolBOOptional.isPresent()) {
            buildToolBO = toolBOOptional.get();
        }
        String phpName = cipBuildStepsPhpDTO.getToolVersion().replace("php", "");
        String phpbrewCommand = "switch-php " + phpName;
        buildShell.append("stage('").append(buildStepsBO.getName()).append("-").append(buildStepsBO.getSerial()).append("') { \n")
                .append(JenkinsUtils.getAgent(buildToolBO))
                .append("steps { \n")
                .append("  strictShNoOut('").append(phpbrewCommand).append("')\n")
                .append("sh '''").append(JenkinsUtils.commandReplace(cipBuildStepsPhpDTO.getCommand())).append("'''\n")
                .append("} \n")
                .append("} \n");
        return buildShell.toString();
    }
}
