package com.cmcc.cmdevops.ci.service.business;

import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.bo.BuildSnapshotBO;
import com.cmcc.cmdevops.ci.service.bo.BuildStepSnapshotBO;
import com.cmcc.cmdevops.ci.service.bo.BuildTaskBO;
import com.cmcc.cmdevops.ci.service.bo.BuildVersionBO;

import java.util.List;

public interface BuildTaskBizService {

    PageResponse<List<BuildTaskBO>> pageBuildTasks(PageRequest pageRequest, BuildTaskBO buildTaskBO);

    // 创建任务
    void createTask(BuildTaskBO buildTaskBO);

    String initTask(String spaceId, Integer templateId);

    String copyTask(String taskId);

    // 删除任务
    void deleteTask(String taskId);

    // 更新任务
    void updateTask(BuildTaskBO buildTaskBO);

    BuildTaskBO getTask(String taskId);

    // 启动任务
    String startTask(String taskId,String source);

    String startTask(BuildTaskBO buildTaskBO,String source);

    // 停止任务
    void stopTask(String buildSnapshotId);

    String queryLog(String buildSnapshotId, boolean all);

    BuildSnapshotBO getBuildSnapshot(String buildSnapshotId);

    List<BuildStepSnapshotBO> getBuildStepSnapshots(String buildSnapshotId);

    void disableTask(String taskId);

    void enableTask(String taskId);

    void batchDeleteTask(List<String> taskIds);

    List<BuildTaskBO> getTasksByGroupId(String groupId);

    PageResponse<List<BuildVersionBO>> getBuildVersionList(PageRequest pageRequest, BuildVersionBO buildVersionBO);



    String createAndStart(String taskId, BuildTaskBO buildTaskBO);

}

