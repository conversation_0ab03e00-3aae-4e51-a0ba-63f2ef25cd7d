package com.cmcc.cmdevops.ci.service.business;

import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.bo.BuildTemplateBO;

import java.util.List;

public interface BuildTemplateBizService {

    void saveBuildTemplate(BuildTemplateBO buildTemplateBO);

    void updateBuildTemplate(BuildTemplateBO buildTemplateBO);

    void deleteBuildTemplate(BuildTemplateBO buildTemplateBO);

    BuildTemplateBO getBuildTemplateById(Integer id);

    PageResponse<List<BuildTemplateBO>> pageBuildTemplate(PageRequest pageRequest);

    // 在BuildTemplateBizService接口中添加以下方法
    /**
     * 按模板类型查询构建模板列表（不分页）
     *
     * @param  templateType (0-系统模板, 1-自定义模板, null-全部模板)
     * @param  spaceId (研发空间Id)
     * @return 构建模板列表
     */
    List<BuildTemplateBO> queryBuildTemplatesByType(Integer templateType,String spaceId);

    /**
     * 获取所有模板列表
     *
     * @return 构建模板列表
     */
    List<BuildTemplateBO> getAllBuildTemplates(BuildTemplateBO templateBO);

}
