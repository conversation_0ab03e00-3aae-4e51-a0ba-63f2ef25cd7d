package com.cmcc.cmdevops.ci.service.business.impl.buildStrategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cmcc.cmdevops.ci.service.atom.BuildConfigFileAtomService;
import com.cmcc.cmdevops.ci.service.bo.*;
import com.cmcc.cmdevops.ci.service.business.BuildStrategy;
import com.cmcc.cmdevops.ci.service.business.SWorkerOpenBizService;
import com.cmcc.cmdevops.ci.service.business.util.EmptyValidator;
import com.cmcc.cmdevops.ci.service.dao.BuildConfigFileDO;
import com.cmcc.cmdevops.exception.BusinessException;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by 51746 on 2021/2/25.
 */
@Service
public class UploadDockerBuildStrategy implements BuildStrategy {

    private static String WAITE_DOCKER_FILE = "1";
    @Resource
    private SWorkerOpenBizService sWorkerOpenBizService;
    @Resource
    private BuildConfigFileAtomService buildConfigFileAtomService;

    @Value("${ci-tool.binfmt-image}")
    private String binfmtImage;

    @Value("${ci-tool.buildkit-image}")
    private String buildxStable;

    @Override
    public String stageShell(BuildSnapshotBO buildSnapshotBO, BuildStepConfigBO buildStepsBO, List<BuildToolBO> buildToolBOList) {
        StringBuilder buildShell = new StringBuilder();
        String repositoryAuth = sWorkerOpenBizService.dockerRepositoryAuth(buildSnapshotBO.getCreateUid());
        String repositoryUrl = sWorkerOpenBizService.dockerRepositoryUrl(buildSnapshotBO.getTenantId(), buildSnapshotBO.getCreateUid());
        String writeFile = "";
        BuildStepsUploadDockerBO cipBuildStepsUploadDockerDTO = buildStepsBO.getConfig().toJavaObject(BuildStepsUploadDockerBO.class);
        if (WAITE_DOCKER_FILE.equals(cipBuildStepsUploadDockerDTO.getDockerfileType())) {
            writeFile = "writeFile encoding: 'UTF-8', file: './tmp/DockerFile', text: '''" + JenkinsUtils.commandReplace(cipBuildStepsUploadDockerDTO.getDockerfileText()) + "'''\n";
            cipBuildStepsUploadDockerDTO.setDockerfilePath("./tmp/DockerFile");
        } else {
            boolean isDynamicDockerfilePath = cipBuildStepsUploadDockerDTO.getDockerfilePath() != null && cipBuildStepsUploadDockerDTO.getDockerfilePath().matches("@@.+@@");
            if (isDynamicDockerfilePath) {
                LambdaQueryWrapper<BuildConfigFileDO> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(BuildConfigFileDO::getFileName, cipBuildStepsUploadDockerDTO.getDockerfilePath().replaceAll("@@", ""));
                wrapper.eq(BuildConfigFileDO::getSpaceId, buildSnapshotBO.getSpaceId());
                BuildConfigFileDO buildConfigFileDO = buildConfigFileAtomService.getOne(wrapper);
                if (EmptyValidator.isEmpty(buildConfigFileDO)) {
                    throw new BusinessException("配置文件不存在!");
                }
                writeFile = "writeFile encoding: 'UTF-8', file: './tmp/DockerFile', text: '''" + JenkinsUtils.commandReplace(buildConfigFileDO.getFileContext()) + "'''\n";
                cipBuildStepsUploadDockerDTO.setDockerfilePath("./tmp/DockerFile");
            }
        }

        boolean multiplaneImageSwitch = cipBuildStepsUploadDockerDTO.getMultiplaneImageSwitch() != null && cipBuildStepsUploadDockerDTO.getMultiplaneImageSwitch();
        String multiplaneImageType = EmptyValidator.isEmpty(cipBuildStepsUploadDockerDTO.getMultiplaneImageType()) ? "NULL" : cipBuildStepsUploadDockerDTO.getMultiplaneImageType();
        buildShell.append("stage('").append(buildStepsBO.getSerial() + 2).append("-").append(buildStepsBO.getName()).append(" ') { \n")
                .append("steps { \n")
                .append("script { \n")
                .append(writeFile)
                .append("strictSh ('").append("python3 /opt/devops-tools/upload_docker.py ")
                .append(buildSnapshotBO.getTaskId()).append(" ")
                .append(buildSnapshotBO.getId()).append(" ")
                .append("${WORKSPACE} ")
                .append(repositoryUrl).append(" ")
                .append(cipBuildStepsUploadDockerDTO.getRepositoryName()).append(" ")
                .append(repositoryAuth).append(" ")
                .append(cipBuildStepsUploadDockerDTO.getArtifactName()).append(" ")
                .append(cipBuildStepsUploadDockerDTO.getArtifactVersion()).append(" ")
                .append(cipBuildStepsUploadDockerDTO.getDockerfilePath()).append(" ")
                .append(multiplaneImageSwitch).append(" ")
                .append(multiplaneImageType).append(" ")
                .append(binfmtImage).append(" ")
                .append(buildxStable).append(" ")
                .append("')\n")
                .append("def json = readFile 'cmdevops-artifactInfo/output.json'\n")
                .append("metadata.artifacts << json\n")
                .append("} \n")
                .append("} \n")
                .append("} \n");

        return buildShell.toString();
    }
}
