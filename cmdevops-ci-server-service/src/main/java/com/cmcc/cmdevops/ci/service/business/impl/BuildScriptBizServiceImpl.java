package com.cmcc.cmdevops.ci.service.business.impl;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.atom.BuildScriptAtomService;
import com.cmcc.cmdevops.ci.service.bo.BuildCodeConfigBO;
import com.cmcc.cmdevops.ci.service.bo.BuildScriptBO;
import com.cmcc.cmdevops.ci.service.bo.BuildStepConfigBO;
import com.cmcc.cmdevops.ci.service.business.BuildScriptBizService;
import com.cmcc.cmdevops.ci.service.dao.BuildCodeConfigDO;
import com.cmcc.cmdevops.ci.service.dao.BuildScriptDO;
import com.cmcc.cmdevops.util.BeanCloner;
import org.springframework.stereotype.Service;
import org.yaml.snakeyaml.Yaml;

import java.util.ArrayList;
import java.util.List;

/**
 * 构建脚本业务实现类
 */
@Service
public class BuildScriptBizServiceImpl implements BuildScriptBizService {

    private final BuildScriptAtomService buildScriptAtomService;

    public BuildScriptBizServiceImpl(BuildScriptAtomService buildScriptAtomService) {
        this.buildScriptAtomService = buildScriptAtomService;
    }

    @Override
    public void createBuildScript(BuildScriptBO buildScriptBO) {
        BuildScriptDO buildScriptDO = BeanCloner.clone(buildScriptBO, BuildScriptDO.class);
        buildScriptAtomService.save(buildScriptDO);
    }

    @Override
    public void updateBuildScript(BuildScriptBO buildScriptBO) {
        BuildScriptDO buildScriptDO = BeanCloner.clone(buildScriptBO, BuildScriptDO.class);

        UpdateWrapper<BuildScriptDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", buildScriptBO.getId());

        buildScriptAtomService.update(buildScriptDO, updateWrapper);
    }

    @Override
    public void deleteBuildScript(BuildScriptBO buildScriptBO) {
        buildScriptAtomService.removeById(buildScriptBO.getId());
    }

    @Override
    public BuildScriptBO getBuildScriptById(String id) {
        BuildScriptDO buildScriptDO = buildScriptAtomService.getById(id);
        return (buildScriptDO != null) ? BeanCloner.clone(buildScriptDO, BuildScriptBO.class) : null;
    }

    @Override
    public PageResponse<List<BuildScriptBO>> pageBuildScripts(PageRequest pageRequest) {
        Page<BuildScriptDO> page = new Page<>(pageRequest.getPageNo(), pageRequest.getPageSize());
        Page<BuildScriptDO> result = buildScriptAtomService.page(page);
        List<BuildScriptBO> list = BeanCloner.clone(result.getRecords(), BuildScriptBO.class);
        return PageResponse.success(list, result.getTotal(), pageRequest.getPageNo(), pageRequest.getPageSize());
    }

    @Override
    public List<BuildStepConfigBO> getBuildStepsByScript(BuildScriptBO buildScript) {
        Yaml yaml = new Yaml();
        Iterable<Object> iterable = yaml.loadAll(buildScript.getBuildScriptContext());
        List<Object> rawList = (List<Object>) iterable.iterator().next();
        // Step 2: 转 JSON 字符串
        String jsonStr = JSONObject.toJSONString(rawList);
        // Step 3: Fastjson 转 Java 对象
        List<BuildStepConfigBO> result = JSONObject.parseObject(jsonStr, new TypeReference<List<BuildStepConfigBO>>() {
        });
        for (int i = 0; i < result.size(); i++) {
            result.get(i).setSerial(i);
        }
        // 校验result配置,有问题报错
        return result;
    }

    @Override
    public BuildScriptBO getBuildScriptByTaskId(String taskId) {
        BuildScriptBO buildScriptBO = new BuildScriptBO();
        buildScriptBO.setTaskId(taskId);
        QueryWrapper<BuildScriptDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("task_id", taskId);
        BuildScriptDO buildScriptDO = buildScriptAtomService.getOne(queryWrapper);
        return (buildScriptDO != null) ? BeanCloner.clone(buildScriptDO, BuildScriptBO.class) : new BuildScriptBO();
    }
}
