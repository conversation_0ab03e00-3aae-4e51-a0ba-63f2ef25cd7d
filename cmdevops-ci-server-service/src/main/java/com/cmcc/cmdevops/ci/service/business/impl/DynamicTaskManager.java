package com.cmcc.cmdevops.ci.service.business.impl;

import com.alibaba.fastjson2.JSONObject;
import com.cmcc.cmdevops.ci.service.bo.BuildSnapshotBO;
import com.cmcc.cmdevops.ci.service.bo.StatusEnum;
import com.cmcc.cmdevops.ci.service.business.BuildScheduleSnapshotBizService;
import com.cmcc.cmdevops.ci.service.business.BuildSnapshotBizService;
import com.cmcc.cmdevops.ci.service.business.CiScheduleBizService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class DynamicTaskManager implements InitializingBean, DisposableBean {

    private ScheduledExecutorService executor;
    private final Map<Integer, List<String>> taskGroups = new ConcurrentHashMap<>();

    @Autowired
    private ApplicationContext applicationContext;

    @Override
    public void afterPropertiesSet() {
        // 初始化线程池
        int threadCount = 10;

        executor = Executors.newScheduledThreadPool(threadCount);
        // 初始化线程任务组
        for (int i = 0; i < threadCount; i++) {
            List<String> group = Collections.synchronizedList(new ArrayList<>());
            taskGroups.put(i, group);
            startWorker(i, group);
        }
        log.info("[DynamicTaskManager] Started with " + threadCount + " threads.");
    }

    private void startWorker(int index, List<String> taskGroup) {
        executor.scheduleAtFixedRate(() -> {
            Iterator<String> iter = taskGroup.iterator();
            while (iter.hasNext()) {
                String jobId = iter.next();
                try {
                    BuildSnapshotBO buildSnapshot = applicationContext.getBean("buildSnapshotBizServiceImpl", BuildSnapshotBizServiceImpl.class).getBuildSnapshotById(jobId);
                    if (buildSnapshot.getBuildStatus().equals(StatusEnum.RUNNING.getCode().toString())) {
                        JSONObject taskStatus = applicationContext.getBean("ciScheduleBizServiceImpl", CiScheduleBizServiceImpl.class).getStatus(jobId);
                        String result = taskStatus.getString("result");
                        if ("SUCCESS".equals(result) || "FAILURE".equals(result) || "ABORTED".equals(result)) {
                            log.info("Job done: " + jobId + " = " + result);
                            iter.remove();
                            // 任务执行完成之后,收集数据
                            applicationContext.getBean("buildSnapshotBizServiceImpl", BuildSnapshotBizServiceImpl.class).getCollectData(jobId, taskStatus);
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    System.err.println("Polling error for " + jobId + ": " + e.getMessage());
                }
            }
        }, 0, 10, TimeUnit.SECONDS);
    }

    public void addJob(String jobId) {
        int groupIndex = findLightestGroup();
        taskGroups.get(groupIndex).add(jobId);
        log.info("Added job: " + jobId + " to group " + groupIndex);
    }

    private int findLightestGroup() {
        return taskGroups.entrySet().stream()
                .min(Comparator.comparingInt(e -> e.getValue().size()))
                .map(Map.Entry::getKey)
                .orElse(0);
    }

    @Override
    public void destroy() {
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
            log.info("[DynamicTaskManager] Shutdown.");
        }
    }
}
