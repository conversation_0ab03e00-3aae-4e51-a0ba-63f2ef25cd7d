package com.cmcc.cmdevops.ci.service.business.impl.buildStrategy;

import com.cmcc.cmdevops.ci.service.bo.*;
import com.cmcc.cmdevops.ci.service.business.BuildStrategy;
import com.cmcc.cmdevops.ci.service.business.SWorkerOpenBizService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class UploadArtifactBuildStrategy implements BuildStrategy {

    @Resource
    private SWorkerOpenBizService sWorkerOpenBizService;

    @Override
    public String stageShell(BuildSnapshotBO buildSnapshotBO, BuildStepConfigBO buildStepsBO, List<BuildToolBO> buildToolBOList) {
        StringBuilder buildShell = new StringBuilder();
        BuildStepsUploadArtifactBO dto = buildStepsBO.getConfig().toJavaObject(BuildStepsUploadArtifactBO.class);
        String repositoryAuth = sWorkerOpenBizService.normalRepositoryAuth(buildSnapshotBO.getCreateUid());
        String repositoryUrl = sWorkerOpenBizService.normalRepositoryUrl(buildSnapshotBO.getTenantId(), dto.getRepositoryName());
        buildShell.append("stage('").append(buildStepsBO.getSerial() + 2).append("-").append(buildStepsBO.getName()).append(" ') { \n")
                .append("steps { \n")
                .append("script { \n")
                .append("strictSh ('").append("python3 /opt/devops-tools/upload_artifact.py").append(" ")
                .append(buildSnapshotBO.getTaskId()).append(" ")
                .append(buildSnapshotBO.getId()).append(" ")
                .append("${WORKSPACE}").append(" ")
                .append(repositoryUrl).append(" ")
                .append(dto.getRepositoryName()).append(" ")
                .append(repositoryAuth).append(" ")
                .append(dto.getArtifactName()).append(" ")
                .append(dto.getArtifactVersion()).append(" ")
                .append("\"").append(dto.getArtifactPath()).append("\" ")
                .append("')\n")
                .append("def json = readFile 'cmdevops-artifactInfo/output.json'\n")
                .append("metadata.artifacts << json\n")
                .append("} \n")
                .append("} \n")
                .append("} \n");


        return buildShell.toString();
    }
}
