package com.cmcc.cmdevops.ci.service.bo;

import lombok.Data;

import java.io.Serializable;


/**
* <p>Title: CipBuildStepsPythonDTO</p>
* <p>Description:  </p>
* <p>Copyright: Copyright (c) 2018</p>
* <p>Company: SI-TECH </p>
* <AUTHOR>
* @version 1.0
* @createtime 2021-04-26 09:30:29
*
*/
@Data
public class BuildStepsCmakeBO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String toolVersion;
    /**
     *
     */
    private String command;

    /**
     * 是否开启分布式构建
     */
    private Boolean enableDistributedBuild;

}
