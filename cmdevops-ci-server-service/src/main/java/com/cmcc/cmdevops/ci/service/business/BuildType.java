package com.cmcc.cmdevops.ci.service.business;

/**
 * 项目构建类型.
 *
 * <AUTHOR>
 */
public class BuildType {
    public static final String MASTER = "master";
    public static final String K8S = "kubernetes";
    public static final String GIT = "gitlab";
    public static final String SVN = "svn";

    public static final String MAVEN = "maven";
    public static final String ANT = "ant";
    public static final String SHELL = "shell";
    public static final String NODE = "node";
    public static final String NPM = "npm";
    public static final String YARN = "yarn";
    public static final String GRUNT = "grunt";
    public static final String GULP = "gulp";
    public static final String VITE = "vite";

    public static final String CANGJIE = "cangjie";

    public static final String GRADLE = "gradle";
    public static final String PYTHON = "python";
    public static final String FIS = "fis";
    public static final String GO = "go";
    public static final String SONAR = "sonarqube";
    public static final String COMP_SCAN = "compScan";
    public static final String DEFAULT_COMMAND = "defaultCommand";
    public static final String UPLOAD_ARTIFACT = "uploadArtifact";
    public static final String UPLOAD_CACHE_DIR = "uploadCacheDir";
    public static final String DOWNLOAD_CACHE_DIR = "downloadCacheDir";
    public static final String UPLOAD_DOCKER = "uploadDocker";
    public static final String SQL = "sql";
    public static final String SVN_VERSION_CONTROL = "svn";
    public static final String GITLAB_VERSION_CONTROL = "gitlab";
    public static final String JUNIT = "junit";
    public static final String CMAKE = "cmake";
    public static final String PHP = "php";
    public static final String MSBUILD = "msbuild";
    public static final String SWIFT = "swift";
    public static final String PY_INSTALLER = "pyinstaller";
    public static final String SETUPTOOLS = "setuptools";
    public static final String RUBY = "ruby";
    public static final String LUA = "lua";
    public static final String RUST = "rust";
    public static final String SCALA = "scala";
    public static final String P4 = "p4";
    public static final String ANDROID = "android";

    public static final String CUSTOMIZE = "customize";



}
