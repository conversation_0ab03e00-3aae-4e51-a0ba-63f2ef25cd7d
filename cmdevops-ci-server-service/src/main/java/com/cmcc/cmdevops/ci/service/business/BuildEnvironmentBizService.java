package com.cmcc.cmdevops.ci.service.business;

import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.bo.BuildEnvironmentBO;

import java.util.List;

/**
 * 构建环境业务接口
 */
public interface BuildEnvironmentBizService {
    /**
     * 创建构建环境配置
     * @param buildEnvironmentBO 构建环境数据对象
     */
    void save(BuildEnvironmentBO buildEnvironmentBO);

    /**
     * 更新构建环境配置
     * @param buildEnvironmentBO 构建环境数据对象
     */
    void update(BuildEnvironmentBO buildEnvironmentBO);

    /**
     * 删除构建环境配置
     * @param id 构建环境数据对象Id
     */
    void delete(Integer id);

    /**
     * 根据ID获取构建环境配置
     * @param id 构建环境ID
     * @return 构建环境数据对象
     */
    BuildEnvironmentBO detail(Integer id);

    /**
     * 分页查询构建环境列表
     * @param pageRequest 分页请求参数
     * @return 分页响应结果
     */
    PageResponse<List<BuildEnvironmentBO>> page(PageRequest pageRequest, BuildEnvironmentBO buildEnvironmentBO);

    void init(Integer environmentId);
}
