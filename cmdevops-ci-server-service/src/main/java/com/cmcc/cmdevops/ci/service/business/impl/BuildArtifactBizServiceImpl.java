package com.cmcc.cmdevops.ci.service.business.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.atom.BuildArtifactAtomService;
import com.cmcc.cmdevops.ci.service.bo.BuildArtifactBO;
import com.cmcc.cmdevops.ci.service.business.BuildArtifactBizService;
import com.cmcc.cmdevops.ci.service.business.util.EmptyValidator;
import com.cmcc.cmdevops.ci.service.dao.BuildArtifactDO;
import com.cmcc.cmdevops.util.BeanCloner;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 构建产物业务实现类
 */
@Service
public class BuildArtifactBizServiceImpl implements BuildArtifactBizService {

    private final BuildArtifactAtomService buildArtifactAtomService;

    public BuildArtifactBizServiceImpl(BuildArtifactAtomService buildArtifactAtomService) {
        this.buildArtifactAtomService = buildArtifactAtomService;
    }

    @Override
    public void createBuildArtifact(BuildArtifactBO buildArtifactBO) {
        BuildArtifactDO buildArtifactDO = BeanCloner.clone(buildArtifactBO, BuildArtifactDO.class);
        buildArtifactAtomService.save(buildArtifactDO);
    }

    @Override
    public void updateBuildArtifact(BuildArtifactBO buildArtifactBO) {
        BuildArtifactDO buildArtifactDO = BeanCloner.clone(buildArtifactBO, BuildArtifactDO.class);

        UpdateWrapper<BuildArtifactDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", buildArtifactBO.getId());

        buildArtifactAtomService.update(buildArtifactDO, updateWrapper);
    }

    @Override
    public void deleteBuildArtifact(BuildArtifactBO buildArtifactBO) {
        buildArtifactAtomService.removeById(buildArtifactBO.getId());
    }

    @Override
    public BuildArtifactBO getBuildArtifactById(String id) {
        BuildArtifactDO buildArtifactDO = buildArtifactAtomService.getById(id);
        return (buildArtifactDO != null) ? BeanCloner.clone(buildArtifactDO, BuildArtifactBO.class) : null;
    }

    @Override
    public PageResponse<List<BuildArtifactBO>> pageBuildArtifacts(PageRequest pageRequest) {
        Page<BuildArtifactDO> page = new Page<>(pageRequest.getPageNo(), pageRequest.getPageSize());
        Page<BuildArtifactDO> result = buildArtifactAtomService.page(page);
        List<BuildArtifactBO> list = BeanCloner.clone(result.getRecords(), BuildArtifactBO.class);
        return PageResponse.success(list, result.getTotal(), pageRequest.getPageNo(), pageRequest.getPageSize());
    }

    @Override
    public List<BuildArtifactBO> list(BuildArtifactBO buildArtifactBO) {
        QueryWrapper<BuildArtifactDO> queryWrapper = new QueryWrapper<>();
        if (EmptyValidator.isNotEmpty(buildArtifactBO.getTaskId())) {
            queryWrapper.eq("task_id", buildArtifactBO.getTaskId());
        }
        if (EmptyValidator.isNotEmpty(buildArtifactBO.getBuildArtifactType())) {
            queryWrapper.eq("build_artifact_type", buildArtifactBO.getBuildArtifactType());
        }
        if (EmptyValidator.isNotEmpty(buildArtifactBO.getBuildSnapshotId())) {
            queryWrapper.eq("build_snapshot_id", buildArtifactBO.getBuildSnapshotId());
        }
        if (EmptyValidator.isNotEmpty(buildArtifactBO.getBuildArtifactName())) {
            queryWrapper.eq("build_artifact_name", buildArtifactBO.getBuildArtifactName());
        }
        if (EmptyValidator.isNotEmpty(buildArtifactBO.getBuildArtifactRealName())) {
            queryWrapper.eq("build_artifact_real_name", buildArtifactBO.getBuildArtifactRealName());
        }
        if (EmptyValidator.isNotEmpty(buildArtifactBO.getBuildVersion())) {
            queryWrapper.eq("build_version", buildArtifactBO.getBuildVersion());
        }
        List<BuildArtifactDO> result = buildArtifactAtomService.list(queryWrapper);
        return BeanCloner.clone(result, BuildArtifactBO.class);
    }
}
