package com.cmcc.cmdevops.ci.service.business.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.atom.BuildStepConfigAtomService;
import com.cmcc.cmdevops.ci.service.bo.BuildStepConfigBO;
import com.cmcc.cmdevops.ci.service.business.BuildStepConfigBizService;
import com.cmcc.cmdevops.ci.service.business.util.UUIDGenerator;
import com.cmcc.cmdevops.ci.service.dao.BuildStepConfigDO;
import com.cmcc.cmdevops.util.BeanCloner;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 构建步骤配置业务实现类
 */
@Service
public class BuildStepConfigBizServiceImpl implements BuildStepConfigBizService {

    private final BuildStepConfigAtomService buildStepConfigAtomService;

    public BuildStepConfigBizServiceImpl(BuildStepConfigAtomService buildStepConfigAtomService) {
        this.buildStepConfigAtomService = buildStepConfigAtomService;
    }

    @Override
    public void createBuildStepConfig(BuildStepConfigBO buildStepConfigBO) {
        BuildStepConfigDO buildStepConfigDO = new BuildStepConfigDO();
        buildStepConfigDO.setTaskId(buildStepConfigBO.getTaskId());
        buildStepConfigDO.setType(buildStepConfigBO.getType());
        buildStepConfigDO.setSerial(buildStepConfigBO.getSerial());
        buildStepConfigDO.setHasOpen(buildStepConfigBO.getHasOpen());
        buildStepConfigDO.setName(buildStepConfigBO.getName());
        JSONObject config = buildStepConfigBO.getConfig();
        config.put("id", UUIDGenerator.create());
        buildStepConfigDO.setConfig(config.toString());
        buildStepConfigAtomService.save(buildStepConfigDO);
    }

    @Override
    public void updateBuildStepConfig(BuildStepConfigBO buildStepConfigBO) {
        BuildStepConfigDO buildStepConfigDO = BeanCloner.clone(buildStepConfigBO, BuildStepConfigDO.class);

        UpdateWrapper<BuildStepConfigDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", buildStepConfigBO.getId());

        buildStepConfigAtomService.update(buildStepConfigDO, updateWrapper);
    }

    @Override
    public void deleteBuildStepConfig(BuildStepConfigBO buildStepConfigBO) {
        buildStepConfigAtomService.removeById(buildStepConfigBO.getId());
    }

    @Override
    public BuildStepConfigBO getBuildStepConfigById(String id) {
        BuildStepConfigDO buildStepConfigDO = buildStepConfigAtomService.getById(id);
        BuildStepConfigBO bo = (buildStepConfigDO != null) ? BeanCloner.clone(buildStepConfigDO, BuildStepConfigBO.class) : null;
        bo.setConfig(JSONObject.parseObject(buildStepConfigDO.getConfig()));
        return bo;
    }

    @Override
    public List<BuildStepConfigBO> getBuildStepConfigListByTaskId(String taskId) {
        QueryWrapper<BuildStepConfigDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("task_id", taskId);
        queryWrapper.orderByAsc("serial");
        List<BuildStepConfigDO> dos = buildStepConfigAtomService.list(queryWrapper);
        List<BuildStepConfigBO> list = BeanCloner.clone(buildStepConfigAtomService.list(queryWrapper), BuildStepConfigBO.class);
        list.forEach(bo -> {
            dos.forEach(dox -> {
                if (bo.getId().equals(dox.getId())){
                    bo.setConfig(JSONObject.parseObject(dox.getConfig()));
                }
            });
        });
        return list;
    }

    @Override
    public PageResponse<List<BuildStepConfigBO>> pageBuildStepConfigs(PageRequest pageRequest) {
        Page<BuildStepConfigDO> page = new Page<>(pageRequest.getPageNo(), pageRequest.getPageSize());
        Page<BuildStepConfigDO> result = buildStepConfigAtomService.page(page);
        List<BuildStepConfigBO> list = BeanCloner.clone(result.getRecords(), BuildStepConfigBO.class);
        return PageResponse.success(list, result.getTotal(), pageRequest.getPageNo(), pageRequest.getPageSize());
    }

    @Override
    public void deleteByTaskIds(List<String> taskIds) {
        LambdaQueryWrapper<BuildStepConfigDO> wrapper = new LambdaQueryWrapper<BuildStepConfigDO>().in(BuildStepConfigDO::getTaskId, taskIds);
        buildStepConfigAtomService.remove(wrapper);
    }
}
