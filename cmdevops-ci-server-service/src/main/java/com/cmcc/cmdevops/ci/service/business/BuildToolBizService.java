package com.cmcc.cmdevops.ci.service.business;

import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.bo.BuildToolBO;

import java.util.List;

/**
 * 构建工具业务接口
 */
public interface BuildToolBizService {
    /**
     * 创建构建工具配置
     *
     * @param buildToolBO 构建工具数据对象
     */
    void createBuildTool(BuildToolBO buildToolBO);

    /**
     * 更新构建工具配置
     *
     * @param buildToolBO 构建工具数据对象
     */
    void updateBuildTool(BuildToolBO buildToolBO);

    /**
     * 删除构建工具配置
     *
     * @param buildToolBO 构建工具数据对象
     */
    void deleteBuildTool(BuildToolBO buildToolBO);

    /**
     * 根据ID获取构建工具配置
     *
     * @param id 构建工具ID
     * @return 构建工具数据对象
     */
    BuildToolBO getBuildToolById(String id);

    /**
     * 分页查询构建工具列表
     *
     * @param pageRequest 分页请求参数
     * @return 分页响应结果
     */
    PageResponse<List<BuildToolBO>> pageBuildTools(PageRequest pageRequest);


    List<BuildToolBO> list(BuildToolBO buildToolBO);
}
