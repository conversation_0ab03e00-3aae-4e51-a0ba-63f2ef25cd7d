package com.cmcc.cmdevops.ci.service.bo;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;

import java.io.Serializable;


/**
* <p>Title: CipBuildStepsUploadDockerDTO</p>
* <p>Description:  </p>
* <p>Copyright: Copyright (c) 2018</p>
* <p>Company: SI-TECH </p>
* <AUTHOR>
* @version 1.0
* @createtime 2021-03-05 11:35:36
*
*/
@Data
public class BuildStepsUploadDockerBO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
    *
    */
    private String repositoryUrl;
    /**
    * 仓库名
    */
    private String repositoryName;

    /**
     *
     */
    private String repositoryAuth;



    private String artifactName;
    /**
     *
     */
    private String artifactVersion;


    private String dockerfileType;
    /**
     *
     */
    private String dockerfilePath;
    /**
     *
     */
    private String dockerfileText;

    /**
     * 多平面构建开关
     */
    private Boolean multiplaneImageSwitch;

    /**
     * 多平面构建类型/架构类型
     */
    private String multiplaneImageType;

    private String binfmtImage;

    private String buildxStable;

}
