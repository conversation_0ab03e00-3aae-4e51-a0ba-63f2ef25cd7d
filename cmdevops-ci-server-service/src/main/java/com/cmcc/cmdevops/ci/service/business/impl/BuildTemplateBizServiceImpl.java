package com.cmcc.cmdevops.ci.service.business.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.atom.BuildTemplateBizAtomService;
import com.cmcc.cmdevops.ci.service.bo.BuildTemplateBO;
import com.cmcc.cmdevops.ci.service.business.BuildTemplateBizService;
import com.cmcc.cmdevops.ci.service.business.util.EmptyValidator;
import com.cmcc.cmdevops.ci.service.business.util.UserUtils;
import com.cmcc.cmdevops.ci.service.dao.BuildTemplateDO;
import com.cmcc.cmdevops.exception.BusinessException;
import com.cmcc.cmdevops.util.BeanCloner;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class BuildTemplateBizServiceImpl implements BuildTemplateBizService {

    private final BuildTemplateBizAtomService buildTemplateAtomService;

    public BuildTemplateBizServiceImpl(BuildTemplateBizAtomService buildTemplateAtomService) {
        this.buildTemplateAtomService = buildTemplateAtomService;
    }

    @Override
    public void saveBuildTemplate(BuildTemplateBO buildTemplateBO) {
        BuildTemplateDO buildTemplateDO = BeanCloner.clone(buildTemplateBO, BuildTemplateDO.class);
        checkName(buildTemplateDO.getId(), buildTemplateDO.getTitle(), buildTemplateDO.getSpaceId());
        buildTemplateDO.setTenantId(UserUtils.getTenantId());
        buildTemplateDO.setCreateUid(UserUtils.getUserId());
        buildTemplateAtomService.save(buildTemplateDO);
    }

    @Override
    public void updateBuildTemplate(BuildTemplateBO buildTemplateBO) {
        BuildTemplateDO buildTemplateDO = BeanCloner.clone(buildTemplateBO, BuildTemplateDO.class);
        checkName(buildTemplateDO.getId(), buildTemplateDO.getTitle(), buildTemplateDO.getSpaceId());
        buildTemplateDO.setUpdateUid(UserUtils.getUserId());
        // 更新操作
        buildTemplateAtomService.updateById(buildTemplateDO);
    }

    @Override
    public void deleteBuildTemplate(BuildTemplateBO buildTemplateBO) {
        buildTemplateAtomService.removeById(buildTemplateBO.getId());
    }

    @Override
    public BuildTemplateBO getBuildTemplateById(Integer id) {
        BuildTemplateDO buildTemplateDO = buildTemplateAtomService.getById(id);
        return buildTemplateDO != null ? BeanCloner.clone(buildTemplateDO, BuildTemplateBO.class) : null;
    }

    @Override
    public PageResponse<List<BuildTemplateBO>> pageBuildTemplate(PageRequest pageRequest) {
        Page<BuildTemplateDO> page = new Page<>(pageRequest.getPageNo(), pageRequest.getPageSize());
        Page<BuildTemplateDO> result = buildTemplateAtomService.page(page);
        List<BuildTemplateBO> list = BeanCloner.clone(result.getRecords(), BuildTemplateBO.class);
        return PageResponse.success(list, result.getTotal(), pageRequest.getPageNo(), pageRequest.getPageSize());
    }

    @Override
    public List<BuildTemplateBO> queryBuildTemplatesByType(Integer templateType,String spaceId) {
        LambdaQueryWrapper<BuildTemplateDO> queryWrapper = new LambdaQueryWrapper<>();
        // 按模板类型筛选
        if (templateType!= null) {
            queryWrapper.eq(BuildTemplateDO::getTemplateType,templateType);
        }
        // 自定义模板按spaceId筛选
        if (spaceId != null && templateType != null && templateType== 1) {
            queryWrapper.eq(BuildTemplateDO::getSpaceId, spaceId);
        }
        // 按创建时间降序排序，让最新的模板排在前面
        queryWrapper.orderByDesc(BuildTemplateDO::getCreateTime);
        List<BuildTemplateDO> templateList = buildTemplateAtomService.list(queryWrapper);
        return BeanCloner.clone(templateList, BuildTemplateBO.class);
    }

    @Override
    public List<BuildTemplateBO> getAllBuildTemplates(BuildTemplateBO templateBO) {
        // 查询所有空白模板
        LambdaQueryWrapper<BuildTemplateDO> blankTemplateQueryWrapper = new LambdaQueryWrapper<>();
        blankTemplateQueryWrapper.eq(BuildTemplateDO::getTemplateType, 4);
        List<BuildTemplateDO> blankTemplateList = buildTemplateAtomService.list(blankTemplateQueryWrapper);
        // 查询所有系统模板
        LambdaQueryWrapper<BuildTemplateDO> systemTemplateQueryWrapper = new LambdaQueryWrapper<>();
        systemTemplateQueryWrapper.eq(BuildTemplateDO::getTemplateType, 0);
        List<BuildTemplateDO> systemTemplateList = buildTemplateAtomService.list(systemTemplateQueryWrapper);
        // 查询所有自定义模板
        LambdaQueryWrapper<BuildTemplateDO> customQueryWrapper = new LambdaQueryWrapper<>();
        customQueryWrapper.eq(BuildTemplateDO::getTemplateType, 1);
        customQueryWrapper.orderByDesc(BuildTemplateDO::getCreateTime);
        if (EmptyValidator.isNotEmpty(templateBO.getSpaceId())) {
            customQueryWrapper.eq(BuildTemplateDO::getSpaceId, templateBO.getSpaceId());
        }
        List<BuildTemplateDO> customTemplateList = buildTemplateAtomService.list(customQueryWrapper);
        // 返回默认模板和自定义模板
        systemTemplateList.addAll(customTemplateList);
        systemTemplateList.addAll(blankTemplateList);
        return BeanCloner.clone(systemTemplateList, BuildTemplateBO.class);
    }

    private void checkName(Integer id, String name, String spaceId) {
        LambdaQueryWrapper<BuildTemplateDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BuildTemplateDO::getTitle, name);
        queryWrapper.eq(BuildTemplateDO::getSpaceId, spaceId);
        if (EmptyValidator.isNotEmpty(id)) {
            queryWrapper.ne(BuildTemplateDO::getId, id);
        }
        long count = buildTemplateAtomService.count(queryWrapper);
        if (count > 0) {
            throw new BusinessException("当前模板名称已存在");
        }
    }
}
