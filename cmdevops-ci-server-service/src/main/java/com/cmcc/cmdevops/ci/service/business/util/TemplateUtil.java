package com.cmcc.cmdevops.ci.service.business.util;

import org.apache.velocity.VelocityContext;

import java.io.StringWriter;
import java.util.Map;

/**
 * <AUTHOR>
 *
 */
public class TemplateUtil {

	public static String getRequestXml(Map<String, Object> params, String templateName) {
		VelocityContext vc = new VelocityContext();
		for(Map.Entry<String, Object> entry : params.entrySet()) {
			vc.put(entry.getKey(), entry.getValue());
		}
		StringWriter writer = new StringWriter();
		FileTemplate.getRuleTemplate(templateName).merge(vc, writer);
		String requestXml = writer.toString();
		return requestXml;
	}

}
