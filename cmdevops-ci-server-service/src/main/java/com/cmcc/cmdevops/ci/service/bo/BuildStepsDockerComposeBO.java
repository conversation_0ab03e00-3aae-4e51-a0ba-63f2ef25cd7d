package com.cmcc.cmdevops.ci.service.bo;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>Title: BuildStepsDockerComposeBO</p>
 * <p>Description: Docker Compose构建步骤配置对象 - 支持内联配置</p>
 * <p>Copyright: Copyright (c) 2018</p>
 * <p>Company: SI-TECH </p>
 * <AUTHOR> Assistant
 * @version 1.0
 * @createtime 2025-07-28
 */
@Data
public class BuildStepsDockerComposeBO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * docker-compose.yml文件内容（内联配置）
     */
    private String composeContent;

    /**
     * 要操作的服务名称，默认为"all"表示所有服务
     */
    private String serviceName;

    /**
     * Docker Compose操作类型：up/down/restart
     */
    private String operation;

    /**
     * 是否后台运行（-d参数）
     */
    private Boolean detached;

    /**
     * 额外的构建参数，如：--build --force-recreate
     */
    private String buildArgs;

    /**
     * 项目名称（-p参数）
     */
    private String projectName;

    /**
     * 是否移除孤立容器（--remove-orphans参数）
     */
    private Boolean removeOrphans;

    /**
     * 超时时间（秒）
     */
    private Integer timeout;
}
