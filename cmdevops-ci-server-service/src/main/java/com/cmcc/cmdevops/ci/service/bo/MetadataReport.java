package com.cmcc.cmdevops.ci.service.bo;

import java.util.List;

public class MetadataReport {
    private List<CodeSafetyReport> codeSafetyReport;
    private List<CodeQualityReport> codeQualityReport;
    private List<SoftReport> softReport;
    private List<ImageReport> imageReport;
    private List<UnitTestingReport> unitTestingReport;

    // Getters and setters
    public List<CodeSafetyReport> getCodeSafetyReport() {
        return codeSafetyReport;
    }

    public void setCodeSafetyReport(List<CodeSafetyReport> codeSafetyReport) {
        this.codeSafetyReport = codeSafetyReport;
    }

    public List<CodeQualityReport> getCodeQualityReport() {
        return codeQualityReport;
    }

    public void setCodeQualityReport(List<CodeQualityReport> codeQualityReport) {
        this.codeQualityReport = codeQualityReport;
    }

    public List<SoftReport> getSoftReport() {
        return softReport;
    }

    public void setSoftReport(List<SoftReport> softReport) {
        this.softReport = softReport;
    }

    public List<ImageReport> getImageReport() {
        return imageReport;
    }

    public void setImageReport(List<ImageReport> imageReport) {
        this.imageReport = imageReport;
    }

    public List<UnitTestingReport> getUnitTestingReport() {
        return unitTestingReport;
    }

    public void setUnitTestingReport(List<UnitTestingReport> unitTestingReport) {
        this.unitTestingReport = unitTestingReport;
    }
}
