package com.cmcc.cmdevops.ci.service.business;

import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.bo.BuildStepSnapshotBO;

import java.util.List;

/**
 * 构建步骤快照业务接口
 */
public interface BuildStepSnapshotBizService {
    /**
     * 创建构建步骤快照
     *
     * @param buildStepSnapshotBO 构建步骤快照数据对象
     */
    void createBuildStepSnapshot(BuildStepSnapshotBO buildStepSnapshotBO);

    /**
     * 更新构建步骤快照
     *
     * @param buildStepSnapshotBO 构建步骤快照数据对象
     */
    void updateBuildStepSnapshot(BuildStepSnapshotBO buildStepSnapshotBO);

    /**
     * 删除构建步骤快照
     *
     * @param buildStepSnapshotBO 构建步骤快照数据对象
     */
    void deleteBuildStepSnapshot(BuildStepSnapshotBO buildStepSnapshotBO);

    /**
     * 根据ID获取构建步骤快照
     *
     * @param id 构建步骤快照ID
     * @return 构建步骤快照数据对象
     */
    BuildStepSnapshotBO getBuildStepSnapshotById(String id);

    /**
     * 分页查询构建步骤快照列表
     *
     * @param pageRequest 分页请求参数
     * @return 分页响应结果
     */
    PageResponse<List<BuildStepSnapshotBO>> pageBuildStepSnapshots(PageRequest pageRequest);


    List<BuildStepSnapshotBO> list(BuildStepSnapshotBO buildStepSnapshotBO);
}
