package com.cmcc.cmdevops.ci.service.business.util;

import java.util.Collection;
import java.util.Map;

public class EmptyValidator {public EmptyValidator() {
}

    public static boolean isEmpty(Object obj) {
        if (obj == null) {
            return true;
        } else if (obj instanceof CharSequence) {
            return ((CharSequence)obj).length() == 0;
        } else if (obj instanceof Collection) {
            return ((Collection)obj).isEmpty();
        } else if (obj instanceof Map) {
            return ((Map)obj).isEmpty();
        } else if (!(obj instanceof Object[])) {
            return false;
        } else {
            Object[] object = (Object[])((Object[])obj);
            if (object.length == 0) {
                return true;
            } else {
                boolean empty = true;
                Object[] var3 = object;
                int var4 = object.length;

                for(int var5 = 0; var5 < var4; ++var5) {
                    Object objTemp = var3[var5];
                    if (!isEmpty(objTemp)) {
                        empty = false;
                        break;
                    }
                }

                return empty;
            }
        }
    }

    public static boolean isNotEmpty(Object obj) {
        return !isEmpty(obj);
    }
}
