package com.cmcc.cmdevops.ci.service.bo;

import lombok.Data;

import java.io.Serializable;


/**
* <p>Title: CipBuildStepsNodeDTO</p>
* <p>Description:  </p>
* <p>Copyright: Copyright (c) 2018</p>
* <p>Company: SI-TECH </p>
* <AUTHOR>
* @version 1.0
* @createtime 2021-03-23 20:41:18
*
*/
@Data
public class BuildStepsYarnBO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String toolVersion;
    /**
     *
     */
    private String command;

}
