package com.cmcc.cmdevops.ci.service.bo;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;


/**
 * <p>Title: CipBuildStepsFisDTO</p>
 * <p>Description:  </p>
 * <p>Copyright: Copyright (c) 2018</p>
 * <p>Company: SI-TECH </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @createtime 2021-04-26 09:30:29
 */
@Data
@Builder
public class BuildStepsUploadCacheDirBO implements Serializable {
    private static final long serialVersionUID = 1L;

    public BuildStepsUploadCacheDirBO() {

    }

    public BuildStepsUploadCacheDirBO(String cacheDir, String s3Endpoint, String accessKey, String secretKey) {
        this.cacheDir = cacheDir;
        this.s3Endpoint = s3Endpoint;
        this.accessKey = accessKey;
        this.secretKey = secretKey;
    }

    private String cacheDir;

    private String s3Endpoint;

    private String accessKey;

    private String secretKey;

}
