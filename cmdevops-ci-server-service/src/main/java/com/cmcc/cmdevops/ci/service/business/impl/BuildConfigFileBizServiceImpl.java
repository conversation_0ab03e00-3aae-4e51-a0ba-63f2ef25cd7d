package com.cmcc.cmdevops.ci.service.business.impl;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.atom.BuildConfigFileAtomService;
import com.cmcc.cmdevops.ci.service.atom.BuildEnvironmentAtomService;
import com.cmcc.cmdevops.ci.service.atom.BuildTaskAtomService;
import com.cmcc.cmdevops.ci.service.bo.*;
import com.cmcc.cmdevops.ci.service.business.BuildConfigFileBizService;
import com.cmcc.cmdevops.ci.service.business.BuildEnvironmentBizService;
import com.cmcc.cmdevops.ci.service.business.BuildJenkinsNodeBizService;
import com.cmcc.cmdevops.ci.service.business.BuildNodeBizService;
import com.cmcc.cmdevops.ci.service.business.util.EmptyValidator;
import com.cmcc.cmdevops.ci.service.business.util.UUIDGenerator;
import com.cmcc.cmdevops.ci.service.business.util.UserUtils;
import com.cmcc.cmdevops.ci.service.dao.BuildConfigFileDO;
import com.cmcc.cmdevops.ci.service.dao.BuildEnvironmentDO;
import com.cmcc.cmdevops.ci.service.dao.BuildTaskDO;
import com.cmcc.cmdevops.ci.service.dao.mapper.BuildConfigFileMapper;
import com.cmcc.cmdevops.exception.BusinessException;
import com.cmcc.cmdevops.util.BeanCloner;
import io.kubernetes.client.custom.IntOrString;
import io.kubernetes.client.custom.Quantity;
import io.kubernetes.client.openapi.ApiClient;
import io.kubernetes.client.openapi.ApiException;
import io.kubernetes.client.openapi.Configuration;
import io.kubernetes.client.openapi.apis.AppsV1Api;
import io.kubernetes.client.openapi.apis.CoreV1Api;
import io.kubernetes.client.openapi.apis.RbacAuthorizationV1Api;
import io.kubernetes.client.openapi.models.*;
import io.kubernetes.client.util.Config;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.yaml.snakeyaml.Yaml;

import java.io.StringReader;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 构建环境业务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BuildConfigFileBizServiceImpl implements BuildConfigFileBizService {

    @Resource
    private BuildConfigFileAtomService buildConfigFileAtomService;
    @Resource
    private BuildConfigFileMapper buildConfigFileMapper;
    @Resource
    private BuildTaskAtomService buildTaskAtomService;


    @Override
    public void save(BuildConfigFileBO buildConfigFileBO) {
        checkEnvironmentNameUnique(null, buildConfigFileBO.getFileName(), buildConfigFileBO.getSpaceId(),buildConfigFileBO.getFileType());
        if (EmptyValidator.isEmpty(buildConfigFileBO.getId())) {
            buildConfigFileBO.setId(UUIDGenerator.create());
        }
        BuildConfigFileDO buildConfigFileDO = BeanCloner.clone(buildConfigFileBO, BuildConfigFileDO.class);
        buildConfigFileDO.setTenantId(UserUtils.getTenantId());
        buildConfigFileDO.setCreateUid(UserUtils.getUserId());
        buildConfigFileDO.setUpdateUid(UserUtils.getUserId());
        buildConfigFileDO.setUpdateTime(LocalDateTime.now());
        buildConfigFileAtomService.save(buildConfigFileDO);
    }

    @Override
    public void update(BuildConfigFileBO buildConfigFileBO) {
        checkEnvironmentNameUnique(buildConfigFileBO.getId(), buildConfigFileBO.getFileName(), buildConfigFileBO.getSpaceId(),buildConfigFileBO.getFileType());
        BuildConfigFileDO buildConfigFileDO = BeanCloner.clone(buildConfigFileBO, BuildConfigFileDO.class);
        buildConfigFileDO.setUpdateUid(UserUtils.getUserId());
        buildConfigFileDO.setUpdateTime(LocalDateTime.now());
        buildConfigFileAtomService.updateById(buildConfigFileDO);
    }

    private void checkEnvironmentNameUnique(String id, String name, String spaceId, String fileType) {
        LambdaQueryWrapper<BuildConfigFileDO> queryWrapper = new LambdaQueryWrapper<>();
        if (EmptyValidator.isNotEmpty(id)) {
            queryWrapper.ne(BuildConfigFileDO::getId, id);
        }
        queryWrapper.eq(BuildConfigFileDO::getFileName, name);
        queryWrapper.eq(BuildConfigFileDO::getFileType, fileType);
        queryWrapper.eq(BuildConfigFileDO::getSpaceId, spaceId);
        long count = buildConfigFileAtomService.count(queryWrapper);
        if (count > 0) {
            throw new BusinessException("已存在该名称的文件");
        }
    }

    @Override
    public void delete(String id) {
        LambdaQueryWrapper<BuildTaskDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BuildTaskDO::getFileId, id);
        List<BuildTaskDO> list = buildTaskAtomService.list(queryWrapper);
        if (EmptyValidator.isNotEmpty(list)) {
            throw new BusinessException("文件存在构建任务引用,无法删除!");
        }
        buildConfigFileAtomService.removeById(id);
    }

    @Override
    public BuildConfigFileBO detail(String id) {
        BuildConfigFileDO buildConfigFileDO = buildConfigFileAtomService.getById(id);
        return (buildConfigFileDO != null) ? BeanCloner.clone(buildConfigFileDO, BuildConfigFileBO.class) : null;
    }

    @Override
    public PageResponse<List<BuildConfigFileBO>> page(PageRequest pageRequest, BuildConfigFileBO buildConfigFileBO) {
        Page<BuildConfigFileDO> page = new Page<>(pageRequest.getPageNo(), pageRequest.getPageSize());
        LambdaQueryWrapper<BuildConfigFileDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(BuildConfigFileDO::getUpdateTime);
        queryWrapper.eq(BuildConfigFileDO::getSpaceId, buildConfigFileBO.getSpaceId());
        queryWrapper.eq(BuildConfigFileDO::getFileType, buildConfigFileBO.getFileType());
        if (EmptyValidator.isNotEmpty(buildConfigFileBO.getFileName())) {
            queryWrapper.like(BuildConfigFileDO::getFileName, buildConfigFileBO.getFileName());
        }
        Page<BuildConfigFileDO> result = buildConfigFileAtomService.page(page, queryWrapper);
        List<BuildConfigFileBO> list = BeanCloner.clone(result.getRecords(), BuildConfigFileBO.class);
        return PageResponse.success(list, result.getTotal(), pageRequest.getPageNo(), pageRequest.getPageSize());
    }

    @Override
    public List<BuildStepConfigBO> getBuildStepsByFileId(String id) {
        String fileContext = buildConfigFileAtomService.getById(id).getFileContext();
        Yaml yaml = new Yaml();
        Iterable<Object> iterable = yaml.loadAll(fileContext);
        List<Object> rawList = (List<Object>) iterable.iterator().next();
        // Step 2: 转 JSON 字符串
        String jsonStr = JSONObject.toJSONString(rawList);
        // Step 3: Fastjson 转 Java 对象
        List<BuildStepConfigBO> result = JSONObject.parseObject(jsonStr, new TypeReference<List<BuildStepConfigBO>>() {
        });
        for (int i = 0; i < result.size(); i++) {
            result.get(i).setSerial(i);
        }
        // 校验result配置,有问题报错
        return result;
    }
}
