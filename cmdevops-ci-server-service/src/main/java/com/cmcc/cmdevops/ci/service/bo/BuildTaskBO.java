package com.cmcc.cmdevops.ci.service.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> @since 2025-05-20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class BuildTaskBO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    private String taskName;

    private String buildStatus;

    private String buildType;

    private String buildCodeType;

    private String fileId;

    private String buildNumber;

    private String lastBuildId;

    private LocalDateTime lastBuildTime;

    private Boolean hasDisable;

    private String spaceId;

    private String tenantId;

    private Boolean deleted;

    private LocalDateTime createTime;

    private String createUid;

    private LocalDateTime updateTime;

    private String updateUid;

    private LocalDateTime deleteTime;

    private String deleteUid;

    private BuildScriptBO buildScript;

    private BuildCodeConfigBO codeConfig;

    private List<BuildStepConfigBO> stepConfigs;

    private List<BuildParameterBO> parameters;

    private List<BuildCacheBO> caches;

    private BuildAdvancedConfigBO advancedConfig;

    private String buildGroup;

    private String permissionType;

    private String systemCode;

    private Integer assignedNode;

    private Short overtime;

    // 用于展示
    private BuildCodeSnapshotBO buildCodeSnapshot;

    private BuildTriggerConfigBO buildTriggerConfig;

    private String buildSource;

    private Integer concurrentStrategy;

    private Integer maxConcurrentCount;

    private String projects;

}
