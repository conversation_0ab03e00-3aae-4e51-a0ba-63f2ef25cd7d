package com.cmcc.cmdevops.ci.service.bo;

import lombok.Data;

import java.io.Serializable;


/**
 * <p>Title: CipBuildStepsUploadArtifactDTO</p>
 * <p>Description:  </p>
 * <p>Copyright: Copyright (c) 2018</p>
 * <p>Company: SI-TECH </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @createtime 2021-03-24 10:04:58
 */
@Data
public class BuildStepsUploadArtifactBO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    private String repositoryUrl;
    /**
     * 仓库名
     */
    private String repositoryName;

    /**
     *
     */
    private String artifactName;
    /**
     *
     */
    private String artifactPath;

    /**
     *
     */
    private String artifactVersion;


}
