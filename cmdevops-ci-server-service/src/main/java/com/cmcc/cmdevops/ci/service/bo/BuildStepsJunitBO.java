package com.cmcc.cmdevops.ci.service.bo;

import lombok.Data;

import java.io.Serializable;


/**
 * <p>Title: CipBuildStepsJunitDTO</p>
 * <p>Description:  </p>
 * <p>Copyright: Copyright (c) 2018</p>
 * <p>Company: SI-TECH </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @createtime 2021-03-26 16:28:28
 */
@Data
public class BuildStepsJunitBO implements Serializable {
    private static final long serialVersionUID = 1L;



    /**
     *
     */
    private String id;
    /**
     *
     */
    private String taskId;
    /**
     *
     */
    private String stepName;
    /**
     *
     */
    private String toolVersion;
    /**
     *
     */
    private String command;
    /**
     *
     */
    private Integer stepSerial;
    /**
     *
     */
    private String reportpath;
    /**
     *
     */
    private String reportfactor;

    private String openCoverage;

    private String jacocoConfig;

}
