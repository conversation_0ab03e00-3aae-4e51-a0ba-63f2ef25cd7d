package com.cmcc.cmdevops.ci.service.business.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.atom.BuildParameterAtomService;
import com.cmcc.cmdevops.ci.service.bo.BuildParameterBO;
import com.cmcc.cmdevops.ci.service.business.BuildParameterBizService;
import com.cmcc.cmdevops.ci.service.business.util.EmptyValidator;
import com.cmcc.cmdevops.ci.service.business.util.UserUtils;
import com.cmcc.cmdevops.ci.service.dao.BuildParameterDO;
import com.cmcc.cmdevops.exception.BusinessException;
import com.cmcc.cmdevops.util.BeanCloner;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 构建参数业务实现类
 */
@Service
@RequiredArgsConstructor
public class BuildParameterBizServiceImpl implements BuildParameterBizService {

    private final BuildParameterAtomService buildParameterAtomService;

    @Override
    public void save(BuildParameterBO buildParameterBO) {
        BuildParameterDO buildParameterDO = BeanCloner.clone(buildParameterBO, BuildParameterDO.class);
        handleCheckUniqueParamName(buildParameterDO);
        buildParameterDO.setTenantId(UserUtils.getTenantId());
        buildParameterDO.setCreateUid(UserUtils.getUserId());
        buildParameterAtomService.save(buildParameterDO);
    }

    @Override
    public void batchSave(List<BuildParameterBO> buildParameterBOs) {
        List<BuildParameterDO> buildParameterDOs = BeanCloner.clone(buildParameterBOs, BuildParameterDO.class);
        buildParameterDOs.forEach(this::handleCheckUniqueParamName);
        buildParameterDOs.forEach(buildParameterDO -> {
            buildParameterDO.setTenantId(UserUtils.getTenantId());
            buildParameterDO.setCreateUid(UserUtils.getUserId());
        });
        buildParameterAtomService.saveBatch(buildParameterDOs);
    }

    @Override
    public void update(BuildParameterBO buildParameterBO) {
        BuildParameterDO buildParameterDO = BeanCloner.clone(buildParameterBO, BuildParameterDO.class);

        handleCheckUniqueParamName(buildParameterDO);

        buildParameterDO.setUpdateUid(UserUtils.getUserId());
        buildParameterAtomService.updateById(buildParameterDO);
    }

    @Override
    public void delete(Integer id) {
        buildParameterAtomService.removeById(id);
    }

    @Override
    public BuildParameterBO detail(Integer id) {
        BuildParameterDO buildParameterDO = buildParameterAtomService.getById(id);
        return (buildParameterDO != null) ? BeanCloner.clone(buildParameterDO, BuildParameterBO.class) : null;
    }

    @Override
    public List<BuildParameterBO> listByTaskId(String taskId) {
        List<BuildParameterDO> buildParameterDOs = buildParameterAtomService.list(new QueryWrapper<BuildParameterDO>().eq("task_id", taskId));
        return BeanCloner.clone(buildParameterDOs, BuildParameterBO.class);
    }

    @Override
    public PageResponse<List<BuildParameterBO>> page(PageRequest pageRequest, BuildParameterBO bo) {
        Page<BuildParameterDO> page = new Page<>(pageRequest.getPageNo(), pageRequest.getPageSize());
        LambdaQueryWrapper<BuildParameterDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BuildParameterDO::getTaskId, bo.getTaskId());
        if (EmptyValidator.isNotEmpty(bo.getSpaceId())){
            queryWrapper.eq(BuildParameterDO::getSpaceId, bo.getSpaceId());
        }
        queryWrapper.orderByDesc(BuildParameterDO::getCreateTime);
        Page<BuildParameterDO> result = buildParameterAtomService.page(page, queryWrapper);
        List<BuildParameterBO> list = BeanCloner.clone(result.getRecords(), BuildParameterBO.class);
        return PageResponse.success(list, result.getTotal(), pageRequest.getPageNo(), pageRequest.getPageSize());
    }

    private void handleCheckUniqueParamName(BuildParameterDO parameterDO){
        LambdaQueryWrapper<BuildParameterDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BuildParameterDO::getParamName, parameterDO.getParamName());
        queryWrapper.eq(BuildParameterDO::getTaskId, parameterDO.getTaskId());
        queryWrapper.eq(BuildParameterDO::getSpaceId, parameterDO.getSpaceId());
        if (EmptyValidator.isNotEmpty(parameterDO.getId())){
            queryWrapper.ne(BuildParameterDO::getId, parameterDO.getId());
        }
        long count = buildParameterAtomService.count(queryWrapper);
        if (count > 0){
            throw new BusinessException("参数名称不能重复");
        }
    }

    @Override
    public void deleteByTaskIds(List<String> taskIds) {
        LambdaQueryWrapper<BuildParameterDO> wrapper = new LambdaQueryWrapper<BuildParameterDO>().in(BuildParameterDO::getTaskId, taskIds);
        buildParameterAtomService.remove(wrapper);
    }
}
