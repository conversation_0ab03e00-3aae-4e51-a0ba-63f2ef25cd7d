package com.cmcc.cmdevops.ci.service.bo;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Setter
@Getter
@Builder
public class CiScheduleBO {
    public CiScheduleBO() {
    }

    public CiScheduleBO(String ciScheduleUrl, String url, String jobId, String jobXml, String userName, String token, Map<String, String> params) {
        this.ciScheduleUrl = ciScheduleUrl;
        this.url = url;
        this.jobId = jobId;
        this.jobXml = jobXml;
        this.userName = userName;
        this.token = token;
        this.params = params;
    }

    private String ciScheduleUrl;

    private String url;

    private String jobId;

    private String jobXml;

    private String userName;

    private String token;

    private Map<String, String> params;

}
