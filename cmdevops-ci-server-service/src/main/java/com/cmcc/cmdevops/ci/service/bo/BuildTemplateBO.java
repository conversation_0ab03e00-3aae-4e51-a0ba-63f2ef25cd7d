package com.cmcc.cmdevops.ci.service.bo;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 构建模板业务对象
 * </p>
 *
 * <AUTHOR> @since 2025-05-21
 */
@Getter
@Setter
@Builder
@Accessors(chain = true)
public class BuildTemplateBO implements Serializable {
    public BuildTemplateBO() {
    }

    public BuildTemplateBO(Integer id, String title, String templateDescribe, String icon, String buildConfig,
                           Integer templateType, String spaceId, String templateGroup, String tenantId,
                           Boolean deleted, String createUid, LocalDateTime createTime, String updateUid,
                           LocalDateTime updateTime, String deleteUid, LocalDateTime deleteTime) {
        this.id = id;
        this.title = title;
        this.templateDescribe = templateDescribe;
        this.icon = icon;
        this.buildConfig = buildConfig;
        this.templateType = templateType;
        this.spaceId = spaceId;
        this.templateGroup = templateGroup;
        this.tenantId = tenantId;
        this.deleted = deleted;
        this.createUid = createUid;
        this.createTime = createTime;
        this.updateUid = updateUid;
        this.updateTime = updateTime;
        this.deleteUid = deleteUid;
        this.deleteTime = deleteTime;
    }

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 模板标题
     */
    private String title;

    /**
     * 模板描述
     */
    private String templateDescribe;

    /**
     * 模板图标
     */
    private String icon;

    /**
     * 构建配置(JSON格式)
     */
    private String buildConfig;

    /**
     * 模板类型(0-系统模板, 1-自定义模板)
     */
    private Integer templateType;

    /**
     * 工作空间ID
     */
    private String spaceId;

    /**
     * 模板分组
     */
    private String templateGroup;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 是否删除
     */
    private Boolean deleted;

    /**
     * 创建人ID
     */
    private String createUid;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    private String updateUid;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除人ID
     */
    private String deleteUid;

    /**
     * 删除时间
     */
    private LocalDateTime deleteTime;
}
