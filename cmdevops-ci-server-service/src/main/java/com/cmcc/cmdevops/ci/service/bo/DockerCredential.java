package com.cmcc.cmdevops.ci.service.bo;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Builder
@Accessors(chain = true)
public class DockerCredential {
    private String userKey;
    private String userToken;

    public DockerCredential(String userKey, String userToken) {
        this.userKey = userKey;
        this.userToken = userToken;
    }

    public DockerCredential() {
    }
}
