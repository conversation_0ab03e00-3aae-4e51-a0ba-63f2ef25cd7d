package com.cmcc.cmdevops.ci.service.bo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> @since 2025-05-20
 */
@Data
@Accessors(chain = true)
public class BuildSnapshotBO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    private String taskId;

    private String buildStatus;

    private Long buildNumber;

    private Integer duration;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private String spaceId;

    private String tenantId;

    private String source;

    private Boolean deleted;

    private LocalDateTime createTime;

    private String createUid;

    private LocalDateTime updateTime;

    private String updateUid;

    private LocalDateTime deleteTime;

    private String deleteUid;
}
