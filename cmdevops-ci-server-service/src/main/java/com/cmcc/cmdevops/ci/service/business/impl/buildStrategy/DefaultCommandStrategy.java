package com.cmcc.cmdevops.ci.service.business.impl.buildStrategy;

import com.cmcc.cmdevops.ci.service.bo.*;
import com.cmcc.cmdevops.ci.service.business.BuildStrategy;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by 51746 on 2021/2/25.
 */
@Service
public class DefaultCommandStrategy implements BuildStrategy {

    @Override
    public String stageShell(BuildSnapshotBO buildSnapshotBO, BuildStepConfigBO buildStepsBO, List<BuildToolBO> buildToolBOList) {
        StringBuilder buildShell = new StringBuilder();
        BuildStepsDefaultCommandBO stepConfig = buildStepsBO.getConfig().toJavaObject(BuildStepsDefaultCommandBO.class);
        buildShell.append("stage('").append(buildStepsBO.getSerial() + 2).append("-").append(buildStepsBO.getName()).append(" ') { \n")
                .append("steps { \n")
                .append("sh '''").append(stepConfig.getCommand()).append("'''\n")
                .append("} \n")
                .append("} \n");
        return buildShell.toString();
    }
}
