package com.cmcc.cmdevops.ci.service.business.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.atom.BuildAdvancedConfigAtomService;
import com.cmcc.cmdevops.ci.service.bo.BuildAdvancedConfigBO;
import com.cmcc.cmdevops.ci.service.business.BuildAdvancedConfigBizService;
import com.cmcc.cmdevops.ci.service.business.util.EmptyValidator;
import com.cmcc.cmdevops.ci.service.dao.BuildAdvancedConfigDO;
import com.cmcc.cmdevops.util.BeanCloner;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 构建高级配置业务实现类
 */
@Service
public class BuildAdvancedConfigBizServiceImpl implements BuildAdvancedConfigBizService {

    private final BuildAdvancedConfigAtomService buildAdvancedConfigAtomService;

    public BuildAdvancedConfigBizServiceImpl(BuildAdvancedConfigAtomService buildAdvancedConfigAtomService) {
        this.buildAdvancedConfigAtomService = buildAdvancedConfigAtomService;
    }

    @Override
    public void createBuildAdvancedConfig(BuildAdvancedConfigBO buildAdvancedConfigBO) {
        // 转换为数据对象并保存
        BuildAdvancedConfigDO buildAdvancedConfigDO = BeanCloner.clone(buildAdvancedConfigBO, BuildAdvancedConfigDO.class);
        buildAdvancedConfigAtomService.save(buildAdvancedConfigDO);
    }

    @Override
    public void updateBuildAdvancedConfig(BuildAdvancedConfigBO buildAdvancedConfigBO) {
        // 转换为数据对象并更新
        BuildAdvancedConfigDO buildAdvancedConfigDO = BeanCloner.clone(buildAdvancedConfigBO, BuildAdvancedConfigDO.class);

        UpdateWrapper<BuildAdvancedConfigDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", buildAdvancedConfigBO.getId());

        buildAdvancedConfigAtomService.update(buildAdvancedConfigDO, updateWrapper);
    }

    @Override
    public void deleteBuildAdvancedConfig(BuildAdvancedConfigBO buildAdvancedConfigBO) {
        // 根据ID删除构建高级配置
        buildAdvancedConfigAtomService.removeById(buildAdvancedConfigBO.getId());
    }

    @Override
    public BuildAdvancedConfigBO getBuildAdvancedConfigById(String id) {
        // 查询构建高级配置数据
        BuildAdvancedConfigDO buildAdvancedConfigDO = buildAdvancedConfigAtomService.getById(id);
        if (buildAdvancedConfigDO != null) {
            return BeanCloner.clone(buildAdvancedConfigDO, BuildAdvancedConfigBO.class);
        }
        return null;
    }

    @Override
    public BuildAdvancedConfigBO getBuildAdvancedConfigByTaskId(String task) {
        BuildAdvancedConfigDO advancedConfigDO = buildAdvancedConfigAtomService.getOne(new QueryWrapper<BuildAdvancedConfigDO>().eq("task_id", task));
        if (EmptyValidator.isEmpty(advancedConfigDO)) {
            return new BuildAdvancedConfigBO();
        }
        return BeanCloner.clone(advancedConfigDO, BuildAdvancedConfigBO.class);
    }

    @Override
    public PageResponse<List<BuildAdvancedConfigBO>> pageBuildAdvancedConfigs(PageRequest pageRequest) {
        // 分页查询构建高级配置
        Page<BuildAdvancedConfigDO> page = new Page<>(pageRequest.getPageNo(), pageRequest.getPageSize());
        Page<BuildAdvancedConfigDO> result = buildAdvancedConfigAtomService.page(page);
        List<BuildAdvancedConfigBO> list = BeanCloner.clone(result.getRecords(), BuildAdvancedConfigBO.class);
        return PageResponse.success(list, result.getTotal(), pageRequest.getPageNo(), pageRequest.getPageSize());
    }

    @Override
    public void deleteByTaskIds(List<String> taskIds) {
        LambdaQueryWrapper<BuildAdvancedConfigDO> wrapper = new LambdaQueryWrapper<BuildAdvancedConfigDO>().in(BuildAdvancedConfigDO::getTaskId, taskIds);
        buildAdvancedConfigAtomService.remove(wrapper);
    }
}
