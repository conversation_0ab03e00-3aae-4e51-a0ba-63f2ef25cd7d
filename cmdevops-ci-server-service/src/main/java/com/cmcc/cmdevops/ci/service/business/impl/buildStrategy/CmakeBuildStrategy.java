package com.cmcc.cmdevops.ci.service.business.impl.buildStrategy;

import com.alibaba.fastjson2.JSONObject;
import com.cmcc.cmdevops.ci.service.bo.BuildSnapshotBO;
import com.cmcc.cmdevops.ci.service.bo.BuildStepConfigBO;
import com.cmcc.cmdevops.ci.service.bo.BuildStepsCmakeBO;
import com.cmcc.cmdevops.ci.service.bo.BuildToolBO;
import com.cmcc.cmdevops.ci.service.business.BuildStrategy;
import com.cmcc.cmdevops.ci.service.business.util.EmptyValidator;
import com.cmcc.cmdevops.util.BeanCloner;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class CmakeBuildStrategy implements BuildStrategy {
    @Override
    public String stageShell(BuildSnapshotBO buildSnapshotBO, BuildStepConfigBO buildStepsBO, List<BuildToolBO> buildToolBOList) {
        StringBuilder buildShell = new StringBuilder();
        BuildStepsCmakeBO cipBuildStepsCmakeDTO = buildStepsBO.getConfig().toJavaObject(BuildStepsCmakeBO.class);
        BuildToolBO buildToolBO = null;
        Optional<BuildToolBO> toolBOOptional = buildToolBOList.stream()
                .filter(buildTool -> buildTool.getToolName().equals(cipBuildStepsCmakeDTO.getToolVersion()))
                .findAny();
        if (toolBOOptional.isPresent()) {
            buildToolBO = toolBOOptional.get();
        }
        // 判断是否开启了分布式构建,如果是，先启动icecream相关容器
        String schedulerPort = "";
        if (cipBuildStepsCmakeDTO.getEnableDistributedBuild()){
            DockerComposeBuildStrategy dockerComposeBuildStrategy = new DockerComposeBuildStrategy();
            BuildStepConfigBO dockerStepConfigBo = BeanCloner.clone(buildStepsBO, BuildStepConfigBO.class);
            dockerStepConfigBo.setName("icecream分布式集群环境初始化");
            JSONObject buildStepsDockerComposeBO = new JSONObject();
            Pair<String, String> dockerComposeContentPair = processTemplateVariables(buildStepsDockerComposeBO, buildSnapshotBO);
            // 调度器对外暴露端口
            schedulerPort = dockerComposeContentPair.getRight();
            // docker-compose文件内容
            buildStepsDockerComposeBO.put("composeContent", dockerComposeContentPair.getLeft());
            buildStepsDockerComposeBO.put("operation", "up");
            buildStepsDockerComposeBO.put("detached", true);
            dockerStepConfigBo.setConfig(buildStepsDockerComposeBO);
            // 获取docker-compose启动脚本
            String shell = dockerComposeBuildStrategy.stageShell(buildSnapshotBO, dockerStepConfigBo, buildToolBOList);
            buildShell.append(shell);
        }
        if(EmptyValidator.isNotEmpty(buildToolBO)){
            String arg = "-v ${WORKSPACE}:/workspace -w /workspace";
            if (cipBuildStepsCmakeDTO.getEnableDistributedBuild()){
                arg += " -e ICECC_SCHEDULER=127.0.0.1:" + schedulerPort;
                arg += " -e USE_DISTRIBUTED_BUILD=true";
                // arg += " -e ICECC_MAX_MEM=400";
                // arg += " -e ICECC_JOBS=2";
            }else {
                arg += " -e USE_DISTRIBUTED_BUILD=false";
            }
            buildToolBO.setArgs(buildToolBO.getArgs() + " " + arg);
        }

        buildShell.append("stage('").append(buildStepsBO.getName()).append("-").append(buildStepsBO.getSerial()).append("') { \n")
                .append(JenkinsUtils.getAgent(buildToolBO))
                .append("steps { \n")
                .append("sh '''").append(wrapWithCcache(JenkinsUtils.commandReplace(cipBuildStepsCmakeDTO.getCommand()))).append("'''\n")
                .append("} \n")
                .append("} \n");
        return buildShell.toString();
    }

    private String wrapWithCcache(String userCommand) {
        // 最简单的方案：直接调用包装脚本
        // return "/usr/local/bin/jenkins-build-init.sh <<'EOF'\n" + userCommand + "\nEOF";
        return String.format(
                "#!/bin/bash\n" +
                "set -e\n" +
                "# 初始化分布式构建环境\n" +
                "source /usr/local/bin/jenkins-build-init.sh --init-only\n" +
                "# 执行用户命令\n" +
                "%s\n",
                userCommand
        );
    }

    /**
     * 处理模板变量替换
     */
    private Pair<String, String> processTemplateVariables(JSONObject buildStepsDockerComposeBO, BuildSnapshotBO buildSnapshotBO) {

        String processedContent = "services:\n" +
                "  # icecream调度器节点\n" +
                "  icecc-scheduler:\n" +
                "    image: ***********:20200/devops-test/icecream-centos7:20250930\n" +
                "    container_name: icecc-scheduler-{{BUILD_SNAPSHOT_ID}}\n" +
                "    hostname: icecc-scheduler-{{BUILD_SNAPSHOT_ID}}\n" +
                "    ports:\n" +
                "      - \"{{SCHEDULER_PORT_10245}}:10245/tcp\"\n" +
                "      - \"{{SCHEDULER_PORT_8765}}:8765/tcp\"\n" +
                "      - \"{{SCHEDULER_PORT_8766}}:8766/tcp\"\n" +
                "      - \"{{SCHEDULER_PORT_8765}}:8765/udp\"\n" +
                "    networks:\n" +
                "      - icecream-net-{{BUILD_SNAPSHOT_ID}}\n" +
                "    command:\n" +
                "      - /bin/sh\n" +
                "      - -c\n" +
                "      - |\n" +
                "        echo \"启动icecream调度器...\"\n" +
                "        icecc-scheduler -u nobody -l /tmp/icecc-scheduler.log -vvv --persistent-client-connection\n" +
                "    restart: unless-stopped\n" +
                "    \n" +
                "  # icecream工作节点1\n" +
                "  icecc-worker-1:\n" +
                "    image: ***********:20200/devops-test/icecream-centos7:20250930\n" +
                "    container_name: icecc-worker-1-{{BUILD_SNAPSHOT_ID}}\n" +
                "    hostname: icecc-worker-1-{{BUILD_SNAPSHOT_ID}}\n" +
                "    depends_on:\n" +
                "      - icecc-scheduler\n" +
                "    networks:\n" +
                "      - icecream-net-{{BUILD_SNAPSHOT_ID}}\n" +
                "    command:\n" +
                "      - /bin/sh\n" +
                "      - -c\n" +
                "      - |\n" +
                "        echo \"等待调度器启动...\"\n" +
                "        sleep 5\n" +
                "        echo \"启动icecream工作节点1...\"\n" +
                "        iceccd -u nobody -d -s {{SCHEDULER_CONNECTION}} -l /tmp/icecc-worker.log -vvv\n" +
                "        tail -f /dev/null\n" +
                "    restart: unless-stopped\n" +
                "    \n" +
                "  # icecream工作节点2\n" +
                "  icecc-worker-2:\n" +
                "    image: ***********:20200/devops-test/icecream-centos7:20250930\n" +
                "    container_name: icecc-worker-2-{{BUILD_SNAPSHOT_ID}}\n" +
                "    hostname: icecc-worker-2-{{BUILD_SNAPSHOT_ID}}\n" +
                "    depends_on:\n" +
                "      - icecc-scheduler\n" +
                "    networks:\n" +
                "      - icecream-net-{{BUILD_SNAPSHOT_ID}}\n" +
                "    command:\n" +
                "      - /bin/sh\n" +
                "      - -c\n" +
                "      - |\n" +
                "        echo \"等待调度器启动...\"\n" +
                "        sleep 8\n" +
                "        echo \"启动icecream工作节点2...\"\n" +
                "        iceccd -u nobody -d -s {{SCHEDULER_CONNECTION}} -l /tmp/icecc-worker.log -vvv\n" +
                "        tail -f /dev/null\n" +
                "    restart: unless-stopped\n" +
                "\n" +
                "networks:\n" +
                "  icecream-net-{{BUILD_SNAPSHOT_ID}}:\n" +
                "    driver: bridge\n" +
                "    ipam:\n" +
                "      config:\n" +
                "        - subnet: **********/16";

        // 替换构建快照ID
        processedContent = processedContent.replace("{{BUILD_SNAPSHOT_ID}}", buildSnapshotBO.getId());

        // 动态端口分配，计算并替换端口变量
        int basePort = 20000;
        int[] dynamicPorts = calculateDynamicPorts(buildSnapshotBO.getId(), basePort);

        processedContent = processedContent.replace("{{SCHEDULER_PORT_10245}}", String.valueOf(dynamicPorts[0]));
        processedContent = processedContent.replace("{{SCHEDULER_PORT_8765}}", String.valueOf(dynamicPorts[1]));
        processedContent = processedContent.replace("{{SCHEDULER_PORT_8766}}", String.valueOf(dynamicPorts[2]));
        processedContent = processedContent.replace("{{SCHEDULER_PORT_8765_UDP}}", String.valueOf(dynamicPorts[1]));

        // 替换其他常用变量
        processedContent = processedContent.replace("{{BUILD_TASK_ID}}", buildSnapshotBO.getTaskId());
        processedContent = processedContent.replace("{{BUILD_NUMBER}}", String.valueOf(buildSnapshotBO.getBuildNumber()));

        // 替换调度器连接地址变量（用于worker节点连接）
        // 在Docker Compose网络内部，容器间通信使用容器名和内部端口
        // 动态端口只影响宿主机到容器的映射，容器间通信仍使用内部端口8765
        String schedulerContainerName = "icecc-scheduler-" + buildSnapshotBO.getId();

        processedContent = processedContent.replace("{{SCHEDULER_HOST}}", schedulerContainerName);
        processedContent = processedContent.replace("{{SCHEDULER_PORT}}", "8765"); // 容器内部端口始终是8765
        processedContent = processedContent.replace("{{SCHEDULER_CONNECTION}}", schedulerContainerName + ":8765");

        return Pair.of(processedContent, String.valueOf(dynamicPorts[1]));
    }

    /**
     * 计算动态端口范围
     */
    private int[] calculateDynamicPorts(String buildSnapshotId, int basePort) {
        // 使用buildSnapshotId的哈希值确保唯一性和一致性
        int hash = Math.abs(buildSnapshotId.hashCode());
        // 每个构建任务分配10个连续端口，避免冲突
        int portOffset = (hash % 5000) * 10;
        int startPort = basePort + portOffset;

        return new int[]{
                startPort,     // 对应10245端口
                startPort + 1, // 对应8765端口
                startPort + 2  // 对应8766端口
        };
    }
}
