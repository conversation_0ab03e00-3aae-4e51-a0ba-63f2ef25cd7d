package com.cmcc.cmdevops.ci.job.processor;

import com.cmcc.cmdevops.util.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;
import tech.powerjob.worker.log.OmsLogger;

import java.util.Date;

@Component
public class BuildTaskProcessor implements BasicProcessor{

    private static final Logger logger = LoggerFactory.getLogger(BuildTaskProcessor.class);

    @Override
    public ProcessResult process(TaskContext context) throws Exception{
        OmsLogger omsLogger = context.getOmsLogger();
        omsLogger.info(">>>>>>>>>>> BuildTaskProcessor begin");
        logger.info(">>>>>>>>>>> BuildTaskProcessor begin");
        String now = DateUtil.format(new Date(), DateUtil.DATETIME_FORMAT);
        String param = context.getJobParams();
        omsLogger.info("param: {}", param);
        logger.info("param: {}", param);
        // 模拟任务处理
        try {
            // todo:业务逻辑处理...
            Thread.sleep(1000);

            omsLogger.info(">>>>>>>>>>> BuildTaskProcessor end");
            logger.info(">>>>>>>>>>> BuildTaskProcessor end");
            // 返回执行结果
            return new ProcessResult(true, "任务执行成功");
        } catch (Exception e) {
            omsLogger.error(">>>>>>>>>>> BuildTaskProcessor error: " + e.getMessage());
            logger.error(">>>>>>>>>>> BuildTaskProcessor error: " + e.getMessage());
            return new ProcessResult(false, "任务执行失败: " + e.getMessage());
        }
    }

}
