package com.cmcc.cmdevops.ci.job.processor;

import com.cmcc.cmdevops.ci.service.bo.BuildTriggerConfigBO;
import com.cmcc.cmdevops.ci.service.business.BuildTaskTriggerService;
import com.cmcc.cmdevops.ci.service.business.BuildTriggerConfigBizService;
import com.cmcc.cmdevops.ci.service.business.util.EmptyValidator;
import com.cmcc.cmdevops.util.DateUtil;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;
import tech.powerjob.worker.log.OmsLogger;

import java.util.Date;
import java.util.List;

@Component
public class BuildTriggerConfigPollingProcessor implements BasicProcessor{

    private static final Logger logger = LoggerFactory.getLogger(BuildTriggerConfigPollingProcessor.class);

    @Resource
    private BuildTriggerConfigBizService buildTriggerConfigBizService;

    @Resource
    private BuildTaskTriggerService buildTaskTriggerService;

    @Override
    public ProcessResult process(TaskContext context) throws Exception{
        OmsLogger omsLogger = context.getOmsLogger();
        omsLogger.info(">>>>>>>>>>> BuildTriggerConfigPollingProcessor begin");
        logger.info(">>>>>>>>>>> BuildTriggerConfigPollingProcessor begin");
        String now = DateUtil.format(new Date(), DateUtil.DATETIME_FORMAT);
        try {
            List<BuildTriggerConfigBO> triggerConfigs = buildTriggerConfigBizService.getAllBuildTriggerConfigs();
            triggerConfigs.forEach(triggerConfig -> {
                try {
                    if(triggerConfig.getDeleted()
                        && EmptyValidator.isNotEmpty(triggerConfig.getJobExeTime())
                        && triggerConfig.getJobExeTime().isBefore(triggerConfig.getDeleteTime())){
                        // job创建后任务被删除需要删除对应job
                        buildTaskTriggerService.deleteJob(triggerConfig.getTaskId());

                    }else if(!triggerConfig.getTimeTriggerSwitch()
                        && EmptyValidator.isNotEmpty(triggerConfig.getJobExeTime())){
                        //  job创建后定时开关被关闭需要删除对应job
                        buildTaskTriggerService.deleteJob(triggerConfig.getTaskId());
                    }
                    else if(!triggerConfig.getDeleted()
                        && triggerConfig.getTimeTriggerSwitch()
                        && EmptyValidator.isNotEmpty(triggerConfig.getTimeTriggerCrontab())
                        && ( EmptyValidator.isEmpty(triggerConfig.getJobExeTime())
                           || triggerConfig.getJobExeTime().isBefore(EmptyValidator.isEmpty(triggerConfig.getUpdateTime())?triggerConfig.getCreateTime():triggerConfig.getUpdateTime()))){
                        // 任务有效 && 打开定时调度开关 && cron非空 && （job未被创建或者创建后执行计划被修改）
                        buildTaskTriggerService.createJob(triggerConfig.getTaskId(), triggerConfig.getTimeTriggerCrontab());
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });

            omsLogger.info(">>>>>>>>>>> BuildTriggerConfigPollingProcessor end");
            logger.info(">>>>>>>>>>> BuildTriggerConfigPollingProcessor end");
            // 返回执行结果
            return new ProcessResult(true, "轮询创建job任务执行成功");
        } catch (Exception e) {
            omsLogger.error(">>>>>>>>>>> BuildTriggerConfigPollingProcessor error: " + e.getMessage());
            logger.error(">>>>>>>>>>> BuildTriggerConfigPollingProcessor error: " + e.getMessage());
            return new ProcessResult(false, "轮询创建job任务执行失败: " + e.getMessage());
        }
    }

}
