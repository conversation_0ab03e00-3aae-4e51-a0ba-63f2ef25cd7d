package com.cmcc.cmdevops.ci.service.business.impl.buildStrategy;

import com.alibaba.fastjson2.JSONObject;
import com.cmcc.cmdevops.ci.service.bo.*;
import com.cmcc.cmdevops.ci.service.business.BuildStrategy;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class UploadCacheDirStrategy implements BuildStrategy {

    @Override
    public String stageShell(BuildSnapshotBO buildSnapshotBO, BuildStepConfigBO buildStepsBO, List<BuildToolBO> buildToolBOList) {
        StringBuilder buildShell = new StringBuilder();
        BuildStepsUploadCacheDirBO dto = buildStepsBO.getConfig().toJavaObject(BuildStepsUploadCacheDirBO.class);
        String cacheDir = dto.getCacheDir();
        String[] cacheDirs = cacheDir.split(",");
        StringBuilder stringBuffer = new StringBuilder();
        for (String dir : cacheDirs) {
            dir = dir.substring(1, dir.length());
            stringBuffer.append(dir).append(" ");
        }

        buildShell.append("stage('" + (buildStepsBO.getSerial() + 2) + "-" + buildStepsBO.getName() + " ') { \n")
                .append("steps { \n")
                .append("echo '上传构建缓存' \n")
                .append("dir('/') {\n")
                .append("  noOutputSh('tar zcf cache.tar.gz " + stringBuffer + "')\n")
                .append("  withEnv(['AWS_ACCESS_KEY_ID=" + dto.getAccessKey() + "', 'AWS_SECRET_ACCESS_KEY=" + dto.getSecretKey() + "']) {\n")
                .append("    noOutputSh('aws s3 cp cache.tar.gz s3://build-cache/" + buildSnapshotBO.getTaskId() + "/dependency-cache/cache.tar.gz --endpoint-url " + dto.getS3Endpoint() + " --no-progress')\n")
                .append("  }\n")
                .append("}\n")
                .append("} \n")
                .append("} \n");
        return buildShell.toString();
    }
}
