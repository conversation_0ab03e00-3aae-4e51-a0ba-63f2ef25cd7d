package com.cmcc.cmdevops.ci.service.business;

import com.cmcc.cmdevops.ci.service.bo.BuildSnapshotBO;
import com.cmcc.cmdevops.ci.service.bo.BuildStepConfigBO;
import com.cmcc.cmdevops.ci.service.bo.BuildToolBO;

import java.util.List;

/**
 * <AUTHOR>
 * <p>
 * 构建策略
 */
public interface BuildStrategy {

    /**
     * 阶段脚本
     *
     * @param stageType
     * @param jsonObject
     * @return
     */
    String stageShell(BuildSnapshotBO buildSnapshotBO, BuildStepConfigBO buildStepBO, List<BuildToolBO> buildToolBOList);
}
