package com.cmcc.cmdevops.ci.service.bo;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;


/**
* <p>Title: CipBuildStepsGoDTO</p>
* <p>Description:  </p>
* <p>Copyright: Copyright (c) 2018</p>
* <p>Company: SI-TECH </p>
* <AUTHOR>
* @version 1.0
* @createtime 2021-04-26 09:30:29
*
*/
@Data
@Builder
public class BuildStepsGoBO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String toolVersion;
    /**
     *
     */
    private String command;
}
