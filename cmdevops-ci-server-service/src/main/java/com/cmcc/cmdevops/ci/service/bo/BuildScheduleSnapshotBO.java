package com.cmcc.cmdevops.ci.service.bo;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@Builder
public class BuildScheduleSnapshotBO implements Serializable {
    public BuildScheduleSnapshotBO() {
    }

    public BuildScheduleSnapshotBO(String id, String ciScheduleUrl, String ciToolUrl, String jobXml, String userName, String token, Map<String, String> params, LocalDateTime createTime, String stageNum, String cacheId, String s3Endpoint, String accessKey, String secretKey, List<DockerCredential> dockerCredentials) {
        this.id = id;
        this.ciScheduleUrl = ciScheduleUrl;
        this.ciToolUrl = ciToolUrl;
        this.jobXml = jobXml;
        this.userName = userName;
        this.token = token;
        this.params = params;
        this.createTime = createTime;
        this.stageNum = stageNum;
        this.cacheId = cacheId;
        this.s3Endpoint = s3Endpoint;
        this.accessKey = accessKey;
        this.secretKey = secretKey;
        this.dockerCredentials = dockerCredentials;
    }

    private static final long serialVersionUID = 1L;

    private String id;

    private String ciScheduleUrl;

    private String ciToolUrl;

    private String jobXml;

    private String userName;

    private String token;

    private Map<String, String> params;

    private LocalDateTime createTime;

    private String stageNum;

    private String cacheId;

    private String s3Endpoint;

    private String accessKey;

    private String secretKey;

    private List<DockerCredential> dockerCredentials;
}

