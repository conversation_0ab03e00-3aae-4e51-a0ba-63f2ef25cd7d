package com.cmcc.cmdevops.ci.service.business.impl.buildStrategy;


import com.cmcc.cmdevops.ci.service.bo.BuildToolBO;
import com.cmcc.cmdevops.ci.service.business.util.EmptyValidator;

/**
 * <p>Title: CipBuildToolAppImpl</p>
 * <p>Description:  单表业务逻辑操作接口实现类 </p>
 * <p>Copyright: Copyright (c) 2018</p>
 * <p>Company: SI-TECH </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @createtime 2021-03-10 15:55:23
 */
public class JenkinsUtils {
    public static String getAgent(BuildToolBO buildToolBO) {
        StringBuilder agent = new StringBuilder();
        if (EmptyValidator.isNotEmpty(buildToolBO)) {
            String cache = "";
            if (EmptyValidator.isNotEmpty(buildToolBO.getCacheDir())) {
                cache = buildToolBO.getCacheDir();
            }
            agent.append("agent {\n" +
                    "                docker {\n" +
                    "                  image '" + buildToolBO.getToolImage() + "'\n" +
                    "                  args '" + buildToolBO.getArgs() + ' ' + cache + "'\n" +
                    "                  reuseNode true\n" +
                    "                }\n" +
                    "            }\n");
        }
        return agent.toString();
    }

    public static String commandReplace(String str) {
        return str.replace("\\", "\\\\");
    }
}
