package com.cmcc.cmdevops.ci.service.business;

import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.bo.BuildAdvancedConfigBO;
import com.cmcc.cmdevops.ci.service.bo.BuildArtifactBO;
import com.cmcc.cmdevops.ci.service.bo.BuildToolBO;

import java.util.List;

/**
 * 构建产物业务接口
 */
public interface BuildArtifactBizService {
    /**
     * 创建构建产物
     *
     * @param buildArtifactBO 构建产物数据对象
     */
    void createBuildArtifact(BuildArtifactBO buildArtifactBO);

    /**
     * 更新构建产物
     *
     * @param buildArtifactBO 构建产物数据对象
     */
    void updateBuildArtifact(BuildArtifactBO buildArtifactBO);

    /**
     * 删除构建产物
     *
     * @param buildArtifactBO 构建产物数据对象
     */
    void deleteBuildArtifact(BuildArtifactBO buildArtifactBO);

    /**
     * 根据ID获取构建产物
     *
     * @param id 构建产物ID
     * @return 构建产物数据对象
     */
    BuildArtifactBO getBuildArtifactById(String id);

    PageResponse<List<BuildArtifactBO>> pageBuildArtifacts(PageRequest pageRequest);

    List<BuildArtifactBO> list(BuildArtifactBO buildArtifactBO);
}
