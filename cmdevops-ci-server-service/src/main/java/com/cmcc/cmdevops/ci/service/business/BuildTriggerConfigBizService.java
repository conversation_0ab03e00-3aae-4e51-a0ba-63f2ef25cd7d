package com.cmcc.cmdevops.ci.service.business;

import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.bo.BuildTriggerConfigBO;
import java.util.List;

/**
 * 构建触发配置业务接口
 */
public interface BuildTriggerConfigBizService {
    /**
     * 创建构建触发配置
     * @param buildTriggerConfigBO 构建触发配置数据对象
     */
    void createBuildTriggerConfig(BuildTriggerConfigBO buildTriggerConfigBO);

    /**
     * 更新构建触发配置
     * @param buildTriggerConfigBO 构建触发配置数据对象
     */
    void updateBuildTriggerConfig(BuildTriggerConfigBO buildTriggerConfigBO);

    /**
     * 删除构建触发配置
     * @param buildTriggerConfigBO 构建触发配置数据对象
     */
    void deleteBuildTriggerConfig(BuildTriggerConfigBO buildTriggerConfigBO);

    /**
     * 根据ID获取构建触发配置
     * @param id 构建触发配置ID
     * @return 构建触发配置数据对象
     */
    BuildTriggerConfigBO getBuildTriggerConfigById(String id);

    /**
     * 分页查询构建触发配置列表
     * @param pageRequest 分页请求参数
     * @return 分页响应结果
     */
    PageResponse<List<BuildTriggerConfigBO>> pageBuildTriggerConfigs(PageRequest pageRequest);

    /**
     * 根据任务ID获取构建触发配置
     * @param taskId 任务ID
     * @return 构建触发配置数据对象
     */
    BuildTriggerConfigBO getBuildTriggerConfigByTaskId(String taskId);

    void deleteByTaskIds(List<String> taskIds);

    List<BuildTriggerConfigBO> getAllBuildTriggerConfigs();
}
