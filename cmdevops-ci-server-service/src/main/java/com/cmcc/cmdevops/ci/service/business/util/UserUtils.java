package com.cmcc.cmdevops.ci.service.business.util;

import com.cmcc.cmdevops.component.auth.user.model.UserInfo;
import com.cmcc.cmdevops.component.auth.user.utils.JwtHeaderInfo;

public class UserUtils {
    public static String getUserId() {
        UserInfo userInfo = null;
        try {
            userInfo = JwtHeaderInfo.getUserInfo();
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (EmptyValidator.isEmpty(userInfo)) {
            return "999999";
        } else {
            return userInfo.getUserId();
        }
    }

    public static String getTenantId() {
        UserInfo userInfo = null;
        try {
            userInfo = JwtHeaderInfo.getUserInfo();
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (EmptyValidator.isEmpty(userInfo)) {
            return "xxjs";
        } else {
            return userInfo.getTenantId();
        }
    }
}
