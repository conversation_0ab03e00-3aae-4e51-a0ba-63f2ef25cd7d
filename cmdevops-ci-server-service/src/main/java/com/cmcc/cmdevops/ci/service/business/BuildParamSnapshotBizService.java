package com.cmcc.cmdevops.ci.service.business;

import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.bo.BuildParamSnapshotBO;
import java.util.List;

/**
 * 构建参数快照业务接口
 */
public interface BuildParamSnapshotBizService {
    /**
     * 创建构建参数快照
     * @param buildParamSnapshotBO 构建参数快照数据对象
     */
    void createBuildParamSnapshot(BuildParamSnapshotBO buildParamSnapshotBO);

    /**
     * 更新构建参数快照
     * @param buildParamSnapshotBO 构建参数快照数据对象
     */
    void updateBuildParamSnapshot(BuildParamSnapshotBO buildParamSnapshotBO);

    /**
     * 删除构建参数快照
     * @param buildParamSnapshotBO 构建参数快照数据对象
     */
    void deleteBuildParamSnapshot(BuildParamSnapshotBO buildParamSnapshotBO);

    /**
     * 根据ID获取构建参数快照
     * @param id 构建参数快照ID
     * @return 构建参数快照数据对象
     */
    BuildParamSnapshotBO getBuildParamSnapshotById(String id);

    /**
     * 分页查询构建参数快照列表
     * @param pageRequest 分页请求参数
     * @return 分页响应结果
     */
    PageResponse<List<BuildParamSnapshotBO>> pageBuildParamSnapshots(PageRequest pageRequest);

    List<BuildParamSnapshotBO> list(BuildParamSnapshotBO paramSnapshotBO);
}
