package com.cmcc.cmdevops.ci.feign;

import com.cmcc.cmdevops.BaseResponse;
import com.cmcc.cmdevops.ci.feign.dto.UserInfoDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * 微服务调用，使用feign调用，但不使用spring-cloud，以适应k8s和服务网格
 * url那里可以注入地址
 * 下面的path应使用环境变量注入，表示context-path
 *
 * <AUTHOR>
 */
@FeignClient(name = "user-demo-service", path = "/xxx")
public interface FeignUserService {
    /**
     * 调用微服务
     *
     * @param userId 用户标识
     * @return 用户信息对象
     */
    @PostMapping("/api/v1/user/get")
    BaseResponse<UserInfoDTO> getUserInfo(String userId);
}
