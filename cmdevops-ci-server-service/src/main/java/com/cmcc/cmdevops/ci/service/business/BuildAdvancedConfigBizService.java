package com.cmcc.cmdevops.ci.service.business;

import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.bo.BuildAdvancedConfigBO;

import java.util.List;

/**
 * 构建高级配置业务接口
 */
public interface BuildAdvancedConfigBizService {
    /**
     * 创建构建高级配置
     *
     * @param buildAdvancedConfigBO 构建高级配置数据对象
     */
    void createBuildAdvancedConfig(BuildAdvancedConfigBO buildAdvancedConfigBO);

    /**
     * 更新构建高级配置
     *
     * @param buildAdvancedConfigBO 构建高级配置数据对象
     */
    void updateBuildAdvancedConfig(BuildAdvancedConfigBO buildAdvancedConfigBO);

    /**
     * 删除构建高级配置
     *
     * @param buildAdvancedConfigBO 构建高级配置数据对象
     */
    void deleteBuildAdvancedConfig(BuildAdvancedConfigBO buildAdvancedConfigBO);

    /**
     * 根据ID获取构建高级配置
     *
     * @param id 构建高级配置ID
     * @return 构建高级配置数据对象
     */
    BuildAdvancedConfigBO getBuildAdvancedConfigById(String id);


    BuildAdvancedConfigBO getBuildAdvancedConfigByTaskId(String task);

    /**
     * 分页查询构建高级配置列表
     *
     * @param pageRequest 分页请求参数
     * @return 分页响应结果
     */
    PageResponse<List<BuildAdvancedConfigBO>> pageBuildAdvancedConfigs(PageRequest pageRequest);

    void deleteByTaskIds(List<String> taskIds);
}
