package com.cmcc.cmdevops.ci.service.business.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cmcc.cmdevops.ci.service.atom.BuildScheduleSnapshotAtomService;
import com.cmcc.cmdevops.ci.service.bo.BuildScheduleSnapshotBO;
import com.cmcc.cmdevops.ci.service.bo.DockerCredential;
import com.cmcc.cmdevops.ci.service.business.BuildScheduleSnapshotBizService;
import com.cmcc.cmdevops.ci.service.business.util.EmptyValidator;
import com.cmcc.cmdevops.ci.service.dao.BuildScheduleSnapshotDO;
import com.cmcc.cmdevops.util.BeanCloner;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;


/**
 * 构建脚本业务实现类
 */
@Service
public class BuildScheduleSnapshotBizServiceImpl implements BuildScheduleSnapshotBizService {

    private final BuildScheduleSnapshotAtomService buildScheduleSnapshotAtomService;

    public BuildScheduleSnapshotBizServiceImpl(BuildScheduleSnapshotAtomService buildScheduleSnapshotAtomService) {
        this.buildScheduleSnapshotAtomService = buildScheduleSnapshotAtomService;
    }


    @Override
    public void create(BuildScheduleSnapshotBO buildScheduleSnapshotBO) {
        BuildScheduleSnapshotDO buildScheduleSnapshotDO = new BuildScheduleSnapshotDO();
        buildScheduleSnapshotDO.setId(buildScheduleSnapshotBO.getId());
        buildScheduleSnapshotDO.setCiScheduleUrl(buildScheduleSnapshotBO.getCiScheduleUrl());
        buildScheduleSnapshotDO.setCiToolUrl(buildScheduleSnapshotBO.getCiToolUrl());
        buildScheduleSnapshotDO.setJobXml(buildScheduleSnapshotBO.getJobXml());
        buildScheduleSnapshotDO.setUserName(buildScheduleSnapshotBO.getUserName());
        buildScheduleSnapshotDO.setToken(buildScheduleSnapshotBO.getToken());
        buildScheduleSnapshotDO.setParams(JSONObject.toJSONString(buildScheduleSnapshotBO.getParams()));
        buildScheduleSnapshotDO.setDockerCredentials(JSONObject.toJSONString(buildScheduleSnapshotBO.getDockerCredentials()));
        buildScheduleSnapshotDO.setCreateTime(LocalDateTime.now());
        buildScheduleSnapshotAtomService.save(buildScheduleSnapshotDO);
    }

    @Override
    public BuildScheduleSnapshotBO getById(String id) {
        QueryWrapper<BuildScheduleSnapshotDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("id", "ci_schedule_url", "ci_tool_url", "create_time", "Docker_credentials");
        queryWrapper.eq("id", id);
        BuildScheduleSnapshotDO buildScheduleSnapshotDO = buildScheduleSnapshotAtomService.getOne(queryWrapper);
        if (buildScheduleSnapshotDO != null) {
            BuildScheduleSnapshotBO buildScheduleSnapshotBO = new BuildScheduleSnapshotBO();
            buildScheduleSnapshotBO.setId(buildScheduleSnapshotDO.getId());
            buildScheduleSnapshotBO.setCiScheduleUrl(buildScheduleSnapshotDO.getCiScheduleUrl());
            buildScheduleSnapshotBO.setCiToolUrl(buildScheduleSnapshotDO.getCiToolUrl());
            buildScheduleSnapshotBO.setCreateTime(buildScheduleSnapshotDO.getCreateTime());
            if (EmptyValidator.isNotEmpty(buildScheduleSnapshotBO.getDockerCredentials())) {
                buildScheduleSnapshotBO.setDockerCredentials(JSON.parseArray(buildScheduleSnapshotDO.getDockerCredentials(), DockerCredential.class));
            }
            return buildScheduleSnapshotBO;
        }
        return null;
    }

    @Override
    public BuildScheduleSnapshotBO getDetailById(String id) {
        QueryWrapper<BuildScheduleSnapshotDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        BuildScheduleSnapshotDO buildScheduleSnapshotDO = buildScheduleSnapshotAtomService.getOne(queryWrapper);
        if (buildScheduleSnapshotDO != null) {
            BuildScheduleSnapshotBO buildScheduleSnapshotBO = new BuildScheduleSnapshotBO();
            buildScheduleSnapshotBO.setId(buildScheduleSnapshotDO.getId());
            buildScheduleSnapshotBO.setCiScheduleUrl(buildScheduleSnapshotDO.getCiScheduleUrl());
            buildScheduleSnapshotBO.setCiToolUrl(buildScheduleSnapshotDO.getCiToolUrl());
            buildScheduleSnapshotBO.setJobXml(buildScheduleSnapshotDO.getJobXml());
            buildScheduleSnapshotBO.setParams(JSON.parseObject(buildScheduleSnapshotDO.getParams(), new TypeReference<Map<String, String>>() {}));
            buildScheduleSnapshotBO.setCreateTime(buildScheduleSnapshotDO.getCreateTime());
            buildScheduleSnapshotBO.setUserName(buildScheduleSnapshotDO.getUserName());
            buildScheduleSnapshotBO.setToken(buildScheduleSnapshotDO.getToken());
            if (EmptyValidator.isNotEmpty(buildScheduleSnapshotBO.getDockerCredentials())) {
                buildScheduleSnapshotBO.setDockerCredentials(JSON.parseArray(buildScheduleSnapshotDO.getDockerCredentials(), DockerCredential.class));
            }
            return buildScheduleSnapshotBO;
        }
        return null;
    }
}
