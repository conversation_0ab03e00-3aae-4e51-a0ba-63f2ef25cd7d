package com.cmcc.cmdevops.ci.service.business.impl.buildStrategy;

import com.alibaba.fastjson2.JSONObject;
import com.cmcc.cmdevops.ci.service.bo.*;
import com.cmcc.cmdevops.ci.service.business.BuildStrategy;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class DownloadCacheDirStrategy implements BuildStrategy {

    @Override
    public String stageShell(BuildSnapshotBO buildSnapshotBO, BuildStepConfigBO buildStepsBO, List<BuildToolBO> buildToolBOList) {
        StringBuilder buildShell = new StringBuilder();
        JSONObject config = buildStepsBO.getConfig();
        BuildStepsDownloadCacheDirBO dto = config.toJavaObject(BuildStepsDownloadCacheDirBO.class);
        String cacheDir = dto.getCacheDir();
        cacheDir = cacheDir.replaceAll(",", " ");
        buildShell.append("stage('" + (buildStepsBO.getSerial() + 2) + "-" + buildStepsBO.getName() + " ') { \n")
                .append("steps { \n")
                .append("echo '下载构建缓存'\n")
                .append("withEnv(['AWS_ACCESS_KEY_ID=" + dto.getAccessKey() + "', 'AWS_SECRET_ACCESS_KEY=" + dto.getSecretKey() + "']) {\n")
                .append("script {\n")
                .append("noOutputSh('mkdir -p " + cacheDir + "')\n")
                .append("def exists = sh(script: 'aws s3 ls s3://build-cache/" + buildSnapshotBO.getTaskId() + "/dependency-cache/cache.tar.gz --endpoint-url " + dto.getS3Endpoint() + " > /dev/null 2>&1', returnStatus: true) == 0\n")
                .append("if (exists) {\n")
                .append("  noOutputSh('aws s3 cp s3://build-cache/" + buildSnapshotBO.getTaskId() + "/dependency-cache/cache.tar.gz . --endpoint-url " + dto.getS3Endpoint() + "  --no-progress')\n")
                .append("  if(fileExists('cache.tar.gz')) {\n")
                .append("    noOutputSh('tar zxf cache.tar.gz -C /; rm cache.tar.gz')\n")
                .append("  }\n")
                .append("}\n")
                .append("}\n")
                .append("} \n")
                .append("} \n")
                .append("} \n");
        return buildShell.toString();
    }
}
