package com.cmcc.cmdevops.ci.service.business;

import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.bo.BuildGroupBO;

import java.util.List;
/**
 * <p>Title: BuildGroupBizService</p>
 * <p>Description: 分组功能 - 接口类 </p>
 * <p>Copyright: Copyright (c) 2021</p>
 * <p>Company: SI-TECH </p>
 * Author by_csd_hlj
 * Version 1.0
 * CreateTime 2025/5/29 16:44
 */
public interface BuildGroupBizService {
    PageResponse<List<BuildGroupBO>> pageBuildGroups(BuildGroupBO buildGroupBO );

    List<BuildGroupBO> listBuildGroups(BuildGroupBO buildGroupBO);

    BuildGroupBO getGroup(Integer groupId);

    String saveGroup(BuildGroupBO buildGroupBO) throws Exception;

    String deleteGroup(Integer groupId);

    void batchGroupRelation(BuildGroupBO buildGroupBO);
}
