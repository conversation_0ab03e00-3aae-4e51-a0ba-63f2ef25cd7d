package com.cmcc.cmdevops.ci.service.business;

import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.bo.BuildConfigFileBO;
import com.cmcc.cmdevops.ci.service.bo.BuildPermissionBO;
import com.cmcc.cmdevops.ci.service.bo.BuildStepConfigBO;
import com.cmcc.cmdevops.ci.service.bo.BuildTaskBO;

import java.util.List;

/**
 * 权限业务接口
 */
public interface BuildPermissionBizService {

    void save(List<BuildPermissionBO> bos);


    void update(BuildPermissionBO buildPermissionBO);


    void delete(String userUid);

    PageResponse<List<BuildPermissionBO>> page(PageRequest pageRequest, BuildPermissionBO buildPermissionBO);

    Boolean validPermission(String taskId,String permissionCode);

    String validPermissionBatch(List<BuildTaskBO> bos, String permissionCode);
}
