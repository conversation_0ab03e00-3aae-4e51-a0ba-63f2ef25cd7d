package com.cmcc.cmdevops.ci.service.business.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.atom.BuildProjectRelationAtomService;
import com.cmcc.cmdevops.ci.service.atom.BuildProjectSnapshotAtomService;
import com.cmcc.cmdevops.ci.service.atom.BuildSnapshotAtomService;
import com.cmcc.cmdevops.ci.service.atom.BuildTaskAtomService;
import com.cmcc.cmdevops.ci.service.bo.*;
import com.cmcc.cmdevops.ci.service.business.BuildCodeSnapshotBizService;
import com.cmcc.cmdevops.ci.service.business.BuildScheduleSnapshotBizService;
import com.cmcc.cmdevops.ci.service.business.BuildSnapshotBizService;
import com.cmcc.cmdevops.ci.service.business.CiScheduleBizService;
import com.cmcc.cmdevops.ci.service.business.SWorkerOpenBizService;
import com.cmcc.cmdevops.ci.service.business.util.EmptyValidator;
import com.cmcc.cmdevops.ci.service.business.util.UserUtils;
import com.cmcc.cmdevops.ci.service.dao.BuildProjectRelationDO;
import com.cmcc.cmdevops.ci.service.dao.BuildProjectSnapshotDO;
import com.cmcc.cmdevops.ci.service.dao.BuildSnapshotDO;
import com.cmcc.cmdevops.ci.service.dao.BuildTaskDO;
import com.cmcc.cmdevops.component.oss.client.OssClient;
import com.cmcc.cmdevops.util.BeanCloner;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 构建快照业务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BuildSnapshotBizServiceImpl implements BuildSnapshotBizService {
    static final String SUCCESS = "SUCCESS";
    static final String FAILURE = "FAILURE";

    static final String ABORTED = "ABORTED";

    private final BuildScheduleSnapshotBizService buildScheduleSnapshotBizService;
    private final BuildArtifactBizServiceImpl buildArtifactBizService;
    private final CiScheduleBizService ciScheduleBizService;
    private final BuildTaskAtomService buildTaskAtomService;
    private final BuildCodeSnapshotBizService buildCodeSnapshotBizService;
    private final BuildStepSnapshotBizServiceImpl buildStepSnapshotBizService;
    private final BuildSnapshotAtomService buildSnapshotAtomService;
    private final OssClient ossClient;
    private final SWorkerOpenBizService sWorkerOpenBizService;
    private final DynamicTaskManager dynamicTaskManager;
    private final BuildProjectSnapshotAtomService buildProjectSnapshotAtomService;
    private final BuildProjectRelationAtomService buildProjectRelationAtomService;

    @Override
    public void createBuildSnapshot(BuildSnapshotBO buildSnapshotBO) {
        BuildSnapshotDO buildSnapshotDO = BeanCloner.clone(buildSnapshotBO, BuildSnapshotDO.class);
        QueryWrapper<BuildSnapshotDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("task_id", buildSnapshotBO.getTaskId());
        long buildNumber = buildSnapshotAtomService.count(queryWrapper);
        buildSnapshotDO.setBuildNumber(buildNumber + 1);
        buildSnapshotBO.setBuildNumber(buildSnapshotDO.getBuildNumber());
        buildSnapshotAtomService.save(buildSnapshotDO);
    }

    @Override
    public void updateBuildSnapshot(BuildSnapshotBO buildSnapshotBO) {
        BuildSnapshotDO buildSnapshotDO = BeanCloner.clone(buildSnapshotBO, BuildSnapshotDO.class);

        UpdateWrapper<BuildSnapshotDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", buildSnapshotBO.getId());

        buildSnapshotAtomService.update(buildSnapshotDO, updateWrapper);
    }

    @Override
    public void deleteBuildSnapshot(BuildSnapshotBO buildSnapshotBO) {
        buildSnapshotAtomService.removeById(buildSnapshotBO.getId());
    }

    @Override
    public BuildSnapshotBO getBuildSnapshotById(String id) {
        BuildSnapshotDO buildSnapshotDO = buildSnapshotAtomService.getById(id);
        return (buildSnapshotDO != null) ? BeanCloner.clone(buildSnapshotDO, BuildSnapshotBO.class) : null;
    }

    public PageResponse<List<BuildSnapshotBO>> list(PageRequest pageRequest, BuildSnapshotBO buildSnapshotBO) {
        Page<BuildSnapshotDO> page = new Page<>(pageRequest.getPageNo(), pageRequest.getPageSize());
        LambdaQueryWrapper<BuildSnapshotDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BuildSnapshotDO::getTaskId, buildSnapshotBO.getTaskId());
        if (EmptyValidator.isNotEmpty(buildSnapshotBO.getStartTime()) && EmptyValidator.isNotEmpty(buildSnapshotBO.getEndTime())) {
            queryWrapper.ge(BuildSnapshotDO::getCreateTime, buildSnapshotBO.getStartTime());
            queryWrapper.le(BuildSnapshotDO::getCreateTime, buildSnapshotBO.getEndTime());
        }
        if (EmptyValidator.isNotEmpty(buildSnapshotBO.getBuildStatus())){
            queryWrapper.eq(BuildSnapshotDO::getBuildStatus, buildSnapshotBO.getBuildStatus());
        }
        queryWrapper.orderByDesc(BuildSnapshotDO::getCreateTime);
        Page<BuildSnapshotDO> result = buildSnapshotAtomService.page(page, queryWrapper);
        List<BuildSnapshotBO> list = BeanCloner.clone(result.getRecords(), BuildSnapshotBO.class);
        return PageResponse.success(list, result.getTotal(), pageRequest.getPageNo(), pageRequest.getPageSize());
    }

    @Override
    public void addJob(String jobId) {
        dynamicTaskManager.addJob(jobId);
    }

    @Override
    public void getCollectData(String jobId, JSONObject taskStatus) {
        BuildSnapshotBO buildSnapshotBO = this.getBuildSnapshotById(jobId);
        String state = taskStatus.getString("state");
        String result = taskStatus.getString("result");
        Integer durationInMillis = taskStatus.getInteger("durationInMillis");
        LocalDateTime startTime = taskStatus.getLocalDateTime("startTime");
        LocalDateTime endTime = taskStatus.getLocalDateTime("endTime");
        buildSnapshotBO.setStartTime(startTime);
        buildSnapshotBO.setEndTime(endTime);
        buildSnapshotBO.setDuration(durationInMillis);
        buildSnapshotBO.setId(jobId);
        if (!StatusEnum.STOP.getCode().toString().equals(buildSnapshotBO.getBuildStatus())) {
            if (SUCCESS.equals(result)) {
                buildSnapshotBO.setBuildStatus(StatusEnum.SUCCESS.getCode().toString());
            } else if (FAILURE.equals(result)) {
                buildSnapshotBO.setBuildStatus(StatusEnum.FAILED.getCode().toString());
            } else if (ABORTED.equals(result)) {
                buildSnapshotBO.setBuildStatus(StatusEnum.PAUSED.getCode().toString());
            }
        }
        this.saveArtifact(jobId);
        // 保存步骤日志
        this.saveStepLog(buildSnapshotBO);
        // 后续保存其他的内容
        this.updateBuildSnapshot(buildSnapshotBO);
        // 更新主表信息
        String taskId = buildSnapshotBO.getTaskId();
        BuildTaskDO buildTaskDO = buildTaskAtomService.getById(taskId);
        buildTaskDO.setBuildStatus(buildSnapshotBO.getBuildStatus());
        buildTaskAtomService.updateById(buildTaskDO);
        //保存构建快照记录与项目关联关系
        this.saveProjectSnapshot(buildSnapshotBO);
        this.collectDataAfter(jobId);
    }

    private void saveProjectSnapshot(BuildSnapshotBO buildSnapshotBO) {
        LambdaQueryWrapper<BuildProjectRelationDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BuildProjectRelationDO::getTaskId, buildSnapshotBO.getTaskId());
        List<BuildProjectRelationDO> list = buildProjectRelationAtomService.list(queryWrapper);
        if (EmptyValidator.isNotEmpty(list)) {
            for (BuildProjectRelationDO buildProjectRelationDO : list) {
                BuildProjectSnapshotDO buildProjectSnapshotDO = new BuildProjectSnapshotDO();
                buildProjectSnapshotDO.setTaskId(buildSnapshotBO.getTaskId());
                buildProjectSnapshotDO.setBuildSnapshotId(buildSnapshotBO.getId());
                buildProjectSnapshotDO.setProjectCode(buildProjectRelationDO.getProjectCode());
                buildProjectSnapshotDO.setTenantId(buildSnapshotBO.getTenantId());
                buildProjectSnapshotDO.setCreateUid(buildSnapshotBO.getCreateUid());
                buildProjectSnapshotAtomService.save(buildProjectSnapshotDO);
            }
        }
    }

    private void collectDataAfter(String jobId) {
        new Thread(() -> {
            try {
                this.getMetadata(jobId);
                Thread.sleep(10000);
//            ciScheduleBizService.deleteTask(jobId);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            // 将队列种的任务继续执行
            BuildSnapshotBO buildSnapshotBO = this.getBuildSnapshotById(jobId);
            PageRequest pageRequest = new PageRequest();
            pageRequest.setPageNo(1);
            pageRequest.setPageSize(50);
            BuildSnapshotBO queryBuildSnapshotBO = new BuildSnapshotBO();
            queryBuildSnapshotBO.setBuildStatus(StatusEnum.QUEUED.getCode().toString());
            queryBuildSnapshotBO.setTaskId(buildSnapshotBO.getTaskId());
            PageResponse<List<BuildSnapshotBO>> list = this.list(pageRequest, queryBuildSnapshotBO);
            if (list.getCount() > 0) {
                BuildSnapshotBO queueBuildSnapshotBO = list.getData().get(list.getData().size() - 1);
                BuildScheduleSnapshotBO buildScheduleSnapshotBO = buildScheduleSnapshotBizService.getDetailById(queueBuildSnapshotBO.getId());
                CiScheduleRequest ciScheduleR = new CiScheduleRequest();
                ciScheduleR.setBuildTaskDTO(buildScheduleSnapshotBO);
                queueBuildSnapshotBO.setBuildStatus(StatusEnum.RUNNING.getCode().toString());
                this.updateBuildSnapshot(queueBuildSnapshotBO);
                ciScheduleBizService.startTask(ciScheduleR);
                dynamicTaskManager.addJob(queueBuildSnapshotBO.getId());
            }
        }).start();
        // 删除构建任务
    }

    @Override
    public BuildAnalysisBO analysis(String startTime, String endTime, String taskId) {
        LambdaQueryWrapper<BuildSnapshotDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BuildSnapshotDO::getTaskId, taskId);
        if (EmptyValidator.isNotEmpty(startTime) && EmptyValidator.isNotEmpty(endTime)) {
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime beginTime = LocalDateTime.parse(startTime, dateTimeFormatter);
            LocalDateTime stopTime = LocalDateTime.parse(endTime, dateTimeFormatter);
            queryWrapper.ge(BuildSnapshotDO::getCreateTime, beginTime);
            queryWrapper.le(BuildSnapshotDO::getCreateTime, stopTime);
        }
        queryWrapper.orderByAsc(BuildSnapshotDO::getCreateTime);
        BuildAnalysisBO buildAnalysisBO = new BuildAnalysisBO();
        List<BuildSnapshotDO> list = buildSnapshotAtomService.list(queryWrapper);
        if (EmptyValidator.isEmpty(list)) {
            return new BuildAnalysisBO().setAverageBuildTime(0.0).setLongestBuildTime(0.0).setShortestBuildTime(0.0)
                    .setBuildSuccessRate(0.0).setSnapshotBuildTimes(Collections.emptyList())
                    .setDailyBuildRates(Collections.emptyList());
        }
        // 根据创建时间分组（按天）
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
        Map<String, List<BuildSnapshotDO>> groupedByDate = list.stream()
                .collect(Collectors.groupingBy(snapshot -> dateTimeFormatter.format(snapshot.getCreateTime())));
        // 获取每天成功构建的次数
        Map<String, Long> successCountByDate = groupedByDate.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().stream()
                                .filter(snapshot -> StatusEnum.SUCCESS.getCode().toString().equals(snapshot.getBuildStatus()))
                                .count()
                ));
        List<BuildAnalysisBO.DayBuildRate> dayBuildRates = new ArrayList<>();
        groupedByDate.forEach((date, snapshots) -> {
            long totalCount = snapshots.size();
            long successCount = successCountByDate.getOrDefault(date, 0L);
            double buildSuccessRate = Math.round((successCount / (double) totalCount) * 1000.0) / 10.0;
            dayBuildRates.add(new BuildAnalysisBO.DayBuildRate(date, buildSuccessRate));
        });
        // 构建返回结果
        long totalSuccessCount = dayBuildRates.stream().filter(each -> each.getBuildSuccessRate() == 100.0).count();
        double totalSuccessRate = Math.round((totalSuccessCount / (double) dayBuildRates.size()) * 1000.0) / 10.0;
        // dayBuildRates按照时间排序
        dayBuildRates.sort(Comparator.comparing(BuildAnalysisBO.DayBuildRate::getDate));
        buildAnalysisBO.setBuildSuccessRate(totalSuccessRate);
        buildAnalysisBO.setDailyBuildRates(dayBuildRates);
        // 统计每个快照构建时长
        List<BuildSnapshotDO> successList = list.stream().filter(snapshot -> StatusEnum.SUCCESS.getCode().toString().equals(snapshot.getBuildStatus())).toList();
        List<BuildAnalysisBO.SnapshotBuildTime> snapshotBuildTimes = new ArrayList<>(successList.stream()
                .map(snapshot -> new BuildAnalysisBO.SnapshotBuildTime(snapshot.getBuildNumber(), (double) (snapshot.getDuration() / 1000)))
                .toList());

        // 如果没有成功的构建，设置默认值
        if (snapshotBuildTimes.isEmpty()) {
            buildAnalysisBO.setShortestBuildTime(0.0);
            buildAnalysisBO.setLongestBuildTime(0.0);
            buildAnalysisBO.setAverageBuildTime(0.0);
            buildAnalysisBO.setSnapshotBuildTimes(Collections.emptyList());
            return buildAnalysisBO;
        }

        // 根据构建时长排序
        snapshotBuildTimes.sort(new Comparator<BuildAnalysisBO.SnapshotBuildTime>() {
            @Override
            public int compare(BuildAnalysisBO.SnapshotBuildTime o1, BuildAnalysisBO.SnapshotBuildTime o2) {
                return Double.compare(o1.getBuildTime(), o2.getBuildTime());
            }
        });
        double shortestBuildTime = snapshotBuildTimes.get(0).getBuildTime();
        double longestBuildTime = snapshotBuildTimes.get(snapshotBuildTimes.size() - 1).getBuildTime();
        double totalBuildTime = 0.0;
        // 获取总构建时间
        for (BuildAnalysisBO.SnapshotBuildTime snapshotBuildTime : snapshotBuildTimes) {
            totalBuildTime += snapshotBuildTime.getBuildTime();
        }
        double averageBuildTime = Math.floor(totalBuildTime / snapshotBuildTimes.size());
        buildAnalysisBO.setShortestBuildTime(shortestBuildTime);
        buildAnalysisBO.setLongestBuildTime(longestBuildTime);
        buildAnalysisBO.setAverageBuildTime(averageBuildTime);
        // 根据构建编号排序
        snapshotBuildTimes.sort(Comparator.comparing(BuildAnalysisBO.SnapshotBuildTime::getBuildNumber));
        buildAnalysisBO.setSnapshotBuildTimes(snapshotBuildTimes);
        return buildAnalysisBO;
    }

    private void saveStepLog(BuildSnapshotBO buildSnapshotBO) {
        log.info("保存构建步骤基本信息与日志");

        BuildStepSnapshotBO buildStepSnapshotQuery = new BuildStepSnapshotBO();
        buildStepSnapshotQuery.setBuildSnapshotId(buildSnapshotBO.getId());
        List<BuildStepSnapshotBO> buildStepSnapshotBOList = buildStepSnapshotBizService.list(buildStepSnapshotQuery);
        // 收集构建日志信息到minio中
        // 建立MINIO 链接
        JSONArray stagesLog = ciScheduleBizService.queryLog(buildSnapshotBO.getId());
        for (int i = 0; i < stagesLog.size(); i++) {
            JSONObject stageLog = stagesLog.getJSONObject(i);
            String log = stageLog.getString("stageLog");
            String stageNum = stageLog.getString("id");
            String filePath = buildSnapshotBO.getTaskId() + "/" + buildSnapshotBO.getId() + "/logs-cache/" + stageNum + "/log.txt";
            BuildStepSnapshotBO buildStepSnapshotBO = buildStepSnapshotBOList.get(i);
            if ("FINISHED".equals(stageLog.getString("state"))) {
                try {
                    ossClient.save(log.getBytes(StandardCharsets.UTF_8), filePath, null);
                    buildStepSnapshotBO.setStepLogPath(filePath);
                    buildStepSnapshotBO.setDuration(stageLog.getInteger("durationInMillis"));
                    buildStepSnapshotBO.setStartTime(stageLog.getLocalDateTime("startTime"));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            buildStepSnapshotBO.setStepState(stageLog.getString("state"));
            buildStepSnapshotBO.setStepResult(stageLog.getString("result"));
            buildStepSnapshotBizService.updateBuildStepSnapshot(buildStepSnapshotBO);
        }
    }

    private void saveArtifact(String jobId) {
        BuildCodeSnapshotBO buildCodeSnapshotBO = buildCodeSnapshotBizService.getBuildCodeSnapshotBySnapshotId(jobId);
        String buildVersion = buildCodeSnapshotBO.getBuildSnapshotId();
        JSONObject artifact = ciScheduleBizService.getArtifact(jobId);
        JSONArray array = artifact.getJSONArray("artifacts");
        log.info("保存制品数据到制品快照表当中");
        log.info("artifact:{}", artifact);
        for (int i = 0; i < array.size(); i++) {
            BuildArtifactBO bo = JSONObject.parseObject(array.getString(i), BuildArtifactBO.class);
            bo.setBuildVersion(buildVersion);
            buildArtifactBizService.createBuildArtifact(bo);
        }
    }

    @Override
    public List<Metadata> getMetadataList(String jobId) {
        List<Metadata> metadataList = new ArrayList<>();
        BuildSnapshotBO buildSnapshotBO = this.getBuildSnapshotById(jobId);
        BuildTaskDO buildTaskDO = buildTaskAtomService.getById(buildSnapshotBO.getTaskId());
        BuildArtifactBO BuildArtifactBO = new BuildArtifactBO();
        BuildArtifactBO.setBuildSnapshotId(jobId);
        List<BuildArtifactBO> list = buildArtifactBizService.list(BuildArtifactBO);
        if (EmptyValidator.isNotEmpty(list)) {
            // 查询代码快照信息
            BuildCodeSnapshotBO buildCodeSnapshotBO = buildCodeSnapshotBizService.getBuildCodeSnapshotBySnapshotId(jobId);
            JSONObject codeMetadata = sWorkerOpenBizService.getCodeMetadata(buildCodeSnapshotBO.getTenantId(), buildCodeSnapshotBO.getVcsId());
            String systemCode = codeMetadata.getString("systemCode");
            JSONArray apps = codeMetadata.getJSONArray("apps");
            String appName = "";
            String applicationCode = "";
            if (EmptyValidator.isNotEmpty(apps)) {
                appName = apps.getJSONObject(0).getString("appName");
                applicationCode = apps.getJSONObject(0).getString("applicationCode");
            }
            if (EmptyValidator.isEmpty(appName)) {
                appName = applicationCode;
            }
            String spaceCode = buildSnapshotBO.getSpaceId();
            // 构建数据
            MetadataPipeline metadataPipeline = new MetadataPipeline();
            metadataPipeline.setIntegrationCode(buildTaskDO.getId());
            metadataPipeline.setIntegrationName(buildTaskDO.getTaskName());
            metadataPipeline.setStructureUserAccount(buildSnapshotBO.getCreateUid());
            metadataPipeline.setApplicationCode(applicationCode);
            metadataPipeline.setApplicationName(appName);
            // 代码数据
            List<MetadataRepository> metadataRepositories = new ArrayList<>();
            MetadataRepository metadataRepository = new MetadataRepository();
            metadataRepository.setRepositoryBranch(buildCodeSnapshotBO.getVcsBranch());
            metadataRepository.setRepositoryCommit(buildCodeSnapshotBO.getCommitId());
            metadataRepository.setRepositoryName(buildCodeSnapshotBO.getVcsName());
            metadataRepository.setRepositoryUrl(buildCodeSnapshotBO.getVcsRepository());
            metadataRepositories.add(metadataRepository);
            list.forEach(buildArtifactBO -> {
                Metadata metadata = new Metadata();
                // 制品数据
                MetadataBasis metadataBasis = new MetadataBasis();
                String artiPath = buildArtifactBO.getBuildArtifactUrl();
//                if ("1".equals(buildArtifactBO.getBuildArtifactType())) {
//                    String downloadUrl = artiPath;
//                    String[] parts = downloadUrl.split("/");
//                    for (int i = 0; i < parts.length; i++) {
//                        if (parts[i].equals("artifactory") && i + 1 < parts.length) {
//                            parts[i + 1] += "_generic";
//                            break;
//                        }
//                    }
//                    artiPath = String.join("/", parts);
//                }
//                System.out.println(artiPath);
                metadataBasis.setArtiPath(artiPath);
                metadataBasis.setArtiType(Integer.parseInt(buildArtifactBO.getBuildArtifactType()));
                metadataBasis.setSpaceCode(spaceCode);
                metadataBasis.setSystemCode(systemCode);
                metadataBasis.setTenantId(buildCodeSnapshotBO.getTenantId());
                metadata.setMetadataBasis(metadataBasis);
                metadata.setMetadataPipeline(metadataPipeline);
                metadata.setMetadataRepository(metadataRepositories);
                metadata.setMetadataReport(new MetadataReport());
                log.info("metadata:{}", JSONObject.toJSONString(metadata));
                metadataList.add(metadata);
            });
        }
        return metadataList;
    }

    private void getMetadata(String jobId) {
        // 查询制品信息
        BuildSnapshotBO buildSnapshotBO = this.getBuildSnapshotById(jobId);
        if (!"PIPELINE".equals(buildSnapshotBO.getSource())) {

            List<Metadata> metadataList = this.getMetadataList(jobId);
            this.sendMessage(metadataList);
        }
    }

    @Override
    public void sendMessage(List<Metadata> metadataList) {
        metadataList.forEach(sWorkerOpenBizService::addMetadata);
    }
}
