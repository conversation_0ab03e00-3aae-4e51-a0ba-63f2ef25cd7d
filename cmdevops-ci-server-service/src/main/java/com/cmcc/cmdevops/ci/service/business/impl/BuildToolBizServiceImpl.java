package com.cmcc.cmdevops.ci.service.business.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.atom.BuildToolAtomService;
import com.cmcc.cmdevops.ci.service.bo.BuildToolBO;
import com.cmcc.cmdevops.ci.service.business.BuildToolBizService;
import com.cmcc.cmdevops.ci.service.dao.BuildToolDO;
import com.cmcc.cmdevops.util.BeanCloner;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 构建工具业务实现类
 */
@Service
public class BuildToolBizServiceImpl implements BuildToolBizService {

    private final BuildToolAtomService buildToolAtomService;

    public BuildToolBizServiceImpl(BuildToolAtomService buildToolAtomService) {
        this.buildToolAtomService = buildToolAtomService;
    }

    @Override
    public void createBuildTool(BuildToolBO buildToolBO) {
        BuildToolDO buildToolDO = BeanCloner.clone(buildToolBO, BuildToolDO.class);
        buildToolAtomService.save(buildToolDO);
    }

    @Override
    public void updateBuildTool(BuildToolBO buildToolBO) {
        BuildToolDO buildToolDO = BeanCloner.clone(buildToolBO, BuildToolDO.class);

        UpdateWrapper<BuildToolDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", buildToolBO.getId());

        buildToolAtomService.update(buildToolDO, updateWrapper);
    }

    @Override
    public void deleteBuildTool(BuildToolBO buildToolBO) {
        buildToolAtomService.removeById(buildToolBO.getId());
    }

    @Override
    public BuildToolBO getBuildToolById(String id) {
        BuildToolDO buildToolDO = buildToolAtomService.getById(id);
        return (buildToolDO != null) ? BeanCloner.clone(buildToolDO, BuildToolBO.class) : null;
    }

    @Override
    public PageResponse<List<BuildToolBO>> pageBuildTools(PageRequest pageRequest) {
        Page<BuildToolDO> page = new Page<>(pageRequest.getPageNo(), pageRequest.getPageSize());
        Page<BuildToolDO> result = buildToolAtomService.page(page);
        List<BuildToolBO> list = BeanCloner.clone(result.getRecords(), BuildToolBO.class);
        return PageResponse.success(list, result.getTotal(), pageRequest.getPageNo(), pageRequest.getPageSize());
    }

    public List<BuildToolBO> list(BuildToolBO buildToolBO) {
        LambdaQueryWrapper<BuildToolDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByAsc(BuildToolDO::getId);
        List<BuildToolDO> buildToolDOList = buildToolAtomService.list();
        return BeanCloner.clone(buildToolDOList, BuildToolBO.class);
    }
}
