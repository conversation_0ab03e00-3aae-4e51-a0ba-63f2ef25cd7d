package com.cmcc.cmdevops.ci.service.business;

import com.alibaba.fastjson2.JSONObject;
import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.bo.BuildAnalysisBO;
import com.cmcc.cmdevops.ci.service.bo.BuildSnapshotBO;
import com.cmcc.cmdevops.ci.service.bo.Metadata;

import java.util.List;

/**
 * 构建快照业务接口
 */
public interface BuildSnapshotBizService {
    /**
     * 创建构建快照
     *
     * @param buildSnapshotBO 构建快照数据对象
     */
    void createBuildSnapshot(BuildSnapshotBO buildSnapshotBO);

    /**
     * 更新构建快照
     *
     * @param buildSnapshotBO 构建快照数据对象
     */
    void updateBuildSnapshot(BuildSnapshotBO buildSnapshotBO);

    /**
     * 删除构建快照
     *
     * @param buildSnapshotBO 构建快照数据对象
     */
    void deleteBuildSnapshot(BuildSnapshotBO buildSnapshotBO);

    /**
     * 根据ID获取构建快照
     *
     * @param id 构建快照ID
     * @return 构建快照数据对象
     */
    BuildSnapshotBO getBuildSnapshotById(String id);

    PageResponse<List<BuildSnapshotBO>> list(PageRequest pageRequest, BuildSnapshotBO buildSnapshotBO);

    void addJob(String jobId);

    void getCollectData(String jobId, JSONObject taskStatus);

    BuildAnalysisBO analysis(String startTime, String endTime, String taskId);

    List<Metadata> getMetadataList(String jobId);
    void sendMessage(List<Metadata> metadataList);


}
