package com.cmcc.cmdevops.ci.service.business;

import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.bo.BuildCodeSnapshotBO;
import java.util.List;

/**
 * 构建代码快照业务接口
 */
public interface BuildCodeSnapshotBizService {
    /**
     * 创建构建代码快照
     * @param buildCodeSnapshotBO 构建代码快照数据对象
     */
    void createBuildCodeSnapshot(BuildCodeSnapshotBO buildCodeSnapshotBO);

    /**
     * 更新构建代码快照
     * @param buildCodeSnapshotBO 构建代码快照数据对象
     */
    void updateBuildCodeSnapshot(BuildCodeSnapshotBO buildCodeSnapshotBO);

    /**
     * 删除构建代码快照
     * @param buildCodeSnapshotBO 构建代码快照数据对象
     */
    void deleteBuildCodeSnapshot(BuildCodeSnapshotBO buildCodeSnapshotBO);

    /**
     * 根据ID获取构建代码快照
     * @param id 构建代码快照ID
     * @return 构建代码快照数据对象
     */
    BuildCodeSnapshotBO getBuildCodeSnapshotById(String id);

    BuildCodeSnapshotBO getBuildCodeSnapshotBySnapshotId(String snapshotId);

    List<BuildCodeSnapshotBO> getBuildCodeSnapshotBySnapshotIds(List<String> snapshotIds);

    List<BuildCodeSnapshotBO> getBuildCodeSnapshotByTaskId(String taskId);
    /**
     * 分页查询构建代码快照列表
     * @param pageRequest 分页请求参数
     * @return 分页响应结果
     */
    PageResponse<List<BuildCodeSnapshotBO>> pageBuildCodeSnapshots(PageRequest pageRequest);
}
