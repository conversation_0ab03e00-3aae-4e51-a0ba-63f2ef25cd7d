package com.cmcc.cmdevops.ci.service.business;

import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.bo.BuildCodeConfigBO;
import com.cmcc.cmdevops.ci.service.bo.BuildScriptBO;
import com.cmcc.cmdevops.ci.service.bo.BuildStepConfigBO;

import java.util.List;

/**
 * 构建脚本业务接口
 */
public interface BuildScriptBizService {
    /**
     * 创建构建脚本
     *
     * @param buildScriptBO 构建脚本数据对象
     */
    void createBuildScript(BuildScriptBO buildScriptBO);

    /**
     * 更新构建脚本
     *
     * @param buildScriptBO 构建脚本数据对象
     */
    void updateBuildScript(BuildScriptBO buildScriptBO);

    /**
     * 删除构建脚本
     *
     * @param buildScriptBO 构建脚本数据对象
     */
    void deleteBuildScript(BuildScriptBO buildScriptBO);

    /**
     * 根据ID获取构建脚本
     *
     * @param id 构建脚本ID
     * @return 构建脚本数据对象
     */
    BuildScriptBO getBuildScriptById(String id);

    /**
     * 分页查询构建脚本列表
     *
     * @param pageRequest 分页请求参数
     * @return 分页响应结果
     */
    PageResponse<List<BuildScriptBO>> pageBuildScripts(PageRequest pageRequest);

    List<BuildStepConfigBO> getBuildStepsByScript(BuildScriptBO buildScript);

    BuildScriptBO getBuildScriptByTaskId(String taskId);
}
