package com.cmcc.cmdevops.ci.service.business.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.atom.BuildGroupAtomService;
import com.cmcc.cmdevops.ci.service.atom.BuildGroupRelationAtomService;
import com.cmcc.cmdevops.ci.service.bo.BuildGroupBO;
import com.cmcc.cmdevops.ci.service.business.BuildGroupBizService;
import com.cmcc.cmdevops.ci.service.business.util.EmptyValidator;
import com.cmcc.cmdevops.ci.service.business.util.UserUtils;
import com.cmcc.cmdevops.ci.service.dao.BuildGroupDO;
import com.cmcc.cmdevops.ci.service.dao.BuildGroupRelationDO;
import com.cmcc.cmdevops.exception.BusinessException;
import com.cmcc.cmdevops.util.BeanCloner;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>Title: BuildGroupBizServiceImpl</p>
 * <p>Description: 分组功能 - 业务实现类</p>
 * <p>Copyright: Copyright (c) 2021</p>
 * <p>Company: SI-TECH </p>
 * Author by_csd_hlj
 * Version 1.0
 * CreateTime 2025/5/29 16:44
 */
@Slf4j
@Service
public class BuildGroupBizServiceImpl implements BuildGroupBizService {

    @Resource
    private BuildGroupAtomService buildGroupAtomService;
    @Resource
    private BuildGroupRelationAtomService buildGroupRelationAtomService;

    @Override
    public PageResponse<List<BuildGroupBO>> pageBuildGroups(BuildGroupBO buildGroupBO) {
        Page<BuildGroupDO> page = new Page<>(buildGroupBO.getPageNo(), buildGroupBO.getPageSize());
        LambdaQueryWrapper<BuildGroupDO> queryWrapper = new LambdaQueryWrapper<>();
        if (EmptyValidator.isNotEmpty(buildGroupBO.getGroupName())) {
            queryWrapper.like(BuildGroupDO::getGroupName, buildGroupBO.getGroupName());
        }
        if (EmptyValidator.isNotEmpty(buildGroupBO.getSpaceId())) {
            queryWrapper.eq(BuildGroupDO::getSpaceId, buildGroupBO.getSpaceId());
        }
        queryWrapper.orderByDesc(BuildGroupDO::getCreateTime);
        Page<BuildGroupDO> result = buildGroupAtomService.page(page, queryWrapper);
        List<BuildGroupBO> list = BeanCloner.clone(result.getRecords(), BuildGroupBO.class);
        return PageResponse.success(list, result.getTotal(), buildGroupBO.getPageNo(), buildGroupBO.getPageSize());
    }

    @Override
    public List<BuildGroupBO> listBuildGroups(BuildGroupBO buildGroupBO) {
        LambdaQueryWrapper<BuildGroupDO> queryWrapper = new LambdaQueryWrapper<>();
        if (EmptyValidator.isNotEmpty(buildGroupBO.getGroupName())) {
            queryWrapper.like(BuildGroupDO::getGroupName, buildGroupBO.getGroupName());
        }
        if (EmptyValidator.isNotEmpty(buildGroupBO.getSpaceId())) {
            queryWrapper.eq(BuildGroupDO::getSpaceId, buildGroupBO.getSpaceId());
        }
        queryWrapper.orderByDesc(BuildGroupDO::getCreateTime);
        List<BuildGroupDO> list = buildGroupAtomService.list(queryWrapper);

        return BeanCloner.clone(list, BuildGroupBO.class);
    }

    @Override
    public BuildGroupBO getGroup(Integer groupId) {

        BuildGroupDO groupDO = buildGroupAtomService.getById(groupId);

        BuildGroupBO groupBO = BeanCloner.clone(groupDO, BuildGroupBO.class);

        return groupBO;
    }

    @Override
    public String saveGroup(BuildGroupBO buildGroupBO) {
        checkName(buildGroupBO);
        // id为空，功能为新增
        if (EmptyValidator.isEmpty(buildGroupBO.getId())) {
            buildGroupBO.setGroupSign("normal");
            buildGroupBO.setTenantId(UserUtils.getTenantId());
            buildGroupBO.setCreateUid(UserUtils.getUserId());
            BuildGroupDO groupDO = BeanCloner.clone(buildGroupBO, BuildGroupDO.class);
            buildGroupAtomService.save(groupDO);
            return String.valueOf(groupDO.getId());
        } else { // id 非空，功能为修改
            buildGroupBO.setUpdateUid(UserUtils.getUserId());
            BuildGroupDO groupDO = BeanCloner.clone(buildGroupBO, BuildGroupDO.class);
            buildGroupAtomService.updateById(groupDO);
            return String.valueOf(groupDO.getId());
        }
    }

    @Override
    public String deleteGroup(Integer groupId) {

        buildGroupAtomService.removeById(groupId);

        // 删除分组关联
        QueryWrapper<BuildGroupRelationDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("group_id", groupId);
        buildGroupRelationAtomService.remove(queryWrapper);
        return "0";
    }

    @Override
    public void batchGroupRelation(BuildGroupBO buildGroupBO) {

        List<String> businessDataId = buildGroupBO.getBusinessDataId();

        businessDataId.stream().forEach(b -> {

            if ("add".equals(buildGroupBO.getBatchRelationType())) {
                boolean status = checkRecord(buildGroupBO.getId(), b);

                if (!status) {
                    BuildGroupRelationDO relationDO = new BuildGroupRelationDO();
                    relationDO.setGroupId(buildGroupBO.getId());
                    relationDO.setBusinessDataId(b);
                    relationDO.setTenantId(UserUtils.getTenantId());
                    relationDO.setCreateUid(UserUtils.getUserId());
                    buildGroupRelationAtomService.save(relationDO);
                }
            }

            if ("sub".equals(buildGroupBO.getBatchRelationType())) {
                QueryWrapper<BuildGroupRelationDO> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("group_id", buildGroupBO.getId());
                queryWrapper.eq("business_data_id", b);
                buildGroupRelationAtomService.remove(queryWrapper);
            }
        });
    }

    private void checkName(BuildGroupBO buildGroupBO) {
        LambdaQueryWrapper<BuildGroupDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BuildGroupDO::getGroupName, buildGroupBO.getGroupName());
        queryWrapper.eq(BuildGroupDO::getSpaceId, buildGroupBO.getSpaceId());
        if (EmptyValidator.isNotEmpty(buildGroupBO.getId())) {
            queryWrapper.ne(BuildGroupDO::getId, buildGroupBO.getId());
        }
        long count = buildGroupAtomService.count(queryWrapper);
        if(count > 0){
            throw new BusinessException("当前分组名称已存在");
        }
    }

    private boolean checkRecord(Integer groupId, String businessDataId) {
        QueryWrapper<BuildGroupRelationDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("group_id", groupId);
        queryWrapper.eq("business_data_id", businessDataId);
        List<BuildGroupRelationDO> list = buildGroupRelationAtomService.list(queryWrapper);
        return !list.isEmpty();
    }

}
