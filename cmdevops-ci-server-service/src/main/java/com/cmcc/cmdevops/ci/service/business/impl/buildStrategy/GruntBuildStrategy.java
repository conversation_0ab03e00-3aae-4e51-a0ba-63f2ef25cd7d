package com.cmcc.cmdevops.ci.service.business.impl.buildStrategy;

import com.cmcc.cmdevops.ci.service.bo.BuildSnapshotBO;
import com.cmcc.cmdevops.ci.service.bo.BuildStepConfigBO;
import com.cmcc.cmdevops.ci.service.bo.BuildStepsGruntBO;
import com.cmcc.cmdevops.ci.service.bo.BuildToolBO;
import com.cmcc.cmdevops.ci.service.business.BuildStrategy;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * Created by 51746 on 2021/2/25.
 */
@Service
public class GruntBuildStrategy implements BuildStrategy {

    @Override
    public String stageShell(BuildSnapshotBO buildSnapshotBO, BuildStepConfigBO buildStepsBO, List<BuildToolBO> buildToolBOList) {
        StringBuilder buildShell = new StringBuilder();
        BuildStepsGruntBO cipBuildStepsGruntDTO = buildStepsBO.getConfig().toJavaObject(BuildStepsGruntBO.class);
        BuildToolBO buildToolBO = null;
        Optional<BuildToolBO> toolBOOptional = buildToolBOList.stream()
                .filter(buildTool -> buildTool.getToolName().equals(cipBuildStepsGruntDTO.getToolVersion()))
                .findAny();
        if (toolBOOptional.isPresent()) {
            buildToolBO = toolBOOptional.get();
        }
        String nodeName = cipBuildStepsGruntDTO.getToolVersion().replace("node", "");
        String nvmCommand = "nvm alias default " + nodeName;
        buildShell.append("stage('").append(buildStepsBO.getName()).append("-").append(buildStepsBO.getSerial()).append("') { \n")
                .append(JenkinsUtils.getAgent(buildToolBO))
                .append("steps { \n")
                .append("  strictShNoOut('").append(nvmCommand).append("')\n")
                .append("sh '''").append(JenkinsUtils.commandReplace(cipBuildStepsGruntDTO.getCommand())).append("'''\n")
                .append("} \n")
                .append("} \n");
        return buildShell.toString();
    }
}
