package com.cmcc.cmdevops.ci.service.business;

import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.bo.BuildJenkinsNodeBO;

import java.util.List;

/**
 * Jenkins节点业务接口
 */
public interface BuildJenkinsNodeBizService {
    /**
     * 创建Jenkins节点配置
     *
     * @param buildJenkinsNodeBO Jenkins节点数据对象
     */
    void createBuildJenkinsNode(BuildJenkinsNodeBO buildJenkinsNodeBO);

    /**
     * 更新Jenkins节点配置
     *
     * @param buildJenkinsNodeBO Jenkins节点数据对象
     */
    void updateBuildJenkinsNode(BuildJenkinsNodeBO buildJenkinsNodeBO);

    /**
     * 删除Jenkins节点配置
     *
     * @param buildJenkinsNodeBO Jenkins节点数据对象
     */
    void deleteBuildJenkinsNode(BuildJenkinsNodeBO buildJenkinsNodeBO);

    /**
     * 根据ID获取Jenkins节点配置
     *
     * @param id Jenkins节点ID
     * @return Jenkins节点数据对象
     */
    BuildJenkinsNodeBO getBuildJenkinsNodeById(String id);

    List<BuildJenkinsNodeBO> list(BuildJenkinsNodeBO pageRequest);

    /**
     * 分页查询Jenkins节点列表
     *
     * @param pageRequest 分页请求参数
     * @return 分页响应结果
     */
    PageResponse<List<BuildJenkinsNodeBO>> pageBuildJenkinsNodes(PageRequest pageRequest);
}
