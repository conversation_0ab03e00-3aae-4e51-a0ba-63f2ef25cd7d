package com.cmcc.cmdevops.ci.service.business.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.atom.BuildVersionAtomService;
import com.cmcc.cmdevops.ci.service.bo.BuildVersionBO;
import com.cmcc.cmdevops.ci.service.business.BuildVersionBizService;
import com.cmcc.cmdevops.ci.service.business.util.EmptyValidator;
import com.cmcc.cmdevops.ci.service.dao.BuildTaskDO;
import com.cmcc.cmdevops.ci.service.dao.BuildVersionDO;
import com.cmcc.cmdevops.util.BeanCloner;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 构建版本业务实现类
 */
@Service
public class BuildVersionBizServiceImpl implements BuildVersionBizService {

    private final BuildVersionAtomService buildVersionAtomService;

    public BuildVersionBizServiceImpl(BuildVersionAtomService buildVersionAtomService) {
        this.buildVersionAtomService = buildVersionAtomService;
    }

    @Override
    public void createBuildVersion(BuildVersionBO buildVersionBO) {
        BuildVersionDO buildVersionDO = BeanCloner.clone(buildVersionBO, BuildVersionDO.class);
        buildVersionAtomService.save(buildVersionDO);
    }

    @Override
    public void updateBuildVersion(BuildVersionBO buildVersionBO) {
        BuildVersionDO buildVersionDO = BeanCloner.clone(buildVersionBO, BuildVersionDO.class);

        UpdateWrapper<BuildVersionDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", buildVersionBO.getId());

        buildVersionAtomService.update(buildVersionDO, updateWrapper);
    }

    @Override
    public void deleteBuildVersion(BuildVersionBO buildVersionBO) {
        buildVersionAtomService.removeById(buildVersionBO.getId());
    }

    @Override
    public BuildVersionBO getBuildVersionById(String id) {
        BuildVersionDO buildVersionDO = buildVersionAtomService.getById(id);
        return (buildVersionDO != null) ? BeanCloner.clone(buildVersionDO, BuildVersionBO.class) : null;
    }

    @Override
    public PageResponse<List<BuildVersionBO>> pageBuildVersions(PageRequest pageRequest, BuildVersionBO buildVersionBO) {
        Page<BuildVersionDO> page = new Page<>(pageRequest.getPageNo(), pageRequest.getPageSize());
        LambdaQueryWrapper<BuildVersionDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BuildVersionDO::getTaskId, buildVersionBO.getTaskId());
        if (EmptyValidator.isNotEmpty(buildVersionBO.getStartTime()) && EmptyValidator.isNotEmpty(buildVersionBO.getEndTime())) {
            queryWrapper.between(BuildVersionDO::getCreateTime, buildVersionBO.getStartTime(), buildVersionBO.getEndTime());
        }
        if (EmptyValidator.isNotEmpty(buildVersionBO.getCreateUid())) {
            queryWrapper.eq(BuildVersionDO::getCreateUid, buildVersionBO.getCreateUid());
        }
        Page<BuildVersionDO> result = buildVersionAtomService.page(page, queryWrapper);
        List<BuildVersionBO> list = BeanCloner.clone(result.getRecords(), BuildVersionBO.class);
        return PageResponse.success(list, result.getTotal(), pageRequest.getPageNo(), pageRequest.getPageSize());
    }
}
