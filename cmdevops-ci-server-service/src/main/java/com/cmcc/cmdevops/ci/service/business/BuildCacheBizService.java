package com.cmcc.cmdevops.ci.service.business;

import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.bo.BuildCacheBO;

import java.util.List;

/**
 * 构建缓存业务接口
 */
public interface BuildCacheBizService {
    /**
     * 创建构建缓存配置
     *
     * @param buildCacheBO 构建缓存数据对象
     */
    void createBuildCache(BuildCacheBO buildCacheBO);

    /**
     * 更新构建缓存配置
     *
     * @param buildCacheBO 构建缓存数据对象
     */
    void updateBuildCache(BuildCacheBO buildCacheBO);

    /**
     * 删除构建缓存配置
     *
     * @param cacheId 构建缓存数据对象Id
     */
    void deleteBuildCache(Integer cacheId);

    /**
     * 根据ID获取构建缓存配置
     *
     * @param id 构建缓存ID
     * @return 构建缓存数据对象
     */
    BuildCacheBO getBuildCacheById(Integer id);

    List<BuildCacheBO> getListByTaskId(String taskId);

    String cleanBuildCache(String id);

    /**
     * 分页查询构建缓存列表
     *
     * @param pageRequest 分页请求参数
     * @return 分页响应结果
     */
    PageResponse<List<BuildCacheBO>> pageBuildCaches(PageRequest pageRequest);

    void deleteByTaskIds(List<String> taskIds);
}
