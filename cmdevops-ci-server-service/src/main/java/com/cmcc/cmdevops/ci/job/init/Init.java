package com.cmcc.cmdevops.ci.job.init;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.PostConstruct;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import tech.powerjob.client.PowerJobClient;
import tech.powerjob.common.enums.ExecuteType;
import tech.powerjob.common.enums.ProcessorType;
import tech.powerjob.common.enums.TimeExpressionType;
import tech.powerjob.common.request.http.SaveJobInfoRequest;
import tech.powerjob.common.response.ResultDTO;

import java.util.HashSet;
import java.util.Set;

@Component
@ConditionalOnProperty(name = "powerjob.worker.enabled", havingValue = "true")
public class Init {

    private final Log log = LogFactory.getLog(getClass());

    @Value("${powerjob.worker.app-name}")
    private String appName;

    @Value("${middleware.powerjob.host}")
    private String serverAddress;

    @Value("${powerjob.worker.password}")
    private String password;

    @PostConstruct
    public void init(){
        if (StringUtils.isNotEmpty(serverAddress) && serverAddress.contains("http://")){
            serverAddress = serverAddress.replace("http://","");
        }
        //查询appName是否存在
        String url = "http://" + serverAddress + "/appInfo/list";
        log.info("查询appName是否存在-------------url为：" + url);

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet request = new HttpGet(url);
            HttpResponse response = httpClient.execute(request);

            String responseBody = EntityUtils.toString(response.getEntity());
            JSONObject jsonObject = JSONObject.parseObject(responseBody);
            log.info("接口调用结果-----------：" + jsonObject.toString());
            JSONArray jsonArray = jsonObject.getJSONArray("data");
            Set<String> appNameList = new HashSet<>();
            for (int i = 0; i < jsonArray.size(); i++) {
                appNameList.add(jsonArray.getJSONObject(i).get("appName").toString());
            }
            if (!appNameList.contains(appName)) {
                log.info("当前appName不存在，开始创建");
                //创建appName
                String url1="http://" + serverAddress + "/appInfo/save";
                String json = "{\"appName\":\""+appName+"\",\"password\":\""+password+"\"}";
                HttpPost httpPost = new HttpPost(url1);
                StringEntity entity = new StringEntity(json, "UTF-8");
                entity.setContentType("application/json");
                httpPost.setEntity(entity);
                HttpResponse response1 = httpClient.execute(httpPost);
                String responseBody1 = EntityUtils.toString(response1.getEntity());
                log.info("appName创建完成，接口调用结果：---------------" + responseBody1);
//                log.info("开始创建BuildTriggerConfigPollingProcessor任务");
//                // 初始化 client，需要server地址和应用名称作为参数
//                PowerJobClient powerJobClient = new PowerJobClient(serverAddress, appName, password);
//                SaveJobInfoRequest newJobInfo = new SaveJobInfoRequest();
//                //jobId，任务 ID，可选，null 代表创建任务，否则填写需要修改的任务 ID
//                newJobInfo.setId(null);
//                //jobName，任务名称
//                newJobInfo.setJobName("BuildTriggerConfigPollingJob");
//                //jobDescription，任务描述
//                newJobInfo.setJobDescription("");
//                //jobParams，任务参数，Processor#process 方法入参TaskContext对象的jobParams字段
//                newJobInfo.setJobParams("");
//                //timeExpressionType，时间表达式类型，枚举值
//                newJobInfo.setTimeExpressionType(TimeExpressionType.CRON);
//                //timeExpression，时间表达式，填写类型由 timeExpressionType 决定，比如 CRON 需要填写 CRON 表达式
//                newJobInfo.setTimeExpression("0 */5 * * * ?");
//                //executeType执行配置，单机执行STANDALONE
//                newJobInfo.setExecuteType(ExecuteType.STANDALONE);
//                //processorType处理器类型，内建BUILT_IN
//                newJobInfo.setProcessorType(ProcessorType.BUILT_IN);
//                //processorInfo全限定类名
//                newJobInfo.setProcessorInfo("com.cmcc.cmdevops.ci.job.processor.BuildTriggerConfigPollingProcessor");
//                //designatedWorkers指定机器执行，设置该参数后只有列表中的机器允许执行该任务，空代表不指定机器
//                newJobInfo.setDesignatedWorkers("");
//                //minCpuCores,最小可用 CPU 核心数，CPU 可用核心数小于该值的 Worker 将不会执行该任务，0 代表无任何限制
//                newJobInfo.setMinCpuCores(0);
//                //minMemorySpace,最小内存大小（GB），可用内存小于该值的Worker 将不会执行该任务，0 代表无任何限制
//                newJobInfo.setMinMemorySpace(0);
//                //minDiskSpace，最小磁盘大小（GB），可用磁盘空间小于该值的Worker 将不会执行该任务，0 代表无任何限制
//                newJobInfo.setMinDiskSpace(0);
//                //是否启用该任务，未启用的任务不会被调度
//                newJobInfo.setEnable(true);
//                ResultDTO<Long> resultDTO = powerJobClient.saveJob(newJobInfo);
//                log.info("BuildTriggerConfigPollingProcessor任务创建接口返回信息----------" + resultDTO.toString());
//                if (resultDTO.isSuccess()) {
//                    log.info("BuildTriggerConfigPollingProcessor任务创建成功！！！");
//                }
            }else{
                log.info("------------当前appName已存在，跳过初始化步骤-----------");
            }
            log.info("powerjob初始化任务完成");
        } catch (Exception e) {
            log.error("powerjob初始化任务失败", e);
        }
    }
}
