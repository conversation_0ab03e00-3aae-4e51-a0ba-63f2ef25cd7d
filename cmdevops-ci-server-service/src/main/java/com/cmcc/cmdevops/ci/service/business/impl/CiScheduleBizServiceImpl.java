package com.cmcc.cmdevops.ci.service.business.impl;


import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.cmcc.cmdevops.ci.service.atom.BuildAdvancedConfigAtomService;
import com.cmcc.cmdevops.ci.service.atom.BuildTaskAtomService;
import com.cmcc.cmdevops.ci.service.atom.impl.BuildTaskAtomServiceImpl;
import com.cmcc.cmdevops.ci.service.bo.*;
import com.cmcc.cmdevops.ci.service.business.*;
import com.cmcc.cmdevops.ci.service.dao.BuildTaskDO;
import com.cmcc.cmdevops.exception.BusinessException;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.errors.*;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
public class CiScheduleBizServiceImpl implements CiScheduleBizService {

    static final String SUCCESS = "SUCCESS";
    static final String FAILURE = "FAILURE";

    private final String CREATE_NODE = "createNode";
    private final String START = "start";
    private final String STOP = "stop";
    private final String DELETE = "delete";
    private final String DETAIL = "detail";
    private final String STATUS = "status";
    private final String STEPS = "steps";
    private final String STEPS_LOG = "stepsLog";

    private final String ARTIFACT = "artifact";

    private final String CLEAN_CACHE = "cleanCache";

    @Resource
    private BuildScheduleSnapshotBizService buildScheduleSnapshotBizService;

    @Resource
    private BuildArtifactBizServiceImpl buildArtifactBizService;

    @Resource
    private BuildSnapshotBizServiceImpl buildSnapshotBizService;

    @Resource
    private BuildStepSnapshotBizServiceImpl buildStepSnapshotBizService;

    @Resource
    private BuildTaskAtomService buildTaskAtomService;

    @Resource
    private BuildAdvancedConfigBizService buildAdvancedConfigBizService;

    @Resource
    private BuildNodeBizService buildNodeBizService;
    @Resource
    private BuildEnvironmentBizService buildEnvironmentBizService;

    @Override
    public String startTask(CiScheduleRequest ciScheduleRequest) {
//        this.sendMessage(ciScheduleRequest, CREATE_NODE);
        this.sendMessage(ciScheduleRequest, START);
        return "xx";
    }

    @Override
    public String stopTask(String jobId) {
        BuildScheduleSnapshotBO buildScheduleSnapshotBO = buildScheduleSnapshotBizService.getById(jobId);
        CiScheduleRequest ciScheduleRequest = new CiScheduleRequest();
        ciScheduleRequest.setBuildTaskDTO(buildScheduleSnapshotBO);
        this.sendMessage(ciScheduleRequest, STOP);
        return null;
    }

    @Override
    public String deleteTask(String jobId) {
        BuildScheduleSnapshotBO buildScheduleSnapshotBO = buildScheduleSnapshotBizService.getById(jobId);
        CiScheduleRequest ciScheduleRequest = new CiScheduleRequest();
        ciScheduleRequest.setBuildTaskDTO(buildScheduleSnapshotBO);
        this.sendMessage(ciScheduleRequest, DELETE);
        return null;
    }

    @Override
    public JSONArray queryLog(String jobId) {
        JSONArray stages = this.queryStage(jobId);
        for (int i = 0; i < stages.size(); i++) {
            JSONObject stage = stages.getJSONObject(i);
            String stageNum = stage.getString("id");
            String log = queryStageLog(jobId, stageNum);
            stage.put("stageLog", log);
        }
        return stages;
    }

    @Override
    public JSONArray queryStage(String jobId) {
        BuildScheduleSnapshotBO buildScheduleSnapshotBO = buildScheduleSnapshotBizService.getById(jobId);
        CiScheduleRequest ciScheduleRequest = new CiScheduleRequest();
        ciScheduleRequest.setBuildTaskDTO(buildScheduleSnapshotBO);
        return this.sendMessage(ciScheduleRequest, STEPS).getJSONArray("data");
    }

    @Override
    public String queryStageLog(String jobId, String stageNum) {
        BuildScheduleSnapshotBO buildScheduleSnapshotBO = buildScheduleSnapshotBizService.getById(jobId);
        buildScheduleSnapshotBO.setStageNum(stageNum);
        CiScheduleRequest ciScheduleRequest = new CiScheduleRequest();
        ciScheduleRequest.setBuildTaskDTO(buildScheduleSnapshotBO);
        return this.sendMessage(ciScheduleRequest, STEPS_LOG).getString("data");
    }


    @Override
    public JSONObject getStatus(String jobId) {
        BuildScheduleSnapshotBO buildScheduleSnapshotBO = buildScheduleSnapshotBizService.getById(jobId);
        CiScheduleRequest ciScheduleRequest = new CiScheduleRequest();
        ciScheduleRequest.setBuildTaskDTO(buildScheduleSnapshotBO);
        return this.sendMessage(ciScheduleRequest, STATUS).getJSONObject("data");
    }

    @Override
    public JSONObject getArtifact(String jobId) {
        BuildScheduleSnapshotBO buildScheduleSnapshotBO = buildScheduleSnapshotBizService.getById(jobId);
        CiScheduleRequest ciScheduleRequest = new CiScheduleRequest();
        ciScheduleRequest.setBuildTaskDTO(buildScheduleSnapshotBO);
        return this.sendMessage(ciScheduleRequest, ARTIFACT).getJSONObject("data");
    }

    @Override
    public void cleanBuildCache(String taskId, String jobId) {
        BuildScheduleSnapshotBO buildScheduleSnapshotBO = buildScheduleSnapshotBizService.getById(jobId);
        buildScheduleSnapshotBO.setCacheId(taskId);
        CiScheduleRequest ciScheduleRequest = new CiScheduleRequest();
        ciScheduleRequest.setBuildTaskDTO(buildScheduleSnapshotBO);
        BuildTaskDO buildTaskDO = buildTaskAtomService.getById(taskId);
        //BuildAdvancedConfigBO buildAdvancedConfigBO = buildAdvancedConfigBizService.getBuildAdvancedConfigByTaskId(taskId);
        Integer assignedNode = buildTaskDO.getAssignedNode();
        BuildNodeBO buildNodeBO = buildNodeBizService.getBuildNodeById(assignedNode);
        BuildEnvironmentBO buildEnvironmentBO = buildEnvironmentBizService.detail(buildNodeBO.getBuildEnvironmentId());
        buildScheduleSnapshotBO.setS3Endpoint(buildEnvironmentBO.getCacheDir());
        buildScheduleSnapshotBO.setAccessKey(buildEnvironmentBO.getAccessKey());
        buildScheduleSnapshotBO.setSecretKey(buildEnvironmentBO.getSecretKey());
        this.sendMessage(ciScheduleRequest, CLEAN_CACHE).getJSONObject("data");
    }


    private JSONObject sendMessage(CiScheduleRequest ciScheduleRequest, String queryType) {
        OkHttpClient client = new OkHttpClient();
        log.info("ciScheduleRequest:{}", JSONObject.toJSONString(ciScheduleRequest));
        Request request = new Request.Builder()
                .url(ciScheduleRequest.getBuildTaskDTO().getCiScheduleUrl() + "/cmdevops-ci/schedule-server/api/v1/buildTask/" + queryType)
                .post(RequestBody.create(MediaType.parse("application/json"), JSONObject.toJSONString(ciScheduleRequest)))
                .build();
        try {
            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                log.info("Response: {}", responseBody);
                return JSONObject.parseObject(responseBody);
            } else {
                log.error("Response failed: {}", response.message());
            }
            response.close();
        } catch (Exception e) {
            log.error("Exception occurred during parsing response", e);
            throw new BusinessException("调度服务异常");
        }
        return new JSONObject();
    }


}
