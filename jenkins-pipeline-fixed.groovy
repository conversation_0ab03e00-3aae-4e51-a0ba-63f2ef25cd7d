package com.cmcc.cmdevops.ci.service.business.impl.buildStrategy

def strictShNoOut(String cmd) {
    sh(script: "#!/bin/bash -e\n${cmd} > /dev/null 2>&1")
}
def strictSh(String cmd) {
    sh(script: "#!/bin/bash -e\n${cmd}", returnStdout: false)
}
def noOutputSh(cmd) {
    return sh (script: '#!/bin/sh -e\n' + cmd, returnStatus: true)
}
def metadata = [artifacts: []]

pipeline {
    agent {
        kubernetes {
            yaml '''
apiVersion: v1
kind: Pod
metadata:
  name: jenkins-slave
  labels:
    app: "jenkins-slave"
spec:
  hostAliases:
    - ip: "**************"
      hostnames:
        - "sgyf-luchi-pod1-core-157-199"
    - ip: "**************"
      hostnames:
        - "sgyf-luchi-pod1-core-157-200"
  containers:
    - name: jnlp
      image: ***********:20200/devops/jenkins-slave:v2
      imagePullPolicy: Always
      resources:
        limits:
          cpu: '8'
          memory: 8Gi
        requests:
          cpu: 400m
          memory: 2048Mi
      securityContext:
        runAsUser: 0
        privileged: true
      volumeMounts:
        - name: config
          mountPath: /etc/buildkitd.toml
          subPath: buildkitd.toml
        - name: config
          mountPath: /etc/docker/daemon.json
          subPath: daemon.json
  volumes:
    - name: config
      configMap:
        defaultMode: 420
        items:
          - key: buildkitd.toml
            path: buildkitd.toml
          - key: daemon.json
            path: daemon.json
        name: inbound-agent-config
'''
        }
    }
    options { 
        timeout (10) 
    } 
    stages {
        stage('1-代码检出') {
            steps {
                checkout([
                    $class: 'GitSCM',
                    branches: [[name: env.GIT_BUILD_REF]],
                    userRemoteConfigs: [[
                        url: env.GIT_REPO_URL,
                        credentialsId: '408eb7caeb454fe2b5fd42cedbb82abc'
                    ]]
                ])
            }
        }
        stage('2-下载构建依赖缓存') { 
            steps { 
                echo '下载构建缓存'
                withEnv(['AWS_ACCESS_KEY_ID=8OoR4PMcv8227GdT', 'AWS_SECRET_ACCESS_KEY=FAuY6Ln3Vn7oiAks1UnujK1QjDVyk7kl']) {
                    script {
                        noOutputSh('mkdir -p /root/.ccache /root/.cache /go/pkg/mod /root/.yarn /root/.npm /root/.gradle/caches /root/.m2')
                        def exists = sh(script: 'aws s3 ls s3://build-cache/2a17899739e5401e99fb174306b936ed/dependency-cache/cache.tar.gz --endpoint-url http://************:9000 > /dev/null 2>&1', returnStatus: true) == 0
                        if (exists) {
                            noOutputSh('aws s3 cp s3://build-cache/2a17899739e5401e99fb174306b936ed/dependency-cache/cache.tar.gz . --endpoint-url http://************:9000  --no-progress')
                            if(fileExists('cache.tar.gz')) {
                                noOutputSh('tar zxf cache.tar.gz -C /; rm cache.tar.gz')
                            }
                        }
                    }
                } 
            } 
        } 
        stage('icecream分布式集群环境初始化-修复版') { 
            steps { 
                script {
                    echo '生成修复后的Docker Compose配置文件...'
                    // 获取宿主机IP地址 - 兼容BusyBox环境
                    def hostIP = ""
                    try {
                        // 方法1: 尝试使用hostname -I (如果支持)
                        hostIP = sh(script: "hostname -I 2>/dev/null | awk '{print \$1}' || echo ''", returnStdout: true).trim()
                        if (hostIP == "") {
                            // 方法2: 使用ip命令获取默认路由接口的IP (BusyBox兼容)
                            hostIP = sh(script: "ip route get ******* 2>/dev/null | grep 'src' | awk '{for(i=1;i<=NF;i++) if(\$i==\"src\") print \$(i+1)}' | head -1 || echo ''", returnStdout: true).trim()
                        }
                        if (hostIP == "") {
                            // 方法3: 使用ifconfig获取第一个非回环接口的IP
                            hostIP = sh(script: "ifconfig 2>/dev/null | grep 'inet ' | grep -v '127.0.0.1' | head -1 | awk '{print \$2}' | cut -d: -f2 || echo ''", returnStdout: true).trim()
                        }
                        if (hostIP == "") {
                            // 方法4: 从/proc/net/route获取默认网关接口，然后获取IP
                            hostIP = sh(script: """
                                DEFAULT_IFACE=\$(awk '\$2 == "00000000" { print \$1; exit }' /proc/net/route 2>/dev/null)
                                if [ -n "\$DEFAULT_IFACE" ]; then
                                    ip addr show \$DEFAULT_IFACE 2>/dev/null | grep 'inet ' | head -1 | awk '{print \$2}' | cut -d/ -f1 || echo ''
                                else
                                    echo ''
                                fi
                            """, returnStdout: true).trim()
                        }
                        if (hostIP == "") {
                            // 方法5: 最后的回退方案，使用容器内部可见的网关IP
                            hostIP = sh(script: "route -n 2>/dev/null | awk '\$1==\"0.0.0.0\" {print \$2; exit}' || echo '**********'", returnStdout: true).trim()
                        }
                    } catch (Exception e) {
                        echo "获取IP地址时发生异常: ${e.getMessage()}"
                        hostIP = "**********"  // 默认Docker网关IP
                    }

                    // 验证IP地址格式
                    if (!hostIP.matches(/^\d+\.\d+\.\d+\.\d+$/)) {
                        echo "警告: 获取到的IP地址格式不正确: '${hostIP}'"
                        hostIP = "**********"  // 使用默认值
                    }

                    echo "检测到宿主机IP: ${hostIP}"
                    
                    // 生成简化的Docker Compose配置
                    def currentTime = new Date().toString()
                    def composeContent = """# icecream分布式编译环境配置 - 简化版本
# 生成时间: ${currentTime}
# 主机IP: ${hostIP}
# 网络模式: host

services:
  # icecream调度器节点
  icecc-scheduler:
    image: ***********:20200/devops-test/icecream-centos7:20250930
    container_name: icecc-scheduler-408eb7caeb454fe2b5fd42cedbb82abc
    hostname: icecc-scheduler-408eb7caeb454fe2b5fd42cedbb82abc
    network_mode: host
    environment:
      - HOST_IP=${hostIP}
    command: ["icecc-scheduler", "-u", "nobody", "-p", "8765", "-l", "/tmp/icecc-scheduler.log", "-vvv", "--persistent-client-connection"]
    restart: unless-stopped

  # icecream工作节点1 - 启动iceccd守护进程，使用不同端口避免冲突
  icecc-worker-1:
    image: ***********:20200/devops-test/icecream-centos7:20250930
    container_name: icecc-worker-1-408eb7caeb454fe2b5fd42cedbb82abc
    hostname: icecc-worker-1-408eb7caeb454fe2b5fd42cedbb82abc
    network_mode: host
    depends_on:
      - icecc-scheduler
    environment:
      - HOST_IP=${hostIP}
      - SCHEDULER_ADDR=${hostIP}:8765
    command: ["sh", "-c", "sleep 10 && iceccd -u nobody -d -s ${hostIP}:8765 -p 10245 -l /tmp/icecc-worker1.log -vvv && tail -f /dev/null"]
    restart: unless-stopped

  # icecream工作节点2 - 启动iceccd守护进程，使用不同端口避免冲突
  icecc-worker-2:
    image: ***********:20200/devops-test/icecream-centos7:20250930
    container_name: icecc-worker-2-408eb7caeb454fe2b5fd42cedbb82abc
    hostname: icecc-worker-2-408eb7caeb454fe2b5fd42cedbb82abc
    network_mode: host
    depends_on:
      - icecc-scheduler
    environment:
      - HOST_IP=${hostIP}
      - SCHEDULER_ADDR=${hostIP}:8765
    command: ["sh", "-c", "sleep 15 && iceccd -u nobody -d -s ${hostIP}:8765 -p 10246 -l /tmp/icecc-worker2.log -vvv && tail -f /dev/null"]
    restart: unless-stopped"""

                    writeFile encoding: 'UTF-8', file: './tmp/docker-compose.yml', text: composeContent
                    
                    // 验证IP地址有效性
                    if (hostIP == "" || hostIP == "**********") {
                        echo "⚠ 警告: 使用默认或回退IP地址: ${hostIP}"
                        echo "这可能导致网络连接问题，请检查网络配置"
                    }

                    echo '开始执行Docker Compose操作...'
                    sh '''docker-compose -f ./tmp/docker-compose.yml up -d'''
                    echo 'Docker Compose操作完成'

                    // 等待调度器启动并验证连接
                    echo '等待调度器启动...'
                    sleep 20  // 增加等待时间

                    // 检查容器状态
                    echo "检查icecream容器状态:"
                    sh '''
                        echo "=== Docker容器状态 ==="
                        docker ps | grep icecc || echo "无icecream容器运行"
                        echo ""

                        echo "=== 调度器容器日志 (最新10行) ==="
                        docker logs --tail 10 icecc-scheduler-408eb7caeb454fe2b5fd42cedbb82abc 2>/dev/null || echo "无法获取调度器日志"
                        echo ""
                    '''

                    // 验证调度器是否正常启动
                    def schedulerStatus = sh(script: "netstat -ln | grep :8765 || echo 'NOT_FOUND'", returnStdout: true).trim()
                    if (schedulerStatus.contains('8765')) {
                        echo "✓ 调度器已成功启动并监听端口8765"

                        // 多次验证调度器连接
                        def connectionSuccess = false
                        for (int attempt = 1; attempt <= 5; attempt++) {
                            echo "调度器连接测试 (第${attempt}/5次)..."
                            def connectionTest = sh(script: "timeout 5 bash -c '</dev/tcp/${hostIP}/8765' 2>/dev/null && echo 'CONNECTED' || echo 'FAILED'", returnStdout: true).trim()
                            if (connectionTest == 'CONNECTED') {
                                echo "✓ 调度器连接测试成功"
                                connectionSuccess = true
                                break
                            } else {
                                echo "⚠ 调度器连接测试失败，等待3秒后重试..."
                                sleep 3
                            }
                        }

                        if (!connectionSuccess) {
                            echo "❌ 调度器连接测试最终失败"
                        }
                    } else {
                        echo "⚠ 警告: 调度器可能未正常启动"

                        // 显示详细的网络诊断信息
                        echo "网络诊断信息:"
                        sh """
                            echo "主机IP: ${hostIP}"
                            echo "监听端口:"
                            netstat -ln | grep :8765 || echo "端口8765未监听"
                            echo ""
                            echo "所有监听端口:"
                            netstat -ln | head -10
                            echo ""
                            echo "Docker容器详细状态:"
                            docker ps -a | grep icecc || echo "无icecream容器"
                            echo ""
                            echo "调度器容器详细日志:"
                            docker logs icecc-scheduler-408eb7caeb454fe2b5fd42cedbb82abc 2>/dev/null || echo "无法获取调度器日志"
                        """
                    }
                } 
            } 
        } 
        stage('CMake构建-修复版') { 
            agent {
                docker {
                    image '***********:20200/devops-test/cmake-ccache-icecc:cmake3.10.1-gcc5.5.0'
                    args '''  -v ${WORKSPACE}:/workspace -w /workspace \
                             --network host \
                             -e USE_DISTRIBUTED_BUILD=true \
                             -v /root/.ccache:/root/.ccache \
                             -v /root/.cache:/root/.cache \
                             -v /go/pkg/mod:/go/pkg/mod \
                             -v /root/.yarn:/root/.yarn \
                             -v /root/.npm:/root/.npm \
                             -v /root/.gradle/caches:/root/.gradle/caches \
                             -v /root/.m2:/root/.m2'''
                    reuseNode true
                }
            }
            steps { 
                script {
                    // 获取宿主机IP地址
                    def hostIP = sh(script: "hostname -I | awk '{print \$1}'", returnStdout: true).trim()
                    echo "使用调度器地址: ${hostIP}:8765"
                    
                    // 设置环境变量并执行构建 - 让jenkins-build-init.sh自动处理分布式编译配置
                    withEnv([
                        "USE_DISTRIBUTED_BUILD=true",
                        "ICECC_SCHEDULER=${hostIP}:8765"
                    ]) {
                        sh '''#!/bin/bash
set -e
echo "=== 构建环境信息 ==="
echo "USE_DISTRIBUTED_BUILD: $USE_DISTRIBUTED_BUILD"
echo "ICECC_SCHEDULER: $ICECC_SCHEDULER"
echo ""

# 使用jenkins-build-init.sh执行完整的构建流程
echo "=== 使用jenkins-build-init.sh执行构建 ==="
/usr/local/bin/jenkins-build-init.sh "rm -rf build && mkdir build && cd build && cmake -G 'Unix Makefiles' -DCMAKE_CXX_STANDARD=11 -DCMAKE_CXX_FLAGS='-std=c++11' -DCMAKE_EXE_LINKER_FLAGS='-lstdc++' -DCMAKE_SHARED_LINKER_FLAGS='-lstdc++' .. && make -j\$(nproc) VERBOSE=1"
'''
                    }
                }
            } 
        } 
        stage('4-上传构建依赖缓存') { 
            steps { 
                echo '上传构建缓存' 
                dir('/') {
                    noOutputSh('tar zcf cache.tar.gz root/.ccache root/.cache go/pkg/mod root/.yarn root/.npm root/.gradle/caches root/.m2 ')
                    withEnv(['AWS_ACCESS_KEY_ID=8OoR4PMcv8227GdT', 'AWS_SECRET_ACCESS_KEY=FAuY6Ln3Vn7oiAks1UnujK1QjDVyk7kl']) {
                        noOutputSh('aws s3 cp cache.tar.gz s3://build-cache/2a17899739e5401e99fb174306b936ed/dependency-cache/cache.tar.gz --endpoint-url http://************:9000 --no-progress')
                    }
                }
            } 
        } 
    }
    post {
        always {
            script {
                // 清理icecream容器
                sh '''
                    docker-compose -f ./tmp/docker-compose.yml down || echo "清理容器失败"
                    docker container prune -f || echo "清理容器失败"
                '''
                
                // 在 post 部分将元数据写入文件并归档
                writeFile file: 'metadata.json', text: groovy.json.JsonOutput.toJson(metadata)
                // 将文件保存为 Artifact
                archiveArtifacts artifacts: 'metadata.json'
            }
        }
    }
}
